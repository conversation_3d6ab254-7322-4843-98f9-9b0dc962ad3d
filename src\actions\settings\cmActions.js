import { action, createRequestTypes } from '../base';

export const cmActionTypes = {
  GET_CM: createRequestTypes('GET_CM'),
  CREATE_CM: createRequestTypes('CREATE_CM'),
  UPDATE_CM: createRequestTypes('UPDATE_CM'),
  DELETE_CM: createRequestTypes('DELETE_CM'),
};

/**
 *
 * @param payload
 * @param orgId
 * @param entityType
 * @param entityId
 * @param limit
 * @param page
 * @param callback
 */
class CmActions {
  /**
   * @param {*} orgId
   * @param {*} entityType
   * @param {*} cmId
   * @param {*} page
   * @param {*} limit
   * @return
   */
  getCM = (orgId, entityId, entityType, page, limit, tenantId, actionType, isActive, isArchived, callback) => action(cmActionTypes.GET_CM.REQUEST, {
    orgId, entityId, entityType, page, limit, tenantId, actionType, isActive, isArchived, callback,
  })

  /**
   *
   * @param {*} getCMLoading
   * @param {*} entityType
   * @return
   */
  getCMLoading = (getCMLoading, entityType) => action(cmActionTypes.GET_CM.LOADING, {
    getCMLoading,
    entityType,
  })

  /**
   * @return
   * @param customMessages
   * @param entityType
   */
  getCMSuccess = (customMessages, entityType) => action(cmActionTypes.GET_CM.SUCCESS, {
    customMessages, entityType,
  })

  /**
   *
   * @param {*} payload
   * @param {*} callback
   * @return
   */
  createCM = (payload, callback) => action(cmActionTypes.CREATE_CM.REQUEST, {
    payload,
    callback,
  })

  /**
   *
   * @param {*} createCMLoading
   * @return
   */
  createCMLoading = (createCMLoading) => action(cmActionTypes.CREATE_CM.LOADING, {
    createCMLoading,
  })

  /**
   *
   * @param {*} payload
   * @param {*} callback
   * @return
   */
  updateCM = (payload, callback) => action(cmActionTypes.UPDATE_CM.REQUEST, {
    payload,
    callback,
  })

  /**
   *
   * @param {*} updateCMLoading
   * @return
   */
  updateCMLoading = (updateCMLoading) => action(cmActionTypes.UPDATE_CM.LOADING, {
    updateCMLoading,
  })

  /**
   * @param payload
   * @param callback
   * @return {{type: *}}
   */
  deleteCM = (payload, callback) => action(cmActionTypes.DELETE_CM.REQUEST, {
    payload, callback,
  })

  /**
   * @param deleteCMLoading
   * @return {{type: *}}
   */
  deleteCMLoading = (deleteCMLoading) => action(cmActionTypes.DELETE_CM.LOADING, {
    deleteCMLoading,
  })
}

export default new CmActions();
