/* eslint-disable no-unused-expressions */
/* eslint-disable no-param-reassign */
/* eslint-disable no-nested-ternary */
/* eslint-disable new-cap */
import React, { Component, Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import H3Button, { defaultButtonTypes } from '@Uilib/h3Button';
import {
  Alert, Progress, Table,
} from 'antd';
import {
} from '@ant-design/icons';
import Helpers from '@Apis/helpers';
import H3FormInput from '@Uilib/h3FormInput';
import MOActions from '@Actions/moActions';
import { v4 as uuidv4 } from 'uuid';
import RawMaterialBatchSelector from '../UpdateRawMaterial/RawMaterialBatchSelector';
import './style.scss';
import { INFINITE_EXPIRY_DATE, QUANTITY } from '@Apis/constants';
import H3Text from '@Uilib/h3Text';
import FormHelpers from '@Helpers/FormHelpers';
import dayjs from 'dayjs';
import ProductCategoryLabel from '../../../../../Common/ProductCategoryLabel';
import ProductCodesAndName from '@Components/Common/ProductCodesAndName';

/**
 *
 */
class UpdateRawMaterialV2 extends Component {
  /**
   *
   * @param props
   */
  constructor(props) {
    super(props);
    this.state = {
      rawMaterialColumns: [
        {
          title: 'PRODUCT',
          width: '200px',
          render: (item) => {
            const enableInternalRefCode = this.props.user?.tenant_info?.inventory_config?.settings?.enable_internal_ref_code;
            const enableInternalSKUCode = this.props.user?.tenant_info?.inventory_config?.settings?.enable_internal_sku_code;
            return (
              <Fragment>
                <ProductCodesAndName
                  skuCode={item?.tenant_product_info?.internal_sku_code}
                  refCode={item?.tenant_product_info?.ref_product_code}
                  name={item.tenant_product_info?.product_sku_name !== 'custom' ? ` ${item?.tenant_product_info?.product_sku_name}` : ''}
                  showSku={enableInternalSKUCode}
                  showRefCode={enableInternalRefCode}
                />
                {item?.product_category_info?.category_path?.length > 0 && (
                  <ProductCategoryLabel
                    categoryPath={item?.product_category_info?.category_path}
                    categoryName={item?.product_category_info?.category_path?.at(-1)}
                    containerStyle={{
                      width: 'fit-content',
                    }}
                  />
                )}
              </Fragment>
            );
          },
        },
        {
          title: 'STOCK USAGE',
          render: (record) => (
            <Fragment>
              <Progress
                percent={parseInt((Math.abs(record?.issued_quantity) / record?.est_quantity) * 100)}
                status="active"
                size="small"
                strokeColor={{ from: '#108ee9', to: '#87d068' }}
                trailColor="#2d7df71a"
                strokeLinecap="round"
                strokeWidth={6}
              />
              <div>
                <H3Text text={`Estimated - ${QUANTITY(record?.est_quantity + record?.wastage_quantity || 0, record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase()}`} />
                <H3Text text={`Used - ${QUANTITY(Math.abs(record?.issued_quantity), record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase()}`} />
              </div>
            </Fragment>
          ),
          onCell: (item) => {
            if (item?.fg_product) {
              return { colSpan: 0 };
            }
            return {};
          },
        },
        {
          title: 'PENDING',
          render: (item) => (
            <Fragment>
              {`${QUANTITY(item?.est_quantity + Number(item?.wastage_quantity) + item?.issued_quantity, item?.uom_info?.precision)} ${item?.uom_info?.uqc?.toProperCase()}`}
            </Fragment>
          ),
        },
        {
          title: 'AVAILABLE STOCK',
          render: (item) => `${QUANTITY(item?.tenant_product_info?.total_available_batches_quantity, item?.uom_info?.precision)} ${item?.uom_info?.uqc?.toProperCase()}`,
        },
        {
          title: 'ISSUED',
          width: '180px',
          render: (record) => {
            const { moLines } = this.state;
            return (
              <Fragment>
                {!record?.tenant_product_info?.product_batches?.filter((item) => item.available_qty > 0)?.length > 0 ? <div className="input-error">OUT OF STOCK</div> : (
                  <Fragment>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <H3FormInput
                        value={record.quantity}
                        type="number"
                        labelClassName="orgFormLabel"
                        inputClassName="orgFormInput"
                        name="quantity is more than available quantity"
                        onChange={(e) => {
                          let remainingQty = Number(e.target.value) * (1 + (Number(record?.bom_line_info?.wastage_percentage) / 100));
                          const batches = record?.tenant_product_info?.product_batches;
                          const copyData = JSON.parse(JSON.stringify(moLines));
                          for (let i = 0; i < batches?.length; i++) {
                            batches[i].consumed_qty = 0;
                          }
                          for (let i = 0; i < batches?.length; i++) {
                            if (batches[i]?.batch_in_use) {
                              if (remainingQty > 0 && Number(batches[i].consumed_qty || '0') < batches?.[i]?.available_qty) {
                                batches[i].consumed_qty = batches?.[i]?.available_qty > remainingQty ? remainingQty : batches?.[i]?.available_qty;
                                remainingQty -= batches[i].consumed_qty;
                              } else {
                                batches[i].consumed_qty = 0;
                                remainingQty -= batches[i].consumed_qty;
                              }
                            }
                          }
                          copyData.map((item) => {
                            if (item.key === record.key) {
                              item.quantity = e.target.value;
                              item.wastage = QUANTITY(Number(e.target.value) * (Number(record?.bom_line_info?.wastage_percentage) / 100), record?.uom_info?.precision);
                              item.tenant_product_info.product_batches = batches;
                            }
                            return moLines;
                          });
                          this.setState({ moLines: copyData });
                        }}
                      />
                      <div style={{ marginLeft: '6px' }}>
                        {record?.uom_info?.uqc?.toProperCase()}
                      </div>
                    </div>
                    {Number(record?.quantity) > record?.tenant_product_info?.total_available_batches_quantity ? <H3Text className="input-error" text="Sufficient stock is not available" /> : ''}
                  </Fragment>
                )}
              </Fragment>
            );
          },
        },
        {
          title: 'OVERAGE', // -> Wastage
          width: '100px',
          render: (record) => {
            const { moLines } = this.state;
            return (
              <Fragment>
                {!record?.tenant_product_info?.product_batches?.filter((item) => item.available_qty > 0)?.length > 0 ? <div className="input-error">OUT OF STOCK</div> : (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <H3FormInput
                      value={record.wastage}
                      type="number"
                      labelClassName="orgFormLabel"
                      inputClassName="orgFormInput"
                      name="quantity is more than available quantity"
                      onChange={(e) => {
                        let remainingQty = Number(e.target.value) + Number(record.quantity);
                        const batches = record?.tenant_product_info?.product_batches;
                        const copyData = JSON.parse(JSON.stringify(moLines));
                        for (let i = 0; i < batches?.length; i++) {
                          batches[i].consumed_qty = 0;
                        }
                        for (let i = 0; i < batches?.length; i++) {
                          if (batches[i]?.batch_in_use) {
                            if (remainingQty > 0 && Number(batches[i].consumed_qty || '0') < batches?.[i]?.available_qty) {
                              batches[i].consumed_qty = batches?.[i]?.available_qty > remainingQty ? remainingQty : batches?.[i]?.available_qty;
                              remainingQty -= batches[i].consumed_qty;
                            } else {
                              batches[i].consumed_qty = 0;
                              remainingQty -= batches[i].consumed_qty;
                            }
                          }
                        }
                        copyData.map((item) => {
                          if (item.key === record.key) {
                            item.wastage = e.target.value;
                            item.tenant_product_info.product_batches = batches;
                          }
                          return moLines;
                        });
                        this.setState({ moLines: copyData });
                      }}
                    />
                    <div style={{ marginLeft: '6px' }}>
                      {record?.uom_info?.uqc?.toProperCase()}
                    </div>
                  </div>
                )}
                {record?.tenant_product_info?.product_batches?.map((item) => item.available_qty > 0)?.length === 0 && record?.batch_quantity > record?.tenant_product_info?.total_available_batches_quantity && <H3Text text="*Sufficient stock not available" className="input-error" />}
              </Fragment>
            );
          },
        },
      ],
    };
  }

  /**
   *
   */
  componentDidMount() {

  }

  /**
   *
   * @param props
   * @param state
   * @return {{isUserReady}|*|(*&{isUserReady: boolean})}
   */
  static getDerivedStateFromProps(props, state) {
    const { selectedMO, fgTenantProductId, rawMaterials } = props;
    if (selectedMO?.mo_id && !state.isUserReady) {
      const moLines = rawMaterials?.map((item) => ({
        ...item,
        key: uuidv4(),
        est_quantity: item.quantity,
        quantity: 0,
        available_quantity: item?.tenant_product_info?.total_available_batches_quantity,
        product_category_info: item?.tenant_product_info?.product_category_info || null,
        tenant_product_info: { ...item.tenant_product_info, product_batches: item.tenant_product_info.product_batches?.map((i) => ({ ...i, batch_in_use: true, available_qty: (i?.available_qty / item?.uom_info?.ratio) * item.tenant_product_info?.uom_info?.ratio })) },
        manufacturingDateFormat: item?.tenant_product_info?.manufacturing_date_format,
        expiryDateFormat: item?.tenant_product_info?.expiry_date_format,
        batchConsumptionMethod: Helpers.getBatchMethod(item?.tenant_product_info),
      }));
      return {
        ...state,
        isUserReady: true,
        moLines,
      };
    }
    return state;
  }

  /**
   *
   */
  componentWillUnmount() {
  }

  /**
   *
   * @param {*} data
   * @return
   */
  getDataSource(data) {
    return JSON.parse(JSON.stringify(data));
  }

  /**
   *
   */
  addRawMaterial = () => {
    const {
      moLines,
    } = this.state;
    const {
      selectedMO, createMOAdjustment, callback, isFlexible,
    } = this.props;
    if (this.isDataValid()) {
      const payload = [];
      moLines?.map((moLine) => {
        moLine.tenant_product_info.product_batches?.filter((item) => item?.consumed_qty > 0)?.map((item) => {
          payload.push({
            entity_type: 'RAW_MATERIAL',
            entity_id: moLine?.mo_line_id,
            quantity: -item?.consumed_qty,
            mo_id: selectedMO?.mo_id,
            status: 'ISSUED',
            batch_id: item?.batch_id,
            cost_price: item?.cost_price,
            selling_price: item?.selling_price,
            lot_number: item?.lot_number || '',
            custom_batch_number: item?.batch_number,
            expiry_date: item?.expiry_date ? FormHelpers.dateFormatter(item?.expiry_date, moLine?.expiryDateFormat) : null,
            wastage_quantity: moLine?.wastage,
          });
        });
      });
      if (payload?.length) {
        createMOAdjustment(payload, () => callback());
      }
    }
  }

  isDataValid() {
    const { moLines } = this.state;
    let isDataValid = true;
    for (let i = 0; i < moLines?.length; i++) {
      if (Number(moLines[i]?.quantity) > moLines[i]?.tenant_product_info?.total_available_batches_quantity || Number(moLines[i]?.quantity) < 0) isDataValid = false;
    }
    if (!moLines?.filter((item) => Number(item?.quantity) > 0)?.length) isDataValid = false;
    return isDataValid;
  }

  /**
   *
   * @param record
   * @param productBatchesRow
   */
  toggleBatch(record, productBatchesRow) {
    const { moLines } = this.state;
    for (let i = 0; i < moLines?.length; i++) {
      if (moLines[i]?.key === productBatchesRow?.key) {
        const rowBatches = moLines[i]?.tenant_product_info?.product_batches;
        for (let j = 0; j < rowBatches?.length; j++) {
          if (rowBatches[j]?.batch_id === record?.batch_id) {
            rowBatches[j].batch_in_use = !rowBatches[j]?.batch_in_use;
            moLines[i].quantity = 0;
            moLines[i].wastage = 0;
            rowBatches[j].consumed_qty = 0;
          }
        }
        break;
      }
    }
    this.setState({ moLines });
  }

  updateBatchValue(key, value, label, batchId) {
    const { moLines } = this.state;

    const copyData = JSON.parse(JSON.stringify(moLines));
    const updatedData = copyData.map((obj) => {
      if (obj.key === key) {
        let productBatches = obj?.tenant_product_info?.product_batches;
        productBatches = productBatches?.map((productBatch) => {
          if (productBatch.batch_id === batchId) {
            return {
              ...productBatch,
              [`${label}`]: value,
            };
          }
          return productBatch;
        });

        let qty = productBatches.reduce((sum, batch) => sum + (Number(batch.consumed_qty) || 0), 0);

        return {
          ...obj,
          tenant_product_info : {
            ...obj?.tenant_product_info, product_batches: productBatches,
          },
          quantity: qty,
        };
      }
      return obj;
    });

    this.setState({ moLines: updatedData });
  }

  rawMaterialStock() {
    const { moLines } = this.state;

    return moLines?.every((rm) =>
      rm?.tenant_product_info?.product_batches?.length > 0 &&
      rm.tenant_product_info.product_batches.some(batch => batch?.available_qty > 0)
    ) || false;
  }

  render() {
    const {
      user, createMOAdjustmentLoading, getMOByIdLoading,
    } = this.props;
    const { rawMaterialColumns, moLines } = this.state;

    const allowFlexibleConsumption = user?.tenant_info?.production_config?.sub_modules?.manufacturing_order?.settings?.allow_flexible_consumption_in_mo;
    return (

      <div className="ant-row">
        <div className="ant-col-md-24">
          <div className="rm_wrapper">
            {!allowFlexibleConsumption && (
              <div className="rm-info-wrapper">
                <Alert
                  message="Flexible consumption is not allowed for this manufacturing order. You cannot consume more than estimated raw material quantities."
                  type="info"
                  showIcon
                  className="rm-info-message"
                // closable
                />
              </div>
            )}
            <div className="rm-table-wrapper hide__in-mobile">
              <Table
                showHeader
                size="small"
                columns={rawMaterialColumns}
                dataSource={this.getDataSource(moLines) || []}
                loading={getMOByIdLoading || createMOAdjustmentLoading}
                pagination={false}
                rowClassName={(record) => (!record?.tenant_product_info?.product_batches?.map((item) => item.available_qty > 0)?.length ? 'disabled-row' : '')}
                expandable={{
                  rowExpandable: (record) => record?.tenant_product_info?.product_batches?.map((item) => item.available_qty > 0)?.length,
                  expandedRowRender: (record) => (
                    <RawMaterialBatchSelector
                      batches={record?.tenant_product_info?.product_batches}
                      uomInfo={record?.uom_info}
                      expiryDays={record?.tenant_product_info?.expiry_days}
                      itemsRow={record}
                      onToggleBatch={(batch, productBatchesRow) => this.toggleBatch(batch, productBatchesRow)}
                      updateBatchValue={(value, label, batchId) => this.updateBatchValue(record?.key, value, label, batchId)}
                      batchMethod={record?.batchConsumptionMethod}
                      updateBatchConsumptionMethod={(value) => {
                        const copyData = this.getDataSource(moLines);
                        const sortedData = copyData?.map((item) => {
                          if (record.key === item.key) {
                            item.batchConsumptionMethod = value;
                            item.tenant_product_info.product_batches = Helpers.batchSelectionMethod(value, item?.tenant_product_info?.product_batches, item?.quantity, false, null, item?.uom_info);
                          }
                          return {
                            ...item,
                          };
                        });
                        this.setState({ moLines: sortedData });
                      }}
                    />
                  ),
                }}
                scroll={{ x: true }}
                bordered
              />
            </div>
          </div>
        </div>
        <div className="custom-drawer__footer" style={{ width: '905px' }}>
          <div className="ant-col-md-6">
            <H3Button
              buttonType={defaultButtonTypes.BLUE_ROUNDED}
              text="Create"
              onClick={() => this.addRawMaterial()}
              isLoading={createMOAdjustmentLoading}
              disabled={createMOAdjustmentLoading || this.rawMaterialStock()}
              style={{
                width: '180px',
                borderRadius: '5px',
                padding: '7px 0px',
                marginLeft: '0',
              }}
            />
          </div>
        </div>
      </div>
    );
  }
}

const mapStateToProps = ({
  UserReducers, MOReducers,
}) => ({
  user: UserReducers.user,
  createMOAdjustmentLoading: MOReducers.createMOAdjustmentLoading,
  getMOByIdLoading: MOReducers.getMOByIdLoading,
});

const mapDispatchToProps = (dispatch) => ({
  createMOAdjustment: (payload, callback) => dispatch(MOActions.createMOAdjustment(payload, callback)),
});

UpdateRawMaterialV2.propTypes = {
  selectedMO: PropTypes.any,
  getMOByIdLoading: PropTypes.any,
  createMOAdjustmentLoading: PropTypes.any,
  createMOAdjustment: PropTypes.func,
  callback: PropTypes.any,
  isFlexible: PropTypes.bool,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(UpdateRawMaterialV2));
