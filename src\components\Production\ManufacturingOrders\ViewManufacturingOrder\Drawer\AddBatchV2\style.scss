.add_batch_wrapper {
  width: 100%;
  margin-bottom: 65px;

  .text-black {
    color: #000000 !important;
  }

  .vq__header {
    margin-top: 0px;
  }

  .vq__header-field-value {
    color: #2D7DF7;
    font-weight: 600;
  }

  .add-batch__label {
    color: #000000;
    margin: 8px 0px 6px 0px;
    font-weight: 500;
    font-size: 13px;
  }

  .add-batch-info-wrapper {
    margin-top: 17px;
    width: 100%;

    .ant-alert-message {
      color: #4F4F4F;
    }

    .add-batch-info-message {
      font-size: 12px !important;
      font-weight: 600;
    }

  }

  .ant-picker {
    width: 100%;
  }
}

.fg-batch-input {
  font-size: 12px !important;
  font-weight: 400;

  .ant-picker-input {
    input {
      font-size: 12px !important;
      font-weight: 400;
    }
  }
}

.fg-add-batch__product-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 0px;
  margin-top: 5px !important;
}

.fg-add-batch__product-details {
  font-weight: 600;
  font-size: 12px;
  margin-bottom: 10px;
  margin-top: 5px;
}

.ant-checkbox-wrapper+.ant-checkbox-wrapper {
  margin-left: 0px;
}

.rm-table-wrapper {
  margin-top: 20px;

  .ant-table {
    overflow: hidden;
    border: 1px solid rgba(15, 77, 171, 0.1) !important;
    .ant-table-tbody{
      .ant-table-cell{
        vertical-align: baseline;
      }
    }
    .ant-table-title {
      background-color: rgba(189, 214, 255, 0.5);
      font-size: 12px;
      color: #3f3f3f;
      font-weight: 600;
    }
  }

  // @media (max-width: 480px) {
  //   display: none;
  // }

  .ant-table-thead {
    .ant-table-cell {
      font-weight: 500;
      background-color: #f8f8f8;
      color: #5e5e5e;
    }

    .ant-table-cell::before {
      background-color: rgba(0, 0, 0, 0.1);
    }
  }

  .ant-table-cell {
    background-color: white;
  }

  .ant-table-tbody {
    border-bottom: none !important;

    .ant-table-row:last-child {
      .ant-table-cell {
        border-bottom: none !important;
      }
    }

    .ant-table-expanded-row {
      td {
        padding: 0px 21px 21px 21px;
      }
    }
  }

  .rm-table-header {
    font-weight: 600;
    font-size: 13px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .view-rm__action-button {
      display: flex;
      font-weight: 500;
      align-items: center;
      font-size: 13px;
      background-color: whitesmoke;
      border: 1px solid rgba(45, 124, 247, 0.5);
      border-radius: 3px;
      height: 32px;
      margin-top: -2px;
      margin-bottom: -2px;
      padding: 5px 10px 5px 10px;

      .view-rm__action-button-padding {
        padding: 8px 10px 8px 10px;
      }

      &:hover {
        cursor: pointer;
        background-color: rgba(45, 124, 247, 0.1);
      }

      a {
        color: #1F1F1F !important;
        padding-top: 3px !important;
        padding: 8px 10px 8px 10px
      }

      .anticon {
        font-size: 14px;
        margin-top: -2px;
      }
    }
  }
  .warehouse-location-selector{
    width: 110px;
  }
}

/* styles.css */

.rm-fg-table-wrapper {
  .ant-table {
    border: 1px solid rgba(40, 150, 32, 0.2) !important;
    position: relative;
    /* Ensure relative positioning for children */
  }

  .ant-table-scroll {
    overflow-x: auto;
    /* Allow horizontal scrolling */
    max-height: calc(100vh - 200px);
    /* Adjust height as needed */
  }

  .ant-table-fixed-right .ant-table-cell-last {
    position: sticky;
    right: 0;
    z-index: 2;
    background-color: white;
    /* Additional styles for making the last column fixed */
    width: 150px;
    /* Adjust the width as needed */
  }

  .ant-table-body {
    overflow-x: hidden !important;
    /* Hide horizontal overflow */
  }

  .ant-table-bordered .ant-table-thead>tr>th {
    border-right: none;
    /* Remove border for last column in the header */
  }

  .ant-table-bordered .ant-table-tbody>tr>td {
    border-right: none;
    /* Remove border for last column in the body */
  }
}

.add-batch__components {
  margin: -10px 0px 60px 0px !important;
  .trl-v2__wrapper-outer{
    margin-top: 30px;
    .ant-table{
      .ant-table-title{
        background-color: rgba(189, 214, 255, 0.5) !important;
      }
    }
    .trl-v2__table-footer{
      justify-content: flex-start;
    }
  }
  .trl_input-row{
    .trl_input-row-input, .select-vendor__wrapper .ant-select{
      width: 200px !important;
    }
  }
}

.add-batch__components-partition {
  margin-top: 25px;
}

.add-batch__components-title {}
