// React & Core Libraries
import React, { Component, Fragment } from 'react';
import { <PERSON>, withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';

// Third-Party Libraries
import { DatePicker, Table, Progress, notification } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';

// Constants
import { INFINITE_EXPIRY_DATE, QUANTITY, DEFAULT_CUR_ROUND_OFF } from '@Apis/constants';

// Custom UI Components
import H3Button, { defaultButtonTypes } from '@Uilib/h3Button';
import H3Text from '@Uilib/h3Text';
import H3FormInput from '@Uilib/h3FormInput';
import PRZModal from '../../../../../Common/UI/PRZModal';
import ProductCategoryLabel from '../../../../../Common/ProductCategoryLabel';
import WarehouseLocationSelector from '@Components/Common/Selector/WarehouseLocationSelector';

// Helpers
import CustomFieldHelpers from '../../../../../../helpers/CustomFieldHelpers';
import CustomFieldLine from '../../../../../Common/CustomFieldLine';

// Actions
import MOActions from '@Actions/moActions';

// Styles
import ProductCodesAndName from '@Components/Common/ProductCodesAndName';
import './style.scss';

/**
 *
 */
class ByProduct extends Component {
  /**
   *
   * @param props
   */
  constructor(props) {
    super(props);
    this.state = {};
  }

  getColumns () {

    const { cfV2DocManufacturingOrder, selectedMO, user} = this.props;
    const { formSubmitted, bpLines } = this.state;
    const enableInternalSKUCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_sku_code;
    const enableInternalRefCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_ref_code;

    const columns = [
      {
        title: 'PRODUCT',
        render: (item) => (
          <Fragment>
            <div onClick={() => window.open(`/inventory/product/view/${item?.tenant_product_info?.product_sku_id}`, '_blank')}>
              <ProductCodesAndName
                skuCode={item?.tenant_product_info?.internal_sku_code}
                refCode={item?.tenant_product_info?.ref_product_code}
                name={item?.tenant_product_info?.product_sku_name}
                showSku={enableInternalSKUCode}
                showRefCode={enableInternalRefCode}
              />
              {item?.product_category_info?.category_path?.length > 0 && (
                <ProductCategoryLabel
                  categoryPath={item?.product_category_info?.category_path}
                  categoryName={item?.product_category_info?.category_path?.at(-1)}
                  containerStyle={{
                    width: 'fit-content',
                  }}
                />
              )}
            </div>
          </Fragment>
        ),
      },
      {
        title: 'COMPLETION',
        width: '150px',
        render: (record) => (
          <div style={{ width: '130px' }}>
            <Progress size="small" percent={parseInt((record?.completed_quantity / record?.quantity) * 100)} strokeColor={{ from: '#108ee9', to: '#87d068' }} trailColor="#2d7df71a" />
          </div>
        ),
      },
      {
        title: 'PENDING',
        render: (record) => `${QUANTITY(record?.quantity - record?.completed_quantity, record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase()}`,
      },
      {
        title: 'QUANTITY',
        width: '120px',
        render: (item) => {
          const { createMOAdjustmentLoading } = this.props;
          const { formSubmitted } = this.state;
          return (
            <Fragment>
              <H3FormInput
                value={item.batch_quantity}
                onChange={(event) => this.handleTableChange(item.key, 'batch_quantity', event.target.value)}
                type="number"
                name=" produced quantity"
                labelClassName="add-batch__label"
                disabled={createMOAdjustmentLoading}
                inputClassName="orgFormInput fg-batch-input"
                required
                showError={formSubmitted && !Number(item.batch_quantity)}
              />
              {item?.batch_quantity < 0 && <H3Text text="*Please enter a valid quantity" className="input-error" />}
            </Fragment>
          );
        },
      },
      {
        title: 'BATCH NUMBER',
        width: '160px',
        render: (item) => {
          const { createMOAdjustmentLoading, user } = this.props;
          const { formSubmitted, } = this.state;
          const isBatchNumberEditable = user?.tenant_info?.inventory_config?.settings?.batch_management?.is_batch_number_editable;
          return (
            <H3FormInput
              value={item?.batch_number}
              onChange={(event) => this.handleTableChange(item.key, 'batch_number', event.target.value)}
              type="text"
              name=" batch number"
              labelClassName="add-batch__label"
              disabled={createMOAdjustmentLoading || !isBatchNumberEditable}
              inputClassName="orgFormInput fg-batch-input"
              required
              showError={formSubmitted && !item?.batch_number}
            />
          );
        },
      },
      {
        title: 'Shelf/Rack',
        width: '160px',
        render: (item) => (
          <WarehouseLocationSelector
            containerClassName="warehouse-location-selector"
            hideTitle
            selectedInventoryLocation={item?.inventory_location_path}
            onChange={(id, name) => {
              const copyData = JSON.parse(JSON.stringify(bpLines));
              const updatedData = copyData.map((obj) => {
                if (obj?.key === item?.key && item?.key) {
                  obj.inventory_location_id = id;
                  obj.inventory_location_path = name;
                  return {
                    ...obj,
                    inventory_location_id: id,
                    inventory_location_path: name,
                  };
                }
                return obj;
              });
              this.setState({ bpLines: updatedData });
            }}
            destDepartmentId={selectedMO?.tenant_department_id}
          />
        ),
      },
      {
        title: 'LOT NUMBER',
        width: '160px',
        render: (item) => {
          const { createMOAdjustmentLoading } = this.props;
          return (
            <H3FormInput
              value={item?.lot_number}
              onChange={(event) => this.handleTableChange(item.key, 'lot_number', event.target.value)}
              type="text"
              name=" produced quantity"
              labelClassName="add-batch__label"
              disabled={createMOAdjustmentLoading}
              inputClassName="orgFormInput fg-batch-input"
              required
            />
          );
        },
      },
      {
        title: 'EXPIRY',
        render: (item) => {
          const { createMOAdjustmentLoading } = this.props;
          return (
            <div className="fg-batch-input">
              {dayjs(item?.expiry_date).format('YYYY-MM-DD') !== INFINITE_EXPIRY_DATE ? (
                <DatePicker
                  value={dayjs(item?.expiry_date)}
                  onChange={(value) => {
                    if (value) {
                      this.handleTableChange(item.key, 'expiry_date', value);
                    } else {
                      this.handleTableChange(item.key, 'expiry_date', dayjs());
                    }
                  }}
                  disabled={createMOAdjustmentLoading}
                  style={{
                    border: '1px solid rgba(68, 130, 218, 0.2)',
                    borderRadius: '3px',
                    height: '28px',
                    padding: '1px 10px',
                    width: '100%',
                    background: 'white',
                  }}
                  disabledDate={(current) => current.isBefore(dayjs().subtract(1, 'day'))}
                />
              ) : '-'}
            </div>

          );
        },
      },
    ];

    if (cfV2DocManufacturingOrder?.data?.success) {

      const batchCustomFields = CustomFieldHelpers.getCfStructure(cfV2DocManufacturingOrder?.data?.batch_custom_fields, true);
      batchCustomFields?.map((cf) => {

        if (cf?.visible && cf?.isActive) {
          columns?.push({
            title: cf?.fieldName?.toUpperCase(),
            key: cf?.cfId,
            width: '150px',
            render: (record) => (
              <CustomFieldLine
                customFields={record?.custom_fields || []}
                cfId={cf?.cfId}
                labelClassName="orgFormLabel"
                inputClassName="orgFormInput"
                formSubmitted={formSubmitted}
                customLineInputChange={(value) => {this.handleTableChange(record?.key, 'custom_fields', value)}}
              />
            )
          });
        }
      });
    }

    columns.push({
      title: '',
      render: (text, record) => (
        <Fragment>
          <div className="update-so__delete-line-button" onClick={() => this.handleDelete(record.key)}>
            <CloseOutlined />
          </div>
        </Fragment>
      ),
      width: '40px',
      fixed: 'right',
    })

    return columns;
  }

  /**
   *
   * @param key
   */
  handleDelete(key) {
    const { bpLines } = this.state;
    if (bpLines?.length === 1) {
      notification.error({
        message: 'You cannot eliminate it if there is only one byproduct.',
        placement: 'top',
        duration: 4,
      });
    } else {
      const copyData = bpLines.filter((item) => item.key !== key);
      this.setState({ bpLines: copyData });
    }
  }

  /**
   *
   * @param key
   * @param label
   * @param value
   */
  handleTableChange(key, label, value) {
    const { bpLines } = this.state;
    const bpLinesCopy = JSON.parse(JSON.stringify(bpLines));
    for (let i = 0; i < bpLines?.length; i++) {
      if (bpLines[i]?.key === key) {
        const selectedRow = bpLinesCopy[i];
        selectedRow[label] = value;
        bpLinesCopy[i] = selectedRow;
      }
    }
    this.setState({ bpLines: bpLinesCopy });
  }

  static getDerivedStateFromProps(props, state) {
    const { selectedMO, fgTenantProductId, cfV2DocManufacturingOrder, inventoryLocations, } = props;

    if (selectedMO?.mo_id && cfV2DocManufacturingOrder?.data?.success && !state.isUserReadyForLocation) {
      const activeLocation = inventoryLocations?.inventory_location?.filter((i) => i?.is_active && i?.tenant_department_id === selectedMO?.tenant_department_id)?.[0] || {};

      const bpLines = selectedMO?.by_product_lines
        ?.filter((item) => item?.fg_tenant_product_id == fgTenantProductId)
        ?.map((item) => ({
          ...item,
          key: uuidv4(),
          batch_quantity: 0,
          batch_number: `${item?.tenant_product_info?.internal_sku_code}/${dayjs().format('DDMMYY')}/${item?.tenant_product_info?.product_batch_counter}`,
          expiry_date: item?.tenant_product_info?.expiry_days > 0
            ? dayjs().add(item?.tenant_product_info?.expiry_days || 0, 'day')
            : dayjs(INFINITE_EXPIRY_DATE),
          cost_price: Number(item?.tenant_product_info?.cost_price?.toFixed(DEFAULT_CUR_ROUND_OFF)),
          product_category_info: item?.tenant_product_info?.product_category_info,
          custom_fields: CustomFieldHelpers.getCfStructure(
            cfV2DocManufacturingOrder?.data?.batch_custom_fields,
            true
          ),
          inventory_location_id: activeLocation?.inventory_location_id ?? null,
          inventory_location_path: activeLocation?.inventory_location_path ?? null,
        }));

      return {
        ...state,
        bpLines,
        isUserReadyForLocation: true,
      };
    }

    if (inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]?.tenant_department_id === selectedMO?.tenant_department_id && state?.isUserReadyForLocation && !state?.isUserReady) {
      const bpLinesCopy = JSON.parse(JSON.stringify(state?.bpLines));
      const activeLocation = inventoryLocations?.inventory_location?.filter(i => i?.is_active)?.[0];

      const updatedLines = bpLinesCopy?.map(item => ({
        ...item,
        inventory_location_id: activeLocation?.inventory_location_id,
        inventory_location_path: activeLocation?.inventory_location_path,
      }));

      return {
        ...state,
        bpLines: updatedLines,
        isUserReady: true,
      };
    }

    return state;
  }

  /**
   *
   */
  addByProducts = () => {
    const {
      bpLines,
    } = this.state;
    const {
      createMOAdjustment, callback, isFlexible,
    } = this.props;
    if (this.isDataValid()) {
      const payload = [];
      bpLines?.map((bpLine) => {
        payload.push({
          entity_type: 'BY_PRODUCT',
          entity_id: bpLine?.by_product_id,
          quantity: bpLine?.batch_quantity,
          expiry_date: bpLine?.tenant_product_info?.expiry_days > 0 ? bpLine?.expiry_date : INFINITE_EXPIRY_DATE,
          mo_id: bpLine?.mo_id,
          status: 'ISSUED',
          lot_number: bpLine?.lot_number || '',
          custom_batch_number: bpLine?.batch_number,
          cost_price: bpLine?.tenant_product_info?.cost_price,
          selling_price: 0,
          wastage_quantity: 0,
          custom_fields: CustomFieldHelpers.postCfStructure(bpLine?.custom_fields),
          inventory_location_id: bpLine?.inventory_location_id,
          inventory_location_path: bpLine?.inventory_location_path,
        });
      });
      if (payload?.length) {
        createMOAdjustment({mo_adjustments: [...payload]}, () => callback());
      }
    }
  }

  isDataValid() {
    const { bpLines } = this.state;
    let isDataValid = true;
    for (let i = 0; i < bpLines?.length; i++) {
      if (Number(bpLines[i]?.batch_quantity) < 0) isDataValid = false;
    }
    if (!bpLines?.filter((item) => Number(item?.batch_quantity) > 0)?.length) isDataValid = false;
    // check for custom fields
    bpLines?.map((bpLine) => {
      for (const field of bpLine?.custom_fields) {
        if (field?.isRequired && (!field?.fieldValue || field?.fieldValue == 0 || field?.fieldValue === '')) {
          isDataValid = false;
        }
      }
    })
    return isDataValid;
  }

  /**
      *
      * @return {JSX.Element}
      */
  render() {
    const {
      createMOAdjustmentLoading, isFlexible, getInventoryLocationsLoading,
    } = this.props;
    const {
      bpLines, columns, formSubmitted, showPRZModal, selectedDocumentId
    } = this.state;

    return (
      <div className="ant-row">
        <div className="ant-col-md-24">
          <div className="rm-table-wrapper">
            <Table
              title={() => `By Products (${bpLines?.length})`}
              columns={this.getColumns()}
              dataSource={bpLines || []}
              size="small"
              scroll={{ x: 'max-content', }}
              pagination={false}
              bordered
              loading={createMOAdjustmentLoading || getInventoryLocationsLoading}
            />
          </div>
        </div>
        <div className="custom-drawer__footer">
          <div className="ant-col-md-6" style={{ marginTop: '0.5rem' }}>
            <H3Button
              buttonType={defaultButtonTypes.BLUE_ROUNDED}
              text="Create"
              onClick={() => {
                this.setState({ formSubmitted: true, })
                this.addByProducts();
              }}
              isLoading={createMOAdjustmentLoading}
              disabled={createMOAdjustmentLoading}
              style={{
                width: '180px',
                borderRadius: '5px',
                padding: '7px 0px',
                marginLeft: '0',
              }}
            />
          </div>
        </div>
        <PRZModal
          isOpen={showPRZModal}
          onClose={() => {
            this.setState({ showPRZModal: false, selectedDocumentId: null });
          }}
          entityType="product"
          documentId={selectedDocumentId}
        />
      </div>
    );
  }
}

const mapStateToProps = ({
  UserReducers, MOReducers, CFV2Reducers, InventoryLocationReducers,
}) => ({
  user: UserReducers.user,
  createMOAdjustmentLoading: MOReducers.createMOAdjustmentLoading,
  selectedMO: MOReducers.selectedMO,
  cfV2DocManufacturingOrder: CFV2Reducers.cfV2DocManufacturingOrder,
  inventoryLocations: InventoryLocationReducers.inventoryLocations,
  getInventoryLocationsLoading: InventoryLocationReducers.getInventoryLocationsLoading,
});

const mapDispatchToProps = (dispatch) => ({
  createMOAdjustment: (payload, callback) => dispatch(MOActions.createMOAdjustment(payload, callback)),
});

ByProduct.propTypes = {
  createMOAdjustment: PropTypes.func,
  selectedMO: PropTypes.any,
  user: PropTypes.any,
  createMOAdjustmentLoading: PropTypes.bool,
  callback: PropTypes.any,
  selectedByProductLine: PropTypes.any,
  isFlexible: PropTypes.bool,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(ByProduct));
