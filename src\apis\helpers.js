import Constants, { CountryC<PERSON>, DEFAULT_CUR_ROUND_OFF, mobileViewBreakPoint, QUANTITY } from '@Apis/constants';
import axios from 'axios';
import Decimal from 'decimal.js';
import '../../slack';
import * as Sentry from '@sentry/react';
import dayjs from 'dayjs';
import documentUINamesConstant from './documentUINamesConstant';
export async function refreshAccessToken() {
  const refreshToken = localStorage.getItem('h3m-procuzy-refresh-token')?.trim();
  const data = JSON.stringify({ refreshToken });
  const config = {
    method: 'post',
    url: Constants.REFRESH_ACCESS_TOKEN,
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  };

  const response = await axios(config);
  if (!response.data.accessToken) { throw new Error('Invalid access token'); }
  localStorage.setItem('h3m-procuzy-access-token', response.data.accessToken.trim());
  return response.data.accessToken.trim();
}

class Helpers {
  /**
   * Converts a given object to a query string that can be appended to a URL.
   *
   * @param {Object} queryObject - The object to convert
   * @returns {String} The query string
   * like {key1: value1, key2: value2} => key1=value1&key2=value2
   */
  convertObjectToQueryString = (queryObject) => {
    let queryString = '';
    if (queryObject === undefined || queryObject === null || Object.keys(queryObject).length === 0) {
      return queryString;
    }
    Object.entries(queryObject).forEach(([key, value]) => {
      queryString += `${key}=${value}&`;
    });
    return queryString;
  };

  // sendApiLog = (error, log) => {
  //    Sentry.captureException(error, { extra: log });
  // }

  batchDateFormatter = (date, format, type = 'alphabet') => {
    if (!date) return ''; // Handle null/undefined dates gracefully

    const numericFormats = {
      'DD/MM/YYYY': 'DD/MM/YYYY',
      'MM/YYYY': 'MM/YYYY',
      'MM/YY': 'MM/YY',
    };

    const alphabetFormats = {
      'DD/MM/YYYY': 'DD MMM, YY',
      'MM/YYYY': 'MMM, YYYY',
      'MM/YY': 'MMM, YY',
    };

    const formats = type === 'number' ? numericFormats : alphabetFormats;

    // Default to custom format if not found in predefined formats
    return dayjs(date).format(formats[format] || format);
  };

  fetchHelper = (reqParams, retry = false) => {
    const {
      url, method, headers = {}, data, file,
    } = reqParams;
    const accessToken = localStorage.getItem('h3m-procuzy-access-token');
    const user = JSON.parse(localStorage.getItem('user'));
    const request = {
      method,
      headers: {
        Authorization: `Bearer ${accessToken}`,
        ...headers,
      },
      muteHttpExceptions: true,
      body: file || (data && JSON.stringify(data)),
    };
    return new Promise((resolve, reject) => {
      fetch(url, request)
        .then((response) => Promise.all([response.json(), response.status]))
        .then(([response, status]) => {
          if (status >= 200 && status <= 300) {
            // if (!response?.success) {
            //   try {
            //     this.sendApiLog(response, {
            //       request: {
            //         url,
            //         ...request,
            //         body: request?.body ? JSON.parse(request?.body) : null,
            //       },
            //       user,
            //     });
            //   } catch (e) {
            //     console.log('Sentry log failed', e);
            //   }
            // }
            resolve(response);
          }
          return [response, status];
        })
        .then(async ([error, status]) => {
          let errorMessage = '';
          switch (status) {
          case 400: {
            errorMessage = error.message || 'Bad Request';
            // this.sendApiLog(error);
            break;
          }
          case 401: {
            if (retry) {
              errorMessage = error.message || 'Unauthorized Access';
            } else {
              await refreshAccessToken()
                .then(async () => {
                  try {
                    const result1 = await this.fetchHelper(reqParams, true);
                    resolve(result1);
                  } catch (error2) {
                    reject(error2);
                  }
                })
                .catch(async () => {
                  errorMessage = 'Error';
                });
              errorMessage = error.message || 'Unauthorized Access';
            }
            break;
          }
          case 500: {
            errorMessage = error.message || 'Internal server error';
            // this.sendApiLog(error);
            break;
          }
          default: {
            errorMessage = 'Error';
          }
          }
          reject(errorMessage);
        });
    });
  };

  fetchHelperDownloadFile = (reqParams, retry = false) => {
    const {
      url, method, headers = {}, data, file,
    } = reqParams;
    const accessToken = localStorage.getItem('h3m-procuzy-access-token');
    return new Promise((resolve, reject) => {
      const hdr = {
        method,
        muteHttpExceptions: true,
        body: file || (data && JSON.stringify(data)),
      };
      if (!url.includes('procuzystorage.blob.core.windows.net')) {
        hdr.headers = {
          Authorization: `Bearer ${accessToken}`,
          ...headers,
        };
      }
      fetch(url, hdr)
        .then((response) => [response.blob(), response.status])
        .then(([response, status]) => {
          if (status >= 200 && status <= 300) {
            resolve(response);
          }
          return [response, status];
        })
        .then(async ([error, status]) => {
          let errorMessage = '';
          switch (status) {
          case 400: {
            errorMessage = error.message || 'Bad Request';
            break;
          }
          case 401: {
            if (retry) {
              errorMessage = error.message || 'Unauthorized Access';
            } else {
              await refreshAccessToken()
                .then(async () => {
                  try {
                    const result1 = await this.fetchHelper(reqParams, true);
                    resolve(result1);
                  } catch (error2) {
                    reject(error2);
                  }
                })
                .catch(async () => {
                  errorMessage = 'Error';
                });
              errorMessage = error.message || 'Unauthorized Access';
            }
            break;
          }
          case 500: {
            errorMessage = error.message || 'Internal server error';
            break;
          }
          default: {
            errorMessage = 'Error';
          }
          }
          reject(errorMessage);
        });
    });
  };

  get = ({ url, headers }) => {
    const reqParams = {
      url,
      method: 'GET',
      headers,
    };
    return this.fetchHelper(reqParams);
  };

  getFile = ({ url, headers }) => {
    const reqParams = {
      url,
      method: 'GET',
      headers,
    };
    return this.fetchHelperDownloadFile(reqParams);
  };

  post = ({ url, data, customHeaders }) => {
    const headers = {
      'Content-Type': 'application/json',
      ...customHeaders,
    };
    const reqParams = {
      url,
      method: 'POST',
      headers,
      data,
    };
    return this.fetchHelper(reqParams);
  };

  filePost = ({ url, file }) => {
    const headers = {};
    const reqParams = {
      url,
      method: 'POST',
      headers,
      file,
    };
    return this.fetchHelper(reqParams);
  };

  filePut = ({ url, file }) => {
    const headers = {};
    const reqParams = {
      url,
      method: 'PUT',
      headers,
      file,
    };
    return this.fetchHelper(reqParams);
  };

  put = ({ url, data }) => {
    const headers = {
      'Content-Type': 'application/json',
    };
    const reqParams = {
      url,
      method: 'PUT',
      headers,
      data,
    };
    return this.fetchHelper(reqParams);
  };

  patch = ({ url, data, customHeaders }) => {
    const headers = {
      'Content-Type': 'application/json',
      ...customHeaders,
    };
    const reqParams = {
      url,
      method: 'PATCH',
      headers,
      data,
    };
    return this.fetchHelper(reqParams);
  };

  delete = ({ url, data }) => {
    const headers = {
      'Content-Type': 'application/json',
    };
    const reqParams = {
      url,
      method: 'DELETE',
      headers,
      data,

    };
    return this.fetchHelper(reqParams);
  };

  timeSinceDate = (date) => {
    const seconds = Math.floor((new Date() - date) / 1000);
    let interval = Math.floor(seconds / 31536000);
    if (interval > 1) {
      return `${interval} years ago`;
    }
    interval = Math.floor(seconds / 2592000);
    if (interval > 1) {
      return `${interval} months ago`;
    }
    interval = Math.floor(seconds / 86400);
    if (interval > 1) {
      return `${interval} days ago`;
    }
    interval = Math.floor(seconds / 3600);
    if (interval > 1) {
      return `${interval} hours ago`;
    }
    interval = Math.floor(seconds / 60);
    if (interval > 1) {
      return `${interval} minutes ago`;
    }
    return `${Math.floor(seconds)} seconds`;
  };

  /**
   *
   * @param email
   * @return {boolean}
   */
  validateEmail = (email) => {
    if (/^[+\w]+([\.-]?[+\w]+)*@\w+([\.-]?\w+)*(\.\w{2,10})+$/.test(email)) {
      return true;
    }
    return false;
  };

  /**
   *
   * @param urlString
   * @return {boolean}
   */
  isValidUrl = (urlString) => {
    const urlPattern = new RegExp('^(https?:\\/\\/)?' // validate protocol
      + '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' // validate domain name
      + '((\\d{1,3}\\.){3}\\d{1,3}))' // validate OR ip (v4) address
      + '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' // validate port and path
      + '(\\?[;&a-z\\d%_.~+=-]*)?' // validate query string
      + '(\\#[-a-z\\d_]*)?$', 'i'); // validate fragment locator
    return !!urlPattern.test(urlString);
  };

  /**
   *
   * @param phone
   * @return {boolean}
   */
  validatePhoneNumber(phone) {
    if (/^\(?(\d{3})\)?[- ]?(\d{3})[- ]?(\d{4})$/.test(phone)) {
      return true;
    }
    return false;
  }

  /**
   * @param gstNumber
   * @param phone
   * @return {boolean}
   */
  validateGstNumber(gstNumber) {
    if (gstNumber?.toString()?.length === 15) {
      return true;
    }
    return false;
  }

  /**
   *
   */
  scrollToTop() {
    document.getElementById('scroller').scroll(0, 0);
  }

  permissionEntities = {
    PRODUCT: 'product',
    INVENTORY_INDENT: 'inventory_indent',
    VENDORS: 'vendors',
    PURCHASE_REQUEST: 'purchase_request',
    PURCHASE_INDENT: 'purchase_indent',
    PURCHASE_ORDER: 'purchase_order',
    GOOD_RECEIVING: 'good_receiving',
    ACCOUNT_PAYABLE_INVOICE: 'account_payable_invoice',
    DEBIT_NOTE: 'debit_note',
    WORKFLOW_SETUP: 'workflow_setup',
    ACCESS_CONTROL_SETUP: 'access_control_setup',
    USER_MANAGEMENT: 'user_management',
    VENDOR_PAYOUT: 'vendor_payout',
    PAYMENT_REQUEST: 'payment_request',
    REQUEST_FOR_QUOTATION: 'request_for_quotation',
    CUSTOMER: 'customer',
    SALES_ESTIMATE: 'sales_estimate',
    SALES_ORDER: 'sales_order',
    ANNUAL_MAINTENANCE_CONTRACT: 'annual_maintenance_contract',
    PACKING_SLIP: 'packing_slip',
    INVOICE: 'invoice',
    CREDIT_NOTE: 'credit_note',
    RETURN_SLIP: 'return_slip',
    CUSTOMER_PAYMENTS: 'customer_payments',
    STOCK_TRANSFER: 'stock_transfer',
    DELIVERY_CHALLAN: 'delivery_challan',
    BOM: 'bill_of_material',
    MO: 'manufacturing_order',
    FACTORY_SETUP: 'factory_setup',
    QUALITY_CHECKS: 'quality_checks',
    QUALITY_RULES: 'quality_rules',
    EXPENSES: 'expenses',
    PURCHASE_DASHBOARD: 'purchase_dashboard',
    INVENTORY_DASHBOARD: 'inventory_dashboard',
    SALES_DASHBOARD: 'sales_dashboard',
    PRODUCTION_DASHBOARD: 'production_dashboard',
    E_WAY_BILL: 'e_way_bill',
    E_INVOICE: 'e_invoice',
    PRICE_LIST: 'price_list',
    REPORTS: 'reports',
    DEMAND_FORECASTING: 'demand_forecasting',
    MRP: 'material_planning',
    GATE_DOCUMENT: 'gate_document',
    VENDOR_PRICE: 'vendor_price',
    CONSUMPTION_ORDER: 'consumption_order',
  };

  features = {
    SALES_ESTIMATE: 'sales_estimate',
    SALES_ORDER: 'sales_order',
    ANNUAL_MAINTENANCE_CONTRACT: 'annual_maintenance_contract',
    PACKING_SLIP: 'packing_slip',
    INVOICE: 'invoice',
    CREDIT_NOTE: 'credit_note',
    PURCHASE_ORDER: 'purchase_order',
    VENDOR_PAYOUTS: 'vendor_payouts',
    GRN: 'grn',
    ACCOUNT_PAYABLE_INVOICE: 'account_payable_invoice',
    DEBIT_NOTE: 'debit_note',
    PURCHASE_REQUEST: 'purchase_request',
    INVENTORY_ADJUSTMENT: 'inventory_adjustment',
    CUSTOMER_PAYMENT: 'customer_payment',
    STOCK_TRANSFER: 'stock_transfer',
    DELIVERY_CHALLAN: 'delivery_challan',
    BILL_OF_MATERIAL: 'bill_of_material',
    MANUFACTURING_ORDER: 'manufacturing_order',
    ZOHO_BOOKS_INTEGRATION: 'zoho_books_integration',
    UNICOMMERCE_INTEGRATION: 'unicommerce_integration',
    TALLY_INTEGRATION: 'tally_integration',
    SHOPIFY_INTEGRATION: 'shopify_integration',
    MRP: 'mrp',
    QUALITY_CONTROL: 'quality_control',
    MARKETPLACE_ENABLED: 'marketplace_enabled',
    INVENTORY: 'inventory',
    VENDOR: 'vendor',
    VENDORS: 'vendor',
    GOOD_RECEIVING_NOTE: 'good_receiving_note',
    EXPENSES: 'expenses',
    FACTORY_SETUP: 'factory_setup',
    E_WAY_BILL: 'e_way_bill',
    E_INVOICE: 'e_invoice',
    BARCODING_ENABLED: 'barcoding_enabled',
    PRICE_LIST: 'price_list',
    DEMAND_FORECASTING: 'demand_forecasting',
    WHATSAPP_INTEGRATION: 'whatsapp_integration',
    DATA_MASKING: 'data_masking',
    RFQ: 'rfq',
    REQUEST_FOR_QUOTATION: 'request_for_quotation',
    GATE_DOCUMENT: 'gate_document',
  };

  permissionTypes = {
    READ: 'READ',
    CREATE: 'CREATE',
    UPDATE: 'UPDATE',
    DELETE: 'DELETE',
    APPROVE: 'APPROVE',
    UPLOAD: 'UPLOAD',
    COSTING: 'COSTING',
  };

  getPermission(entity, type, user) {
    if (user?.tenant_info?.permission[entity]) {
      return !!user?.tenant_info?.permission[entity][type];
    }
    return false;
  }

  getFeatureEnabled(entity, user) {
    return user?.tenant_info?.config?.feature[entity] || false;
  }

  getStatusColor(status) {
    if (status === 'DRAFT') return { color: 'rgb(136,180,248)', text: 'Draft' };
    if (status === 'PENDING') return { color: '#dcaa06', text: 'Pending' };
    if (status === 'VOID') return { color: '#939393', text: 'Void' };
    if (status === 'ISSUED') return { color: '#22C55EFF', text: 'Issued' };
    if (status === 'SENT_FOR_APPROVAL') return { color: '#1c7be1', text: 'Sent for Approval' };
    if (status === 'CANCELLED') return { color: '#ff0000', text: 'Cancelled ' };
    if (status === 'REJECTED') return { color: '#ff0000', text: 'Rejected ' };
    if (status === 'PAID') return { color: '#22C55EFF', text: 'Paid' };
    if (status === 'PAYMENT_PROCESSING') return { color: '#2daef7', text: 'Processing' };
    if (status === 'PAYMENT_FAILED') return { color: '#ff0000', text: 'Failed' };
    if (status === 'APPROVED') return { color: '#22C55EFF', text: 'Approved ' };
    if (status === 'PAYMENT_STARTED') return { color: '#22C55EFF', text: 'Paid' };
    if (status === 'PAYMENT_SUCCESS') return { color: '#22C55EFF', text: 'Paid' };
    if (status === 'NOT_RECEIVED' || status === 'NOT RECEIVED') return { color: '#b0b0b0', text: 'Not Received' };
    if (status === 'PARTIALLY_RECEIVED') return { color: '#3e77cc', text: 'Partially Received' };
    if (status === 'RECEIVED_MORE') return { color: '#22C55EFF', text: 'Received More' };
    if (status === 'PARTIALLY_PAID') return { color: '#3e77cc', text: 'Partially Paid' };
    if (status === 'PAID_MORE') return { color: '#22C55EFF', text: 'Paid More' };
    if (status === 'RECEIVED') return { color: '#22C55EFF', text: 'Received' };
    if (status === 'BILLED') return { color: '#22C55EFF', text: 'Billed' };
    if (status === 'NOT_PAID') return { color: '#b0b0b0', text: 'Not Billed' };
    if (status === 'SENT_FOR_REVISION') return { color: '#f68d16', text: 'In Revision' };
    if (status === 'COMPLETED') return { color: '#22C55EFF', text: 'Closed' };
    if (status === 'CONFIRMED') return { color: '#22C55EFF', text: 'Confirmed' };
    if (status === 'DELIVERED') return { color: '#22C55EFF', text: 'Delivered' };
    if (status === 'PARTIALLY_FULFILLED') return { color: '#3e77cc', text: 'Partially Fulfilled' };
    if (status === 'FULFILLED') return { color: '#22C55EFF', text: 'Fulfilled' };
    if (status === 'PLANNED') return { color: '#22C55EFF', text: 'Planned' };
    if (status === 'IN_PROGRESS') return { color: '#2daef7', text: 'In Progress' };
    if (status === 'PENDING_FOR_QC') return { color: '#06b5dc', text: 'Quality Check Pending' };
    if (status === 'DONE') return { color: '#22C55EFF', text: 'Done' };
    if (status === 'CLOSED') return { color: '#22C55EFF', text: 'Closed' };
    return { color: '#dcaa06', text: status };
  }

  getProductType(type) {
    if (type === 'BUNDLE') return { color: '#1db91d', text: 'Bundle' };
    if (type === 'SERVICE') return { color: '#5287da', text: 'Service' };
    if (type === 'NON_STORABLE') return { color: '#f33ad5', text: 'Non Storable' };
    if (type === 'STORABLE') return { color: '#eb2f96', text: 'Storable' };
    return { color: '#ea8e15', text: type?.toProperCase() };
  }

  checkIfFiedExists(list, key) {
    let exists = false;
    if (list?.length && key) {
      for (let i = 0; i < list?.length; i++) {
        const listItem = list[i];
        if (listItem[key]) {
          exists = true;
          break;
        }
      }
    }
    return exists;
  }

  getValueTotalInObject(list, field) {
    let total = 0;
    for (let i = 0; i < list?.length; i++) {
      total += Number(list[i]?.[field]);
    }
    return total;
  }

  hasNull(element, index, array) {
    return !element || element === null;
  }

  /**
 * Retrieves the user's tenant department ID and the default store ID
 * for a specific tenant (based on the record's tenant_id).
 */
  getUserTenantDepartmentIds = (user, tenantId) => {
    const tenantUserData = user?.user_tenants?.find((i) => i.tenant_id === tenantId);
    return {
      userTenantDepartmentId: tenantUserData?.tenant_department_info?.tenant_department_id,
      tenantDefaultStoreId: tenantUserData?.default_store_id,
    };
  };

  getTenantDepartmentId(user) {
    const tenantInfo = user?.user_tenants?.find((i) => i.tenant_id === user?.tenant_info?.tenant_id);
    return tenantInfo?.tenant_department_info?.tenant_department_id;
  }

  getDefaultTenantDepartmentIdForSelectedTenant(user, tenantId) {
    const tenantInfo = user?.user_tenants?.find((i) => i.tenant_id === tenantId);
    return tenantInfo?.default_store_id;
  }

  getTenantDepartment(user) {
    const tenantInfo = user?.user_tenants?.find((i) => i.tenant_id === user?.tenant_info?.tenant_id);
    return tenantInfo?.tenant_department_info?.alias_name;
  }

  getDepartmentId(user) {
    const tenantInfo = user?.user_tenants?.find((i) => i.tenant_id === user?.tenant_info?.tenant_id);
    return tenantInfo?.tenant_department_info?.department_id;
  }

  snakeCaseToNormal(str) {
    if (str) {
      return str.split('_').join(' ');
    }
    return '';
  }

  getTenantEntityPermission(tenants, entity, permissionType) {
    const permittedTenants = [];
    for (let i = 0; i < tenants?.length; i++) {
      if (tenants[i]?.permission?.[entity]?.[permissionType]) {
        permittedTenants.push(tenants[i]?.tenant_id);
      }
    }
    return permittedTenants;
  }

  getUploadedBatches = (tenantProducts, quantity, getBatches) => {
    const oldBaches = tenantProducts?.filter((product) => product?.product_batches?.length)?.map((product) => product?.product_batches) || [];
    const batches = oldBaches?.flat();
    return getBatches(batches, quantity);
  };

  getValueFromUrl = (label) => new URLSearchParams(window.location.search).get(label);

  getAvatarColors = (character) => {
    const colors = [
      { letter: 'A', color1: '#FF6347', color2: '#FFD700' },
      { letter: 'B', color1: '#1E90FF', color2: '#32CD32' },
      { letter: 'C', color1: '#8A2BE2', color2: '#FF1493' },
      { letter: 'D', color1: '#FFA500', color2: '#FFFF00' },
      { letter: 'E', color1: '#008080', color2: '#00FFFF' },
      { letter: 'F', color1: '#FF6347', color2: '#FFD700' },
      { letter: 'G', color1: '#1E90FF', color2: '#32CD32' },
      { letter: 'H', color1: '#8A2BE2', color2: '#FF1493' },
      { letter: 'I', color1: '#FFA500', color2: '#FFFF00' },
      { letter: 'J', color1: '#008080', color2: '#00FFFF' },
      { letter: 'K', color1: '#FF6347', color2: '#FFD700' },
      { letter: 'L', color1: '#1E90FF', color2: '#32CD32' },
      { letter: 'M', color1: '#8A2BE2', color2: '#FF1493' },
      { letter: 'N', color1: '#FFA500', color2: '#FFFF00' },
      { letter: 'O', color1: '#008080', color2: '#00FFFF' },
      { letter: 'P', color1: '#FF6347', color2: '#FFD700' },
      { letter: 'Q', color1: '#1E90FF', color2: '#32CD32' },
      { letter: 'R', color1: '#8A2BE2', color2: '#FF1493' },
      { letter: 'S', color1: '#FFA500', color2: '#FFFF00' },
      { letter: 'T', color1: '#008080', color2: '#00FFFF' },
      { letter: 'U', color1: '#FF6347', color2: '#FFD700' },
      { letter: 'V', color1: '#1E90FF', color2: '#32CD32' },
      { letter: 'W', color1: '#8A2BE2', color2: '#FF1493' },
      { letter: 'X', color1: '#FFA500', color2: '#FFFF00' },
      { letter: 'Y', color1: '#008080', color2: '#00FFFF' },
      { letter: 'Z', color1: '#808080', color2: '#C0C0C0' },
    ];
    return colors.find((i) => i.letter === character?.toUpperCase());
  };

  getDialCode = (countryCode) => CountryCodes?.find((country) => country?.code === countryCode)?.dial_code;

  hasExpiryNotification = () => localStorage.getItem('expiry_notification') === 'true';

  transformArrayToTree = (array) => {
    // Create a map to store the nodes of the tree.
    const nodeMap = new Map();

    // Iterate over the array and create nodes for each element.
    for (const element of array) {
      const node = {
        ...element,
        child_bom_lines: [],
      };
      nodeMap.set(element.key, node);
    }

    // Iterate over the array again and link the child nodes to their parent nodes.
    for (const element of array) {
      if (element.parentKey) {
        const parentNode = nodeMap.get(element.parentKey);
        parentNode?.child_bom_lines?.push(nodeMap.get(element.key));
      }
    }
    // Return the list of nodes in which parentKey is null.
    const rootNodes = [];
    for (const [key, node] of nodeMap.entries()) {
      if (node.parentKey === null) {
        rootNodes.push(node);
      }
    }
    return rootNodes;
  };

  groupAndSumByTaxName(inputArray) {
    const groupedArray = {};
    inputArray?.forEach((item) => {
      const {
        tax_amount,
        tax_type_name,
      } = item;

      if (!groupedArray[tax_type_name]) {
        groupedArray[tax_type_name] = {
          tax_amount,
          tax_type_name,
        };
      } else {
        groupedArray[tax_type_name].tax_amount += tax_amount;
      }
    });

    return Object.values(groupedArray);
  }

  calculateTax(taxable_value, taxed_value, tax) {
    // Calculate tax amount for the non-group tax
    let tax_amount;
    if (tax.applicable_on === 'TAXED_VALUE') {
      tax_amount = taxed_value * (tax.tax_value / 100);
    } else {
      tax_amount = taxable_value * (tax.tax_value / 100);
    }

    return {
      ...tax,
      tax_amount,
      taxable_value,
      value_after_tax: tax.applicable_on === 'TAXED_VALUE' ? taxed_value + tax_amount : taxable_value + tax_amount,
      taxed_value: taxed_value + tax_amount,
    };
  }

  computeTaxation(taxable_value, tax_info, bill_from_state, bill_to_state, taxed_value) {
    const UT_STATES = ['ANDAMAN AND NICOBAR', 'LAKSHADWEEP', 'DADAR AND NAGAR HAVELI', 'DAMAN AND DIU', 'CHANDIGARH'];
    bill_from_state = bill_from_state?.toUpperCase();
    bill_to_state = bill_to_state?.toUpperCase();

    if (tax_info) {
      const { country_code } = tax_info;
      taxed_value = taxed_value || taxable_value;
      const line_tax_info = [];

      switch (country_code) {
      case 'IN': {
        if (tax_info.tax_type_name === 'GST') {
          if (tax_info.is_group) {
            const applicableTaxes = tax_info.child_taxes.filter((tax) => {
              if (UT_STATES.includes(bill_from_state) && UT_STATES.includes(bill_to_state)) {
                return !['SGST', 'CGST', 'IGST'].includes(tax.tax_type_name);
              } if (bill_from_state === bill_to_state) {
                return !['UTGST', 'IGST'].includes(tax.tax_type_name);
              }
              return !['SGST', 'CGST', 'UTGST'].includes(tax.tax_type_name);
            });

            applicableTaxes.forEach((tax) => {
              const calculated_tax = this.calculateTax(taxable_value, taxed_value, tax);
              taxed_value = calculated_tax.taxed_value;
              line_tax_info.push(calculated_tax);
            });
          } else {
            const calculated_tax = this.calculateTax(taxable_value, taxed_value, tax_info);
            taxed_value = calculated_tax.taxed_value;
            line_tax_info.push(calculated_tax);
          }
        } else {
          const applicableTaxes = tax_info.is_group ? tax_info.child_taxes : [tax_info];

          applicableTaxes.forEach((tax) => {
            const calculated_tax = this.calculateTax(taxable_value, taxed_value, tax);
            taxed_value = calculated_tax.taxed_value;
            line_tax_info.push(calculated_tax);
          });
        }
        break;
      }

      // Add cases for other countries if needed

      default: {
        const applicableTaxes = tax_info.is_group ? tax_info.child_taxes : [tax_info];

        applicableTaxes.forEach((tax) => {
          const calculated_tax = this.calculateTax(taxable_value, taxed_value, tax);
          taxed_value = calculated_tax.taxed_value;
          line_tax_info.push(calculated_tax);
        });
      }
      }

      return {
        tax_info: {
          ...tax_info,
          child_taxes: line_tax_info,
        },
        taxed_value,
        tax_amount: line_tax_info.reduce((prev, curr) => prev + curr.tax_amount, 0),
      };
    }
    return {
      tax_info: {
        child_taxes: [{
          tax_name: '',
          tax_amount: 0,
        }],
      },
    };
  }

  sortByReserved = (data) => {
    data?.sort((a, b) => {
      if (a.is_reserved && !b.is_reserved) {
        return -1;
      } if (!a.is_reserved && b.is_reserved) {
        return 1;
      }
      return 0;
    });
    return data;
  };

  //  Batch Selection Methods Start
  allocateBatchQuantiyM1 = (batches, quantity, allStock, uomInfo) => {
    let remainingQty = new Decimal(quantity || 0);
    for (let i = 0; i < batches?.length; i++) {
      batches[i].consumed_qty = 0;
    }

    for (let i = 0; i < batches?.length; i++) {
      const batch = batches[i];

      if (batch?.batch_in_use && (allStock ? !batch?.is_rejected_batch : true)) {
        const availableQty = new Decimal(QUANTITY(batch?.available_qty || 0, uomInfo?.precision || 3));
        const consumedQty = new Decimal(batch?.consumed_qty || 0);

        if (remainingQty.gt(0) && consumedQty.lt(availableQty)) {
          const consumeQty = Decimal.min(availableQty, remainingQty);
          batch.consumed_qty = consumeQty.toNumber();
          remainingQty = remainingQty.minus(consumeQty);
        } else {
          batch.consumed_qty = 0;
        }
      }
    }

    return batches;
  };

  allocateBatchQuantiyM2 = (batches, quantity, allStock, uomInfo) => {
    let remainingQty = new Decimal(quantity || 0);

    for (let i = 0; i < batches?.length; i++) {
      batches[i].quantity = 0;
    }

    for (let i = 0; i < batches?.length; i++) {
      const batch = batches[i];

      if (batch?.batch_in_use && (allStock ? !batch?.is_rejected_batch : true)) {
        const consumedQty = new Decimal(batch?.consumed_qty || 0);
        const dnGenQty = new Decimal(QUANTITY(batch?.dn_gen_qty || 0, uomInfo?.precision || 3));
        const availableToAllocate = consumedQty.minus(dnGenQty);

        if (
          remainingQty.gt(0) &&
          new Decimal(batch?.quantity || 0).lt(availableToAllocate)
        ) {
          const allocateQty = Decimal.min(availableToAllocate, remainingQty);
          batch.quantity = allocateQty.toNumber();
          remainingQty = remainingQty.minus(allocateQty);
        } else {
          batch.quantity = 0;
        }
      }
    }

    return batches;
  };

  selectBatchesByFIFO = (batches = [], quantity, allStock, entity, uomInfo) => {
    // Custom sorting function based on the 'created_at' property
    function compareCreatedAt(a, b) {
      const timestampA = Date.parse(a.created_at);
      const timestampB = Date.parse(b.created_at);

      // Compare timestamps
      return timestampA - timestampB;
    }

    // Sort the 'data' array using the custom sorting function
    if (batches) {
      batches.sort(compareCreatedAt);
    }

    let sortedBatch;

    if (entity === 'DebitNote') {
      sortedBatch = this.allocateBatchQuantiyM2(batches, quantity, allStock, uomInfo);
    } else {
      sortedBatch = this.allocateBatchQuantiyM1(this.sortByReserved(batches), quantity, allStock, uomInfo);
    }

    return sortedBatch;
  };

  selectBatchesByFEFO = (batches = [], quantity, allStock, entity, uomInfo) => {
    const currentDate = new Date().getTime();

    // Custom sorting function based on the proximity to the current date
    function compareNearestExpiry(a, b) {
      const diffA = Math.abs(new Date(a.expiry_date).getTime() - currentDate);
      const diffB = Math.abs(new Date(b.expiry_date).getTime() - currentDate);

      // Compare the differences
      return diffA - diffB;
    }

    // Sort the 'data' array using the custom sorting function
    if (batches) {
      batches.sort(compareNearestExpiry);
    }

    let sortedBatch;

    if (entity === 'DebitNote') {
      sortedBatch = this.allocateBatchQuantiyM2(batches, quantity, allStock, uomInfo);
    } else {
      sortedBatch = this.allocateBatchQuantiyM1(this.sortByReserved(batches), quantity, allStock, uomInfo);
    }

    return sortedBatch;
  };

  batchSelectionMethod = (batchConsumptionMethod, batches, quantity, allStock, entity, umoInfo) => {
    if (batchConsumptionMethod === 'FIFO') {
      return this.selectBatchesByFIFO(batches, quantity, allStock, entity, umoInfo);
    }
    if (batchConsumptionMethod === 'FEFO') {
      return this.selectBatchesByFEFO(batches, quantity, allStock, entity, umoInfo);
    }
  };

  getBatchMethod = (tenantSku) => {
    let batchMethod;
    if (tenantSku?.default_outwards_method) {
      batchMethod = tenantSku?.default_outwards_method;
    } else if (tenantSku?.product_info?.expiry_days > 0 || tenantSku?.product_sku_info?.expiry_days > 0) {
      batchMethod = 'FEFO';
    } else {
      batchMethod = 'FIFO';
    }

    return batchMethod;
  };

  getFileMimeTypeUsingName = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase();
    const mimeTypes = {
      txt: 'text/plain',
      html: 'text/html',
      css: 'text/css',
      js: 'application/javascript',
      json: 'application/json',
      png: 'image/png',
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      gif: 'image/gif',
      svg: 'image/svg+xml',
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      xls: 'application/vnd.ms-excel',
      xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      ppt: 'application/vnd.ms-powerpoint',
      pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      zip: 'application/zip',
      rar: 'application/x-rar-compressed',
      mp3: 'audio/mpeg',
      mp4: 'video/mp4',
      // Add more MIME types as needed
    };

    return mimeTypes[extension] || 'application/octet-stream';
  };

  downloadFileFromBase64 = (base64Data, fileName, fileType) => {
    if (!fileType) fileType = this.getFileMimeTypeUsingName(fileName);
    // Decode base64 string
    const byteCharacters = atob(base64Data);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);

    // Create a Blob from the byte array
    const blob = new Blob([byteArray], { type: fileType });

    // Create a URL for the Blob
    const blobUrl = URL.createObjectURL(blob);

    // Create a link element and trigger a download
    const link = document.createElement('a');
    link.href = blobUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  getTallyAllowedTenants(tenants, it_id) {
    const permittedTenants = [];
    for (let i = 0; i < tenants?.length; i++) {
      if (tenants[i]?.tally_configuration?.it_id === it_id) {
        permittedTenants.push(tenants[i]?.tenant_id);
      }
    }
    return permittedTenants;
  }

  convertDays(totalDays) {
    const daysInMonth = 30.44; // Average number of days in a month

    if (totalDays > 90) {
      // Calculate the number of months
      const months = Math.floor(totalDays / daysInMonth);
      const remainingDays = totalDays - (months * daysInMonth);

      return `${months} Months, ${remainingDays.toFixed(2)} Days`;
    }

    return `${totalDays.toFixed(2)} Days`;
  }

  objectToBase64 = (obj) => {
    try {
      const jsonString = JSON.stringify(obj);
      const base64String = btoa(jsonString);
      return base64String;
    } catch (error) {
      console.error('Error converting object to Base64:', error);
      return null;
    }
  };

  getDrawerWidth = (width) => {
    const screenWidth = window.innerWidth;
    if (screenWidth < mobileViewBreakPoint + 1) {
      return screenWidth - screenWidth * 0.1;
    } if (screenWidth < 1080) {
      return screenWidth - screenWidth * 0.15;
    } if (screenWidth < 1200) {
      return screenWidth - screenWidth * 0.2;
    }
    return width;
  };

  configuredRoundOff = (value, configuration) => {

    let val = Number(value || 0);
    if (configuration === 'LOWER_ROUND_OFF') {
      return { value: Math.floor(val?.toFixed(DEFAULT_CUR_ROUND_OFF)), roundOff: Math.floor(val?.toFixed(DEFAULT_CUR_ROUND_OFF)) - val, };
    }
    else if (configuration === 'UPPER_ROUND_OFF') {
      return { value: Math.ceil(val?.toFixed(DEFAULT_CUR_ROUND_OFF)), roundOff: Math.ceil(val?.toFixed(DEFAULT_CUR_ROUND_OFF)) - val, };
    }
    else if (configuration === 'NEAREST_WHOLE_NUMBER') {
      return { value: Math.round(val?.toFixed(DEFAULT_CUR_ROUND_OFF)), roundOff: Math.round(val?.toFixed(DEFAULT_CUR_ROUND_OFF)) - val, };
    }
    return { value: val?.toFixed(DEFAULT_CUR_ROUND_OFF), roundOff: 0, };
  };

  setColumnMinimumWidth = (column_name) => {

    const len = column_name?.length;

    if (len < 6) {
      return 'min-width-50';
    }
    if (len < 9) {
      return 'min-width-70';
    }
    if (len < 11) {
      return 'min-width-85';
    }
    if (len < 13) {
      return 'min-width-100';
    }
    if (len < 15) {
      return 'min-width-115';
    }
    if (len < 17) {
      return 'min-width-130';
    }
    if (len < 20) {
      return 'min-width-150';
    }
    if (len < 23) {
      return 'min-width-170';
    }
    if (len < 25) {
      return 'min-width-185';
    }
    return 'min-width-200';
  };

  appendSystemDefaultFilterIdInURL = (url) => `${url}&filter_id=system_default`;

  trimDecimalsAsString(value, digits) {
    if (typeof value !== 'number' || typeof digits !== 'number') return '';
    const parts = value.toString().split('.');
    if (parts.length === 1 || digits === 0) return parts[0];
    return `${parts[0]}.${parts[1].substring(0, digits)}`;
  }

  trimDecimalsAsNumber(value, digits) {
    if (typeof value !== 'number' || typeof digits !== 'number') return null;
    const factor = Math.pow(10, digits);
    return Math.trunc(value * factor) / factor;
  }

  sortByParameter = ({ data, parameter = 'internal_sku_code', orderBy = 'ASC' }) => {
    const getNestedValue = (obj, path) => {
      return path.split('.').reduce((acc, key) => acc?.[key], obj);
    };
    return data?.sort((a, b) => {
      const valA = getNestedValue(a, parameter) ?? '';
      const valB = getNestedValue(b, parameter) ?? '';

      return orderBy === 'ASC'
        ? String(valA).localeCompare(String(valB))
        : String(valB).localeCompare(String(valA));
    });
  };

}
export const configMapping = (userData = {}) => {
  const documentUINames = documentUINamesConstant(userData);

  return {
    // vendors
    address_required: 'Keep address mandatory for adding new vendors',
    restrict_vendor_gst_update: 'Restrict GST number update in Vendor',
    gst_details_in_transaction: 'GST Details in Transaction (As Per Master/ As per Document)',
    allow_user_to_create_update_seller_with_duplicate_name: 'Allow user to create/update vendor with duplicate name',
    // Customers
    restrict_customer_gst_update: 'Restrict GST number update in Customer',
    allow_user_to_create_update_customer_with_duplicate_name: 'Allow user to create/update customer with duplicate name',

    // materials request
    purchase_request_target_price_required: 'Keep target price mandatory while creating materials request',
    allow_pr_creation_from_other_departments: 'Allow users to create materials requests on behalf of other departments',
    allow_excess_quantity_for_so_to_pr: `Allow users to create excess quantity materials request for ${documentUINames.salesOrderUIName}`,
    allow_st_from_other_departments: 'Allow Stock Transfer from other departments',
    stock_availability_visible: 'Make stock availability and incoming stock visible while creating and viewing material request',
    allow_excess_transfer_from_material_request: 'Allow excess transfer from Material Request',
    enable_issuer_department_restriction_on_view_mr: 'Restrict MR View to Issuer Department Only',
    enable_st_creation_from_mr: 'Enable Stock Transfer creation from MR',
    enable_pi_creation_from_mr: 'Enable Purchase Indent creation from MR',
    enable_po_creation_from_mr: 'Enable Purchase Order creation from MR',
    // purchase order
    seller_address_required: 'Keep vendor address mandatory for creating purchase order',
    enable_po_price_more_than_pr: 'Enable users to create purchase orders at higher price than target price in materials request',
    delivery_date_mandatory: 'Keep delivery date mandatory in the document',
    auto_apply_t_and_c_while_creating_po_from_pr: 'Auto apply terms & conditions in PO while creating from material request',
    enable_purchasing_group: 'Enable Purchasing Group',
    // goods receiving
    flexible_qty_wrt_po: 'Allow users to create excess materials receipt of higher quantity than purchase order',
    allow_future_date_grn: 'Allow users to create materials receipt in future date',
    allow_past_date_grn: 'Allow users to create back dated materials receipt',
    is_e_way_bill_mandatory: 'Keep e-way bill number mandatory for creating goods receipt',
    make_invoice_number_unique_against_vendor: 'Keep vendor invoice number unique against seller',
    //ap invoice
    is_enabled: 'Enable Account Payable Invoice.',
    // grn_due_date_required: 'keep due date mandatory ',
    grn_invoice_number_mandatory: 'Keep invoice number of the vendor mandatory',
    enable_adhoc_po_grn: 'Allow addition of ADHOC items while creating an goods receipt from purchase order',
    allow_to_create_adhoc_grn: 'Allow users to create adhoc grn',
    allow_grn_date_before_invoice_date: 'Allow GRN date to be before vendor invoice date',

    disable_price_change_in_grn_from_po: 'Restrict price change in GRN created against a Purchase Order',

    // invoice
    allow_invoice_to_print_multiple_copies: 'Enable downloading multiple copies of invoices',
    disable_price_change_in_invoice_from_so: `Restrict price change in Invoice created against a ${documentUINames.salesOrderUIName}`,
    show_total_quantity: 'Show Total Quantity',
    auto_calculate_selling_price_from_selected_batches: 'Auto calculate selling price from selected batches',
    restrict_invoice_of_other_acc_manager: 'Restrict Invoice of Other Account Managers',
    calculate_total_quantity_with_parent_quantity_for_bundle_products: 'Calculate total quantity with parent quantity for bundle products',

    // inventory
    department_level_stock: 'Track stock for every department',
    show_stock_to_other_department: 'Allow users to see the stock level in other departments',
    is_inventory_location_mandatory: 'Keep warehouse shelf code mandatory while creating stock inwards',
    is_hsn_code_mandatory: 'Keep HSN Code mandatory while creating product',
    is_internal_reference_id_mandatory: 'Keep Internal Reference ID mandatory while creating product',
    is_category_mandatory: 'Keep Category mandatory while creating product',
    is_barcode_mandatory: 'Keep Barcode mandatory while creating product',
    flexible_qty_from_so: `Enable flexible quantity from ${documentUINames.salesOrderUIName}`,
    flexible_qty_from_invoice: 'Enable flexible quantity from invoice',
    negative_inventory_allowed: 'Allow negative inventory',
    quantity_precision: 'Digits of precision for printing quantities in inventory',
    price_precision: 'Digits of precision for prices',
    enable_cart_checkout: 'Enable cart checkout',
    auto_approve_current_user_workflow: 'Auto approve workflow for current user',
    department_wise_visibility: 'Restrict users from viewing transactions of other departments',
    auto_send_expired_batch_for_quality_recheck: 'Automatically send expired batch for quality recheck',
    allow_user_to_create_update_with_duplicate_name: 'Allow user to create/update product with duplicate name',
    allow_user_to_create_update_with_duplicate_ref_product_code: 'Allow user to create/update product with duplicate internal sku code',
    automatically_mark_expired_batch_as_rejected_batch: 'Automatically mark expired batch as rejected batch',
    user_limit: 'Set Limit for Users',
    business_unit_limit: 'Set Limit for Business Units',
    create_business_unit: 'Allow Creation of Business Units',
    organisation_configurable: 'Enable Organisation Configuration',
    create_UOM: 'Allow Creation of Units of Measurement (UOM)',
    update_UOM: 'Allow Update of Units of Measurement (UOM)',
    create_taxes: 'Allow Creation of Taxes',
    custom_inputs: 'Enable Custom Inputs',
    disable_shopify_stock_updates: 'Disable stock updates to Shopify(Only applicable if Shopify is connected)',
    enable_location_based_stock: 'Enable warehouse shelf/racks for inventory',
    is_batch_number_editable: 'Keep batch number editable while creating new batches',
    po_expiry_date: 'Enable document expiry date on purchase orders',
    default_expiry_days: 'Default duration for expiry of purchase orders',
    delivery_date_offset: 'Set default offset for delivery date',
    allow_manufacturing_order_more_than_sales_order: `Allow manufacturing order Quantity to exceed ${documentUINames.salesOrderUIName} quantity`,
    allow_flexible_consumption_in_mo: 'Allow flexible consumption of raw materials in manufacturing orders',
    default_costing_method: 'Set default costing method for manufacturing orders',
    enable_auto_generation_of_ar_number: 'Enable auto generation of ar number',
    enable_internal_sku_code: 'Enable Internal SKU Code',
    enable_internal_ref_code: 'Enable Internal Reference Code',

    // sales estimate
    restrict_estimate_of_other_acc_manager: `Restrict ${documentUINames.estimateUIName} of Other Account Managers`,

    // packing slip
    restrict_packing_slip_of_other_acc_manager: 'Restrict Packing Slip of Other Account Managers',

    // sales order
    restrict_orders_of_other_acc_manager: `Restrict ${documentUINames.salesOrderUIName}s of Other Account Managers`,
    is_price_list_mandatory: 'Keep Price List selection mandatory while creating document',
    round_off_method: 'Round Off Method',
    // calculate_total_quantity_with_parent_quantity_for_bundle_products: 'Calculate total quantity with parent quantity for bundle products',

    // stock transfer
    create_grn_incase_of_internal_stock_transfers: 'Do not allow inter department stock transfer without creating goods receiving note',

    //  manufacturing Order
    disable_quantity_change_in_mo: 'Restrict Quantity Change in manufacturing orders',
    disable_price_change_in_mo: 'Restrict Price Change in manufacturing orders',
    enable_time_tracking_in_job_cards: 'Enable time tracking in job cards',
    allow_user_to_update_start_time_and_end_time: 'Allow user to update start time and end time',
    enable_mr_creation_from_mo: 'Enable MR creation from MO',
    enable_pi_creation_from_mo: 'Enable Purchase Indent creation from MO',
    reserve_grn_qty_for_mr_when_mr_is_created_from_mo: 'Create a reservation for the GRN when the GRN source Material Request is created from a Manufacturing Order',
    reserve_grn_qty_for_pi_when_pi_is_created_from_mo: 'Create a reservation for the GRN when the GRN source Purchase Indent is created from a Manufacturing Order',
    // Quality Checks
    only_assignees_can_approve_quality_check: 'Only Assignees can approve Quality Checks',

    is_shipping_address_mandatory: 'Keep shipping address mandatory',
    is_billing_address_mandatory: 'Keep billing address mandatory',
    //mrp
    enable_mr_creation_from_mrp: 'Enable MR creation from MRP',
    enable_pi_creation_from_mrp: 'Enable Purchase Indent creation from MRP',
    enable_po_creation_from_mrp: 'Enable Purchase Order creation from MRP',
  };
};

export default new Helpers();
