import React, { useEffect, useState } from 'react';
import { Popconfirm, Table, Tooltip } from 'antd';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import './style.scss';
import PRZButton from '@Components/Common/UI/PRZButton';
import { defaultButtonTypes } from '@Uilib/h3Button';
import PRZDrawer from '@Components/Common/UI/PRZDrawer';
import TnCForm from './TnCForm';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
// ─── Actions
import CMActions from '@Actions/settings/cmActions';

const TnCConfig = ({ label, entityName, user, getCM, deleteCM, deleteCMLoading }) => {

  const [openTncDrawer, setOpenTncDrawer] = useState(false);
  const [selectedTnC, setSelectedTnC] = useState(null);
  const [call, setCall] = useState(false);
  const [tncData, setTncData] = useState([]);
  const [loading, setLoading] = useState(false);

  const columns = [
    {
      title: 'Title',
      key: 'label',
      render: (record) => (
        <div>
          {record?.title?.toProperCase()}
          {' '}
          {record?.is_default
            && (
              <span
                className="status-tag"
              >
                Default
              </span>
            )}
        </div>
      ),
    },
    {
      title: 'Terms & Conditions',
      key: 'tnc',
      render: (record) => {
        const htmlString = record?.message || '';

        // Strip HTML to get first 18 words
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlString;
        const plainText = tempDiv.textContent || tempDiv.innerText || '';
        const truncated = plainText.split(/\s+/).slice(0, 18).join(' ') + '...';

        return (
          <Tooltip
            title={
              <div
                style={{
                  maxHeight: '250px',
                  overflowY: 'auto',
                  maxWidth: '700px',
                }}
                dangerouslySetInnerHTML={{ __html: htmlString }}
              />
            }
          >
            <div dangerouslySetInnerHTML={{ __html: truncated }} />
          </Tooltip>
        );
      },
    },
    {
      title: 'Actions',
      key: 'action',
      render: (_, record) => (
        <div
          style={{
            display: 'flex',
            justifyContent: 'flex-start',
            alignItems: 'center',
            gap: '12px',
          }}
        >
          <div
            style={{ color: '#1890ff' }}
            className="roundIconButton"
            onClick={() => {
              setOpenTncDrawer(true);
              setSelectedTnC(record);
            }}
          >
            <EditOutlined />
          </div>
          {record?.is_default ? (
            <div
              className={`tnc_deleteButton ${record?.is_default ? 'disabledButton' : ''}`}
              style={{
                cursor: record?.is_default ? 'not-allowed' : 'pointer',
                opacity: record?.is_default ? '0.5' : '1',
              }}
            >
              <DeleteOutlined />
            </div>
          ) : (
            <Popconfirm
              placement="topRight"
              title="Are you sure you want to delete this TnC?"
              onConfirm={() => deleteCM({ entity_level_message_id: record?.entity_level_message_id },
                () => setCall(!call))}
              okText="Yes"
              cancelText="No"
            >
              <div
                className="tnc_deleteButton"
                style={{ cursor: 'pointer' }}
              >
                <DeleteOutlined />
              </div>
            </Popconfirm>
          )}
        </div >
      ),
    }
  ];

  useEffect(() => {
    setLoading(true);
    getCM(user?.tenant_info?.org_id, '', entityName, '', '', user?.tenant_info?.tenant_id, 'TERMS_AND_CONDITIONS', true, '', (t_and_c_Data) => {
      if (t_and_c_Data) {
        setTncData(t_and_c_Data);
        setLoading(false);
      }
    });
  }, [user, call, entityName]);

  return (
    <div>
      <Table
        title={() => (
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            {tncData?.length > 0 ? `${tncData?.length} Terms and Conditions` : 'No Terms and Conditions Available'}
            <PRZButton
              buttonType={defaultButtonTypes.BLUE_ROUNDED}
              text="+ New TnC"
              onClick={() => setOpenTncDrawer(true)}
            >
              + New Terms and Conditions
            </PRZButton>
          </div>
        )}
        dataSource={tncData}
        columns={columns}
        size="small"
        bordered={false}
        pagination={false}
        loading={deleteCMLoading || loading}
      />
      <PRZDrawer
        open={openTncDrawer}
        onClose={() => {
          setOpenTncDrawer(false);
          setSelectedTnC(null);
        }}
        width={650}
        headerWidth={600}
        title={`${selectedTnC ? 'Update' : 'Create'} Terms and Conditions for ${label}`}
      >
        <TnCForm
          callback={() => {
            setOpenTncDrawer(false);
            setSelectedTnC(null);
            setCall(!call);
          }}
          entityName={entityName}
          selectedTnC={selectedTnC}
        />
      </PRZDrawer>
    </div>
  );
};

const mapStateToProps = ({ UserReducers, CMReducers }) => ({
  user: UserReducers.user,
  deleteCMLoading: CMReducers.deleteCMLoading,
});

const mapDispatchToProps = (dispatch) => ({
  getCM: (orgId, entityId, entityType, page, limit, tenantId, actionType, isActive, isArchived, callback) => dispatch(CMActions.getCM(orgId, entityId, entityType, page, limit, tenantId, actionType, isActive, isArchived, callback)),
  deleteCM: (payload, callback) => dispatch(CMActions.deleteCM(payload, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(TnCConfig));
