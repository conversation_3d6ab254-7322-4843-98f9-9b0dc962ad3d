/* eslint-disable no-nested-ternary */
import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import Decimal from 'decimal.js';
import { Table, Checkbox, Select, Switch, Popconfirm, Tooltip } from 'antd';
import dayjs from 'dayjs';
import H3Text from '@Uilib/h3Text';
import H3FormInput from '@Uilib/h3FormInput';
import Helpers from '@Apis/helpers';
import { INFINITE_EXPIRY_DATE, QUANTITY, DEFAULT_CUR_ROUND_OFF } from '@Apis/constants';
import Crown from '@Images/crown2.png';
import './style.scss';
import HideValue from '../../../../../../Common/RestrictedAccess/HideValue';
import ViewBatchCustomFields from '../../../../../../Common/ViewBatchCustomFields';
import PRZSelect from '../../../../../../Common/UI/PRZSelect';

const { Option } = Select;

class RawMaterialBatchSelector extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isAllBatchesSelected: true,
    };
  }

  componentDidMount() {
    // this.onToggleAllBatch();
  }

  componentDidUpdate(prevProps) {
    if (prevProps?.batches !== this.props?.batches) {
      const allSelected = this.props?.batches?.length > 0 &&
        this.props?.batches?.every(batch => batch?.batch_in_use);
      if (this.state?.isAllBatchesSelected !== allSelected) {
        this.setState({ isAllBatchesSelected: allSelected });
      }
    }
  }

  onToggleAllBatch = () => {
    const { batches, onToggleBatch, itemsRow } = this.props;
    const { isAllBatchesSelected } = this.state;
    const shouldSelectAll = !isAllBatchesSelected;

    this.setState({ isAllBatchesSelected: shouldSelectAll });
    batches?.forEach((batch) => {
      if (batch?.batch_in_use !== shouldSelectAll) {
        onToggleBatch(batch, itemsRow);
      }
    });
  };

  getColumns() {
    const { priceMasking, hideSelectAllBatchesOption } = this.props;
    const { isDataMaskingPolicyEnable, isHideCostPrice } = priceMasking;
    const { isAllBatchesSelected } = this.state;
    const rawMatrialBatchColumns = [
      {
        title: (
          !hideSelectAllBatchesOption ? (
            <Checkbox
              checked={isAllBatchesSelected}
              onChange={this.onToggleAllBatch}
            />
          ) : null
        ),
        fixed: 'left',
        render: (record) => {
          const { onToggleBatch, itemsRow } = this.props;
          return (
            <Checkbox
              checked={record?.batch_in_use}
              onChange={(event) => onToggleBatch(record, itemsRow, event.target.checked)}
              disabled={record?.is_reserved}
            />
          );
        },
        hidden: false,
        width: '36px',
      },
      {
        title: 'Batch#',
        render: (text, record) => {
          const { org, itemsRow } = this.props;
          const batchConfig = org?.organisation?.[0]?.inventory_config?.settings?.batch_management;
          return (
            <div>
              <H3Text text={record?.custom_batch_number || record?.batch_number} style={{ fontWeight: '500' }} />
              <div className="table-subscript">
                {record?.lot_number && batchConfig?.lot_number_is_enabled && (
                  <H3Text text={(
                    <span>
                      <span style={{ color: 'rgb(30, 30, 30)' }}>Lot#</span>
                      {' '}
                      {record?.lot_number}
                    </span>
                  )}
                  />
                )}
                {record?.ar_number && batchConfig?.ar_number_is_enabled && (
                  <H3Text text={(
                    <span>
                      <span style={{ color: 'rgb(30, 30, 30)' }}>AR#</span>
                      {' '}
                      {record?.ar_number}
                    </span>
                  )}
                  />
                )}
                {record?.batch_barcode && batchConfig?.batch_barcode_is_enabled && (
                  <H3Text text={(
                    <span>
                      <span style={{ color: 'rgb(30, 30, 30)' }}>Barcode</span>
                      {' '}
                      {record?.batch_barcode}
                    </span>
                  )}
                  />
                )}
                {record?.batch_inward_date && batchConfig?.batch_inward_date_is_enabled && (
                  <H3Text text={(
                    <span>
                      <span style={{ color: 'rgb(30, 30, 30)' }}>Inward Date</span>
                      {' '}
                      {dayjs(record?.batch_inward_date).format('DD MMM, YY')}
                    </span>
                  )}
                  />
                )}
                {record?.roll_no && batchConfig?.roll_no_is_enabled && (
                  <H3Text text={(
                    <span>
                      <span style={{ color: 'rgb(30, 30, 30)' }}>Roll No#</span>
                      {' '}
                      {record?.roll_no}
                    </span>
                  )}
                  />
                )}
                {record?.mrp > 0 && batchConfig?.mrp_is_enabled && (
                  <H3Text text={(
                    <span>
                      <span style={{ color: 'rgb(30, 30, 30)' }}>MRP</span>
                      {' '}
                      {this.props.MONEY(record?.mrp)}
                    </span>
                  )}
                  />
                )}
                {record?.selling_price > 0 && batchConfig?.selling_price_is_enabled && (
                  <H3Text text={(
                    <span>
                      <span style={{ color: 'rgb(30, 30, 30)' }}>SP</span>
                      {' '}
                      {(isDataMaskingPolicyEnable && isHideSellingPrice) ? <HideValue showPopOver popOverMessage={"You don't have access to view selling price"} /> : this.props.MONEY(record?.selling_price)}
                    </span>
                  )}
                  />
                )}
                {record?.landed_cost > 0 && batchConfig?.landed_cost_is_enabled && (
                  <H3Text text={(
                    <span>
                      <span style={{ color: 'rgb(30, 30, 30)' }}>Landed Cost</span>
                      {' '}
                      {(isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver popOverMessage={"You don't have access to view landed cost"} /> : this.props.MONEY(record?.landed_cost)}
                    </span>
                  )}
                  />
                )}
                {record?.freight_cost > 0 && batchConfig?.freight_cost_is_enabled && (
                  <H3Text text={(
                    <span>
                      <span style={{ color: 'rgb(30, 30, 30)' }}>Freight Cost</span>
                      {' '}
                      {this.props.MONEY(record?.freight_cost)}
                    </span>
                  )}
                  />
                )}
                {record?.other_cost > 0 && batchConfig?.other_cost_is_enabled && (
                  <H3Text text={(
                    <span>
                      <span style={{ color: 'rgb(30, 30, 30)' }}>Other Cost</span>
                      {' '}
                      {this.props.MONEY(record?.other_cost)}
                    </span>
                  )}
                  />
                )}
                {record?.margin > 0 && batchConfig?.margin_is_enabled && (
                  <H3Text text={(
                    <span>
                      <span style={{ color: 'rgb(30, 30, 30)' }}>Margin</span>
                      {' '}
                      {parseFloat(Number(record?.margin).toFixed(DEFAULT_CUR_ROUND_OFF))}%
                    </span>
                  )}
                  />
                )}
                {record?.brand && batchConfig?.brand_is_enabled && (
                  <H3Text text={(
                    <span>
                      <span style={{ color: 'rgb(30, 30, 30)' }}>Brand</span>
                      {' '}
                      {record?.brand}
                    </span>
                  )}
                  />
                )}
                {record?.mfg_batch_no && batchConfig?.mfg_batch_no_is_enabled && (
                  <H3Text text={(
                    <span>
                      <span style={{ color: 'rgb(30, 30, 30)' }}>Mfg batch #</span>
                      {' '}
                      {record?.mfg_batch_no}
                    </span>
                  )}
                  />
                )}
                {record?.manufacturing_date && batchConfig?.manufacturing_date_is_enabled && (
                  <H3Text text={(
                    <span>
                      <span style={{ color: 'rgb(30, 30, 30)' }}>Mfg Date</span>
                      {' '}
                      {Helpers.batchDateFormatter(record?.manufacturing_date, itemsRow?.manufacturingDateFormat)}
                    </span>
                  )}
                  />
                )}
                {record?.next_qc_date && batchConfig?.next_qc_date_is_enabled && (
                  <H3Text text={(
                    <span>
                      <span style={{ color: 'rgb(30, 30, 30)' }}>Next QC Date</span>
                      {' '}
                      {dayjs(record?.next_qc_date).format('DD MMM, YY')}
                    </span>
                  )}
                  />
                )}
                {record?.last_qc_date && batchConfig?.last_qc_date_is_enabled && (
                  <H3Text text={(
                    <span>
                      <span style={{ color: 'rgb(30, 30, 30)' }}>Last QC Date</span>
                      {' '}
                      {dayjs(record?.last_qc_date).format('DD MMM, YY')}
                    </span>
                  )}
                  />
                )}
              </div>
              {record?.is_reserved && (
                <div className="status-tag reservation-tag">
                  Reserved
                </div>
              )}
            </div>
          );
        },
        width: '150px',
      },
      {
        title: 'Expiry',
        render: (text, record) => (record?.expiry_date !== INFINITE_EXPIRY_DATE ? Helpers.batchDateFormatter(record?.expiry_date, this.props.itemsRow?.expiryDateFormat) : '-'),
        width: '80px',
      },
      {
        title: 'Shelf Life',
        render: (text, record) => {
          const { expiryDays } = this.props;
          const expiryMoment = dayjs(dayjs(record.expiry_date).format('YYYY-MM-DD'));
          let shelfLife = 0;
          if (dayjs().isSameOrBefore(expiryMoment)) {
            shelfLife = expiryMoment.diff(dayjs(dayjs().format('YYYY-MM-DD')), 'days');
          }
          let shelfLifePct = Math.round(((shelfLife / expiryDays) * 100)) > 100 ? 100 : Math.round(((shelfLife / expiryDays) * 100));
          if (record?.expiry_date === INFINITE_EXPIRY_DATE) shelfLifePct = 100;
          if (expiryDays < 0) {
            return (
              <div style={{ width: '220px' }}>
                No Expiry (100%)
              </div>
            );
          }
          return (
            (
              <div className="batch-shelf-life">
                <div
                  className="progress-line__wrapper"
                  style={{
                    width: '120px',
                    backgroundColor: shelfLifePct < 25 ? 'rgba(216,51,15,0.2)' : shelfLifePct > 75 ? 'rgba(23,181,77,0.2)' : 'rgba(204,185,10,0.2)',
                  }}
                >
                  <div
                    className="progress-line"
                    style={{
                      width: `${shelfLifePct}%`,
                      backgroundColor: shelfLifePct < 25 ? '#D8330F' : shelfLifePct > 75 ? '#17B54D' : '#CCB90A',
                    }}
                  />
                </div>
                <span>
                  {record?.expiry_date === INFINITE_EXPIRY_DATE ? 'NA' : `${shelfLife} Days (${shelfLifePct}%)`}
                </span>
              </div>
            )
          );
        },
        width: '130px',
      },
      {
        title: 'Shelf/Rack',
        render: (text, record) => {
          return (
            <Tooltip title={`${record?.inventory_location_path ?? ''}`}>
              <div>
                {record?.inventory_location_path ?? '-'}
              </div>
            </Tooltip>
          );
        },
        width: '90px',
      },
      {
        title: 'Batch Quality',
        render: (text, record) => (
          <div className="qr__status" style={{ backgroundColor: record?.is_rejected_batch ? '#ee2232' : '#2fbc6e' }}>
            {record?.is_rejected_batch ? 'Bad' : 'Good'}
          </div>
        ),
        responsive: ['sm', 'md', 'lg', 'xxl'],
        // className: this.props.innerBatchSelector ? 'display-none' : '',
        hidden: false,
        width: '90px',
      },
      {
        title: 'Cost Price',
        render: (text, record) => (record?.cost_price ? ((isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver popOverMessage={"You don't have access to view cost price"} /> : this.props.MONEY(record?.cost_price)) : '-'),
        width: '90px',
      },
      {
        title: 'Quantity',
        render: (text, record) => {
          const { uomInfo } = this.props;
          return (
            <div>{`${QUANTITY(record?.available_qty, uomInfo?.precision)} ${uomInfo?.uqc?.toProperCase()}`}</div>
          );
        },
        width: '90px',
      },
      {
        title: 'Custom Fields',
        render: (record) => (<ViewBatchCustomFields customFields={record?.custom_fields} />),
        responsive: ['sm', 'md', 'lg', 'xxl'],
        width: '150px'
      },
      {
        title: () => {
          const batchConsumedQtyEditable = this.props.user?.tenant_info?.inventory_config?.settings?.enable_batch_consumed_qty_editable;
          return (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div>Consumed</div>
              {!batchConsumedQtyEditable && (
                <Popconfirm
                  placement="topRight"
                  title="This feature is not accessible within your current plan to use this feature contact us."
                  onConfirm={() => window.Intercom('showNewMessage')}
                  okText="Contact Us"
                  cancelText="Cancel"
                >
                  <img
                    className="heading-restrict"
                    src={Crown}
                    alt="premium"
                    style={{
                      marginLeft: '6px',
                      marginTop: '-3px'
                    }}
                  />
                </Popconfirm>
              )}
            </div>
          );
        },
        render: (text, record) => {
          const { uomInfo, updateBatchValue, user } = this.props;
          const batchConsumedQtyEditable = user?.tenant_info?.inventory_config?.settings?.enable_batch_consumed_qty_editable;

          return (
            <React.Fragment>
              {batchConsumedQtyEditable && (
                <H3FormInput
                  value={record?.consumed_qty || ''}
                  type="text"
                  containerClassName={`${(Number(record?.consumed_qty) < 0 || Number(record?.consumed_qty) > Number(record?.available_qty)) ? 'form-error__input' : 0}`}
                  labelClassName="orgFormLabel"
                  inputClassName="orgFormInput"
                  onChange={(event) => {
                    updateBatchValue(event.target.value, 'consumed_qty', record?.batch_id);
                  }}
                  disabled={!batchConsumedQtyEditable || !record?.batch_in_use}
                />
              )}
              <div style={{ color: record?.consumed_qty ? 'red' : 'black' }}>{`${record?.consumed_qty ? '-' : ''}${QUANTITY(record?.consumed_qty || '0', uomInfo?.precision)} ${uomInfo?.uqc?.toProperCase()}`}</div>
            </React.Fragment>
          );
        },
        width: '120px',
      },
    ];
    return rawMatrialBatchColumns;
  }

  getBatches(oldBatches, quantity) {
    const batches = oldBatches;
    let remainingQty = new Decimal(quantity || 0);

    // Reset consumed_qty
    for (let i = 0; i < batches?.length; i++) {
      batches[i].consumed_qty = 0;
    }

    for (let i = 0; i < batches?.length; i++) {
      const consumed = new Decimal(batches[i].consumed_qty || 0);
      const available = new Decimal(batches[i].available_qty || 0);

      if (remainingQty.gt(0) && consumed.lt(available)) {
        const consumeQty = Decimal.min(available, remainingQty);
        batches[i].consumed_qty = consumeQty.toNumber();
        remainingQty = remainingQty.minus(consumeQty);
      } else {
        batches[i].consumed_qty = 0;
        // No deduction necessary since it's 0
      }
    }

    return batches;
  }


  render() {
    const {
      batches, defaultExpandAllRows, batchMethod, updateBatchConsumptionMethod, allStock, updateAllStock
    } = this.props;
    const rawMatrialBatchColumns = this.getColumns()?.filter((column) => !column.hidden);

    return (
      <div className="rm-batch-selector-wrapper">
        <div className="flex-display">
          <H3Text text="Available Stock Batches" className="rm-batch-selector-title" />
          <div style={{ marginLeft: 'auto' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div className="rm-batch-selector-method">
                <PRZSelect
                  filterOption={false}
                  value={batchMethod}
                  onChange={(value) => {
                    updateBatchConsumptionMethod(value);
                  }}
                >
                  <Option key="FEFO" value="FEFO">FEFO</Option>
                  <Option key="FIFO" value="FIFO">FIFO</Option>
                </PRZSelect>
              </div>
              <div>
                {String(allStock) && (<Switch checked={allStock} onChange={() => updateAllStock(!allStock)} checkedChildren="Good Stock Only" unCheckedChildren="All Stock Batches" />)}
              </div>
            </div>
          </div>
        </div>
        <Table
          size="small"
          columns={rawMatrialBatchColumns}
          dataSource={allStock ? batches?.filter((batch) => !batch?.is_rejected_batch) : batches}
          pagination={false}
          bordered
          tableLayout='fixed'
        />
      </div>
    );
  }
}

const mapStateToProps = ({
  UserReducers,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  org: UserReducers.org,
  priceMasking: UserReducers.priceMasking,
});

const mapDispatchToProps = (dispatch) => ({
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(RawMaterialBatchSelector));
