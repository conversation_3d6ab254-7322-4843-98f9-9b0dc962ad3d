import React, { Component, Fragment } from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import ViewCustomFields from '@Components/Common/CustomField/ViewCustomFields';
import './style.scss';
import { Drawer, Modal, Tooltip } from 'antd';
import { EditOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import Helpers from '@Apis/helpers';
import closeIcon from '@Images/icons/icon-close-blue.png';
import H3Image from '@Uilib/h3Image';
import H3Text from '@Uilib/h3Text';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEyeSlash } from '@fortawesome/free-solid-svg-icons';
import UpdateTallyLedger from '../../../../Common/UpdateTallyLedger/UpdateTallyProduct';
import HideValue from '../../../../Common/RestrictedAccess/HideValue';
/**

 *
 */
class ProductInformation extends Component {
  /**
   * Constructor
   *
   * @param props
   */
  constructor(props) {
    super(props);
    this.state = {
    };
  }

  /**
   *
   * @return {*}
   */
  render() {
    const {
      selectedProduct, user, MONEY, callback, priceMasking, przModalView,
    } = this.props;
    const automaticArNumberEnabled = user?.tenant_info?.inventory_config?.settings?.enable_auto_generation_of_ar_number;
    const { isModalOpen } = this.state;
    const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;
    const enableInternalRefCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_ref_code;
    return (
      <Fragment>
        <div className="product-info__wrapper">
          <div className="ant-row">
            <div className="ant-col-md-12 ant-col-xs-24">
              <div className="ant-col-md-24 ant-col-xs-24">
                <div className="product-info__block">
                  <div className="product-info__heading">
                    Product Type
                  </div>
                  <div className="product-info__value">
                    {selectedProduct?.product_type?.toProperCase() || '-'}
                  </div>
                </div>
              </div>
              <div className="ant-col-md-24 ant-col-xs-24">
                <div className="product-info__block">
                  <div className="product-info__heading">
                    Marketplace Product
                  </div>
                  <div className="product-info__value">
                    {selectedProduct?.is_marketplace_product ? 'Yes' : 'No'}
                  </div>
                </div>
              </div>
              <div className="ant-col-md-24 ant-col-xs-24">
                <div className="product-info__block">
                  <div className="product-info__heading">
                    Product Category
                  </div>
                  <div className="product-info__value">
                    {selectedProduct?.product_category_info?.category_path && (
                      <Tooltip
                        overlay={(
                          <div
                            style={{
                              color: 'rgb(0, 0, 0)',
                              fontSize: '12px',
                              fontWeight: '500',
                              padding: '2px',
                            }}
                          >
                            {selectedProduct.product_category_info.category_path.join(' / ')}
                          </div>
                        )}
                        color="rgb(234, 242, 254)"
                      >
                        {' '}
                        {/* Tooltip wrapping the div */}
                        <div
                          className="inventory-qr__status"
                          style={{ backgroundColor: '#3aa5a6' }}
                        >
                          {/* Displaying only the last category name with ellipsis if path is long */}
                          {selectedProduct?.product_category_info ? (
                            `.../${selectedProduct?.product_category_info?.product_category_name}`
                          ) : (
                            ''
                          )}
                        </div>
                      </Tooltip>
                    )}
                  </div>
                </div>

              </div>
              <div className="ant-col-md-24 ant-col-xs-24">
                <div className="product-info__block">
                  <div className="product-info__heading">
                    Unit of Measurement
                  </div>
                  <div className="product-info__value">
                    {`${selectedProduct?.uom_info?.uom_name?.toProperCase()} (${selectedProduct?.uom_info?.uqc?.toProperCase()})` || '-'}
                  </div>
                </div>
              </div>
              <div className="ant-col-md-24 ant-col-xs-24">
                <div className="product-info__block">
                  <div className="product-info__heading">
                    Purchase UOM
                  </div>
                  <div className="product-info__value">
                    {`${selectedProduct?.purchase_uom_info?.uom_name?.toProperCase()} (${selectedProduct?.purchase_uom_info?.uqc?.toProperCase() || ''})` || '-'}
                  </div>
                </div>
              </div>
              {user?.tenant_info?.global_config?.settings?.enable_secondary_uom && selectedProduct?.secondary_uom_info && (
                <div className="ant-col-md-24 ant-col-xs-24">
                  <div className="product-info__block">
                    <div className="product-info__heading">
                      Secondary UOM
                    </div>
                    <div className="product-info__value">
                      {`${selectedProduct?.secondary_uom_info?.uom_name?.toProperCase() || 'N/A'} (${selectedProduct?.secondary_uom_info?.uqc?.toProperCase() || ''})` || '-'}
                    </div>
                  </div>
                </div>
              )}
              <div className="ant-col-md-24 ant-col-xs-24">
                <div className="product-info__block">
                  <div className="product-info__heading">
                    Minimum Floor Quantity
                  </div>
                  <div className="product-info__value">
                    {selectedProduct?.threshold_qty || 0}
                  </div>
                </div>
              </div>
              {automaticArNumberEnabled && (
                <div className="ant-col-md-24 ant-col-xs-24">
                  <div className="product-info__block">
                    <div className="product-info__heading">
                      AR Number Prefix
                    </div>
                    <div className="product-info__value">
                      {selectedProduct?.product_info?.ar_number_prefix || ''}
                    </div>
                  </div>
                </div>
              )}
              {automaticArNumberEnabled && (
                <div className="ant-col-md-24 ant-col-xs-24">
                  <div className="product-info__block">
                    <div className="product-info__heading">
                      AR Number Counter
                    </div>
                    <div className="product-info__value">
                      {selectedProduct?.product_info?.ar_number_counter || ''}
                    </div>
                  </div>
                </div>
              )}
              <div className="ant-col-md-24 ant-col-xs-24">
                <div className="product-info__block">
                  <div className="product-info__heading">
                    Shelf Life
                  </div>
                  <div className="product-info__value">
                    {selectedProduct?.product_info?.expiry_days < 0 ? 'Non Perishable' : `${selectedProduct?.product_info?.expiry_days || 0} Days`}
                  </div>
                </div>
              </div>
              {selectedProduct?.ref_product_code && enableInternalRefCode && (
                <div className="ant-col-md-24 ant-col-xs-24">
                  <div className="product-info__block">
                    <div className="product-info__heading">
                      Internal Reference
                    </div>
                    <div className="product-info__value">
                      {selectedProduct?.ref_product_code || '-'}
                    </div>
                  </div>
                </div>
              )}
              {user?.tenant_info?.zoho_branch_id && (
                <div className="ant-col-md-24 ant-col-xs-24">
                  <div className="product-info__block">
                    <div className="product-info__heading">
                      Zoho Reference ID
                    </div>
                    <div className="product-info__value">
                      {selectedProduct?.zoho_sku_code
                        ? (
                          <a href={`https://books.zoho.in/app#/inventory/items/${selectedProduct?.zoho_sku_code}`} target="_blank" rel="noreferrer">
                            {selectedProduct?.zoho_sku_code || '-'}
                          </a>
                        )
                        : (
                          <div>
                            {' '}
                            -
                          </div>
                        )}

                    </div>
                  </div>
                </div>
              )}
              {user?.tenant_info?.shopify_location_id && (
                <div className="ant-col-md-24 ant-col-xs-24">
                  <div className="product-info__block">
                    <div className="product-info__heading">
                      Shopify ID
                    </div>
                    <div className="product-info__value">
                      {selectedProduct?.product_info?.shopify_parent_id
                        ? (
                          <a href={`https://admin.shopify.com/store/${selectedProduct?.product_info?.shopify_shop_name?.split('.')?.[0]}/products/${selectedProduct?.product_info?.shopify_parent_id}/variants/${selectedProduct?.product_info?.shopify_variant_id}`} target="_blank" rel="noreferrer">
                            {selectedProduct?.product_info?.shopify_parent_id}
                          </a>
                        )
                        : (
                          <div>
                            {' '}
                            -
                          </div>
                        )}

                    </div>
                  </div>
                </div>
              )}
              {user?.tenant_info?.it_id && (
                <div className="ant-col-md-24 ant-col-xs-24">
                  <div className="product-info__block">
                    <div className="product-info__heading">
                      Tally Product Name
                    </div>
                    <div className="product-info__value">
                      <div style={{ display: 'flex', marginLeft: '3px', alignItems: 'center' }}>
                        <div className="product-info__header-title">
                          {selectedProduct?.tally_products?.[0]?.product_name}
                        </div>
                        <div>
                          {!przModalView && (
                            <div
                              className="roundIconButton"
                              onClick={() => {
                                this.setState({ isModalOpen: true });
                              }}
                            >
                              <EditOutlined />
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div className="ant-col-md-12">
              {user?.tenant_info?.global_config?.sub_modules?.barcoding?.is_active && (
                <div className="ant-col-md-24 ant-col-xs-24">
                  <div className="product-info__block">
                    <div className="product-info__heading">
                      Barcode
                    </div>
                    <div className="product-info__value">
                      {selectedProduct?.barcode}
                    </div>
                  </div>
                </div>
              )}
              <div className="ant-col-md-24 ant-col-xs-24">
                <div className="product-info__block">
                  <div className="product-info__heading">
                    HSN/SAC Code
                  </div>
                  <div className="product-info__value">
                    {selectedProduct?.product_info?.hsn_code || '-'}
                  </div>
                </div>
              </div>
              <div className="ant-col-md-24 ant-col-xs-24">
                <div className="product-info__block">
                  <div className="product-info__heading">
                    Sales Tax
                  </div>
                  <div className="product-info__value">
                    {`${selectedProduct?.tax_info?.tax_name} (${selectedProduct?.tax_info?.tax_value}%)` || '-'}
                  </div>
                </div>

              </div>
              <div className="ant-col-md-24 ant-col-xs-24">
                <div className="product-info__block">
                  <div className="product-info__heading">
                    Sales Price
                  </div>
                  <div className="product-info__value">
                    {(isDataMaskingPolicyEnable && isHideSellingPrice) ? <HideValue showPopOver popOverMessage={"You don't have access to view selling price"} /> : (
                      <React.Fragment>
                        {MONEY(selectedProduct?.selling_price || '0')}
                        <div>
                          {`(= ${MONEY((selectedProduct?.selling_price + selectedProduct?.selling_price * (Number(selectedProduct?.tax_info?.tax_value || 0) / 100)) || '0')} Incl. Taxes)`}
                        </div>
                      </React.Fragment>
                    )}
                  </div>
                </div>
              </div>
              <div className="ant-col-md-24 ant-col-xs-24">
                <div className="product-info__block">
                  <div className="product-info__heading">
                    Cost Price
                  </div>
                  <div className="product-info__value">
                    {(isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver popOverMessage={"You don't have access to view cost price"} /> : MONEY(selectedProduct?.cost_price || '0')}
                  </div>
                </div>
              </div>
              <div className="ant-col-md-24 ant-col-xs-24">
                <div className="product-info__block">
                  <div className="product-info__heading">
                    Manufacturing Date Format
                  </div>
                  <div className="product-info__value">
                    {selectedProduct?.manufacturing_date_format}
                  </div>
                </div>
              </div>
              <div className="ant-col-md-24 ant-col-xs-24">
                <div className="product-info__block">
                  <div className="product-info__heading">
                    Expiry Date Format
                  </div>
                  <div className="product-info__value">
                    {selectedProduct?.expiry_date_format}
                  </div>
                </div>
              </div>
              <div className="ant-col-md-24 ant-col-xs-24">
                <div className="product-info__block">
                  <div className="product-info__heading">
                    MRP
                  </div>
                  <div className="product-info__value">
                    {MONEY(selectedProduct?.mrp || '0')}
                  </div>
                </div>
              </div>
              <div className="ant-col-md-24 ant-col-xs-24">
                <div className="product-info__block">
                  <div className="product-info__heading">
                    Weighted Avg Price
                  </div>
                  <div className="product-info__value">
                    {(isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver popOverMessage={"You don't have access to view weighted avg price"} /> : MONEY(selectedProduct?.avg_batch_cost_price || '0')}
                  </div>
                </div>
              </div>
              <div className="ant-col-md-24 ant-col-xs-24">
                <div className="product-info__block">
                  <div className="product-info__heading">
                    Weighted Avg LC
                  </div>
                  <div className="product-info__value">
                    {(isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver popOverMessage={"You don't have access to view weighted avg landed cost"} /> : MONEY(selectedProduct?.avg_batch_landed_cost || '0')}
                  </div>
                </div>
              </div>
            </div>
            <ViewCustomFields
              isHorizontal
              customFields={selectedProduct?.custom_fields}
              wrapperClass="product-info__block"
              labelClass="product-info__heading"
              valueClass="product-info__value"
            />
          </div>
        </div>
        <Drawer
          open={isModalOpen}
          width="700px"
          mask
          onClose={() => {
            this.setState({ isModalOpen: false });
          }}
          destroyOnClose
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header" style={{ width: '665px' }}>
              <H3Text text="Update Tally Product" className="custom-drawer__title" />
              <H3Image
                src={closeIcon}
                className="custom-drawer__close-icon"
                onClick={() => {
                  this.setState({ isModalOpen: false });
                }}
              />
            </div>
            <div style={{ paddingTop: '55px' }}>
              <UpdateTallyLedger
                productSkuIds={selectedProduct?.tally_products?.[0]?.temp_tally_product_id}
                selectedProduct={Array.isArray(selectedProduct) ? selectedProduct : [selectedProduct]}
                callback={() => {
                  this.setState({ isModalOpen: false });
                  callback();
                }}
              />
            </div>
          </div>
        </Drawer>
      </Fragment>
    );
  }
}

const mapStateToProps = ({ UserReducers }) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  priceMasking: UserReducers.priceMasking,
});

const mapDispatchToProps = (dispatch) => ({
});

ProductInformation.propTypes = {
  user: PropTypes.any,
  selectedProduct: PropTypes.any,
  MONEY: PropTypes.func,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(ProductInformation));
