import React, { Component, Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { DatePicker, Drawer, Select, Upload, notification, Checkbox, Radio, Popconfirm, Tooltip, Dropdown } from 'antd';
import {
  EditFilled, PlusCircleFilled, PlusOutlined,
} from '@ant-design/icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCircleXmark, faPlusCircle, faCircleInfo, faRotateRight, faTrash } from '@fortawesome/free-solid-svg-icons';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import Constants, {
  INFINITE_EXPIRY_DATE, toISTDate, DEFAULT_CUR_ROUND_OFF,
} from '@Apis/constants';
import Crown from '@Images/crown2.png';

// Components
import RestrictedAccessMessage from '../../../Common/RestrictedAccess/RestrictedAccessMessage';
import TenantSelector from '@Components/Common/Selector/TenantSelector';
import PRZButton from '../../../Common/UI/PRZButton';
import PRZConfirmationPopover from '../../../Common/UI/PRZConfirmationPopover';
import PRZInput from '../../../Common/UI/PRZInput';
import PRZText from '../../../Common/UI/PRZText';
import PRZSelect from '../../../Common/UI/PRZSelect';
import H3Text from '@Uilib/h3Text';
import H3FormInput from '@Uilib/h3FormInput';
import AddressSelector from '@Components/Common/FormUtils/AddressSelecter';
import VendorForm from '@Components/Common/VendorForm';
import BulkUpload from '@Components/Common/BulkUpload';
import SelectExtraCharge from '../../../Admin/Common/SelectExtraCharge';
import SelectTaxType from '../../../Admin/Common/SelectTaxType';
import RichTextEditor from '../../../Common/RichTextEditor';
import SelectPaymentTerm from '../../../Common/SelectPaymentTerm';
import SelectSellerV2 from '../../../Common/SelectSellerV2';
import H3Modal from '../../../../uilib/H3Modal';
import FormLoadingSkull from '@Components/Common/formLoadingSkull';
import CurrencyConversionV2 from '../../../Common/CurrencyConversionV2';
import ErrorHandle from '../../../Common/ErrorHandle';
import SelectDepartment from '@Components/Common/SelectDepartment';
import CustomFieldV3 from '@Components/Common/CustomFieldV3';
import FreightTaxInput from '@Components/Common/FreightTaxInput';
import ChargesTaxInput from '@Components/Common/ChargesTaxInput';
import CustomDocumentInputs from '@Components/Common/CustomDocumentInputs';
import CustomDocumentColumns from '@Components/Common/CustomDocumentColumns';
import TagSelector from '@Components/Common/Selector/TagSelector';
import DocumentNumberSeqInput from '../../../Admin/Common/DocumentNumberSeqInput';
import SelectAppUser from '../../../Common/SelectAppUser';
import PurchaseIndentSelector from './PurchaseIndentSelector';

// Helpers
import Helpers from '@Apis/helpers';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import FormHelpers from '@Helpers/FormHelpers';

// Actions
import PurchaseOrderActions from '@Actions/purchaseOrderActions';
import WorkflowActions from '@Actions/workflowActions';
import SellerActions from '@Actions/sellerActions';
import AddressActions from '@Actions/addressActions';
import OfferActions from '@Actions/offerActions';
import TenantActions from '@Actions/tenantActions';
import CFV2Actions from '@Actions/configurations/cfV2Actions';
import DocConfigActions from '@Actions/docConfigActions';
import ExtraChargesActions from '@Actions/configurations/extraChargesAction';
import CurrenciesActions from '@Actions/configurations/currenciesAction';
import GrnActions from '@Actions/grnActions';
import AddToCartActions from '@Actions/addToCart/addToCartActions';
import TagActions from '@Actions/tagActions';
import TaxActions from '@Actions/taxActions';
import DepartmentActions from '@Actions/departmentActions';
import { GetPurchaseIndents } from '@Modules/purchase/purchaseIndent';

// Current File Imports
import poerrorList from './PoErrors';
import PoLines from './POLines';
import './style.scss';
import '../../../../scss/form.scss';
import TnCSelector from '@Components/Admin/Common/TnCSelector';

const uploadButton = (
  <div>
    <PlusOutlined />
    <div style={{ marginTop: 8 }}>Upload</div>
  </div>

);

class POForm extends Component {
  constructor(props) {
    super(props);
    const purchaseRequestData = props.location.state?.purchaseRequestData;
    const selectedPI = props?.selectedPI;
    const _visibleColumns = {
      PRODUCT: {
        label: 'Product',
        visible: true,
        disabled: true,
      },
      QUANTITY: {
        label: 'Quantity',
        visible: true,
        disabled: true,
      },
      UNIT: {
        label: 'Unit',
        visible: true,
        disabled: true,
      },
      UNIT_PRICE: {
        label: 'Rate',
        visible: true,
        disabled: true,
      },
      TAX: {
        label: 'Tax',
        visible: true,
        disabled: true,
      },
      DISCOUNT: {
        label: 'Discount',
        visible: true,
        disabled: true,
      },
      DELIVERY_DATE: {
        label: 'Delivery Date',
        visible: true,
        disabled: false,
      },
      LINE_TOTAL: {
        label: 'Line Total',
        visible: true,
        disabled: true,
      },
    };

    if (purchaseRequestData || selectedPI) {
      _visibleColumns.REQUESTED_QUANTITY = {
        label: 'Requested Quantity',
        visible: true,
        disabled: true,
      };
    }

    this.timeOutRef = React.createRef(null);
    this.state = {
      purchaseOrderType: {
        0: 'BLANKET_PURCHASE_ORDER',
        1: 'PURCHASE_ORDER',
        2: 'WORK_ORDER',
      },
      productType: '',
      purchaseOrderTypeValue: 1,
      seller: null,
      isMarketplaceSeller: false,
      showNewVendorModal: false,
      currentSelectedSeller: null,
      data: [
        {
          key: uuidv4(),
          asset1: '',
          product_sku_name: '',
          quantity: '',
          price: '',
          lot: 0,
          taxId: 0,
          discountType: 'Percent',
          child_taxes: [{
            tax_amount: 0,
            tax_type_name: '',
          }],
          lineCustomFields: [],
        },
      ],
      fileList: [],
      selectedTags: [],
      savedData: [],
      chargeData: [],
      paymentTerms: '',
      poDate: dayjs(),
      attachments: [],
      showNewAddressModal: false,
      addressType: '',
      checkedRecipients: true,
      toRecipients: [],
      textAreaHeight: 2,
      isLineWiseDiscount: false,
      productKey: '',
      selectedCurrencyID: '',
      selectedCurrencyName: '',
      taxTypeName: 'TCS',
      offerPrice: 0,
      poNumber: '',
      sendWhatsappNotification: false,
      tenantDepartmentId: null,
      blanketPO: false,
      charge1Name: 'Freight',
      charge1Value: 0,
      discountPercentage: 0,
      discountType: 'Percent',
      visibleColumns: _visibleColumns,
      freightTaxId: 'Not Applicable',
      freightTax: null,
      openFreightTax: false,
      openChargesTax: false,
      selectedRmBearer: true,
      isAutomaticConversionRate: null,
      currencyConversionRate: '',
      isManualControlCF: false,
      selectedTenant: props?.user?.tenant_info?.tenant_id,
      updateDocumentReason: '',
      freightTaxData: {
        child_taxes: [
          {
            tax_amount: 0,
            tax_type_name: '',
          },
        ],
      },
      //these state are using only in case of multi pi selection on po form
      selectedPurchaseIndentIds: [],
      savedSelectedPurchaseIndentIds: [],
      selectedPurchaseIndentsData: [],
      isUserReadyUpdateCase: false,
      tnCId: null,
    };
  }

  componentDidMount() {
    const {
      getSellers, user, getAddresses, purchaseWorkflows, getPurchaseWorkflows, match, getTenants, getPurchaseOrderById, getGRN, moFgLines,
      moInfo, tenantId, getTenantById, getDocConfig, getCurrencies, jwInfo, location, getDocCFV2, getCharges, getTaxes, getTenantsConfiguration, getDepartments
    } = this.props;
    const { selectedTenant } = this.state;
    const purchaseRequestData = location.state?.purchaseRequestData;
    const isCreatePage = !match.params?.poId && !location.state?.clonePoId;
    const createClonePoId = location?.state?.clonePoId;
    const clonePoTenantId = location?.state?.selectedTenantId;
    const purchaseRequestTenantId = purchaseRequestData?.issuer_tenant_id || purchaseRequestData?.tenant_id;
    if (createClonePoId) {
      getSellers('', (clonePoTenantId || purchaseRequestTenantId), 1, 30, '', () => { }, true, !!moInfo);
    }
    if (purchaseRequestTenantId) {
      const payload = {
        orgId: user?.tenant_info?.org_id,
        entityName: 'PURCHASE_ORDERS',
      };
      getTenantsConfiguration(purchaseRequestTenantId);
      getAddresses(1, purchaseRequestTenantId, 'TENANT');
      getTenantById(purchaseRequestTenantId);
      getCurrencies();
      getDocCFV2(payload);
      getSellers('', purchaseRequestTenantId, 1, 30, '', () => { }, true, !!moInfo);
    }
    if (isCreatePage && !purchaseRequestTenantId) {
      getCurrencies();
      const { seller, visibleColumns } = this.state;
      const payload = {
        orgId: user?.tenant_info?.org_id,
        entityName: 'PURCHASE_ORDERS',
      };
      if (!location?.state?.dataForPrefillPO) {
        getDocCFV2(payload, (cfData) => {
          const lineCFs = CustomFieldHelpers.getCfStructure(cfData?.data?.document_line_custom_fields?.filter((item) => item?.is_active), false);
          if (moInfo) {
            this.setState({
              ...moInfo,
              currentSelectedSeller: Number(moInfo?.currentSelectedSeller) || null,
              cfPurchaseOrdersDoc: CustomFieldHelpers.getCfStructure(cfData?.data?.document_custom_fields, true),
              cfPurchaseOrdersLine: lineCFs,
              visibleColumns: CustomFieldHelpers.updateVisibleColumns(cfData?.data?.document_line_custom_fields, visibleColumns),
              data: moFgLines?.map((item) => ({
                ...item,
                key: uuidv4(),
                lineCustomFields: CustomFieldHelpers.getCfStructure(FormHelpers.lineSystemFieldValue(cfData.data?.document_line_custom_fields, item?.po_line_custom_fields || []))?.map((i) => ({
                  ...i,
                  fieldValue: i?.fieldName === 'Rate' ? Number(item.price || 0) : (i?.fieldName === 'Quantity' ? Number(item?.quantity) : i?.fieldValue),
                })),
                lineDiscountType: 'Percent',
                showUnitDiscount: false,
                tenantDepartmentId: moInfo?.fgTenantDepartmentId,
                secondary_uom_qty: item.secondary_uom_qty,
                secondaryUomUqc: item?.product_sku_info?.secondary_uom_info?.uqc,
                secondaryUomId: item?.product_sku_info?.secondary_uom_id,
              })),
              tenantDepartmentId: moInfo?.fgTenantDepartmentId,
              selectedTenant: tenantId,
            });
          } else if (jwInfo) {
            const cfs = lineCFs?.map((i) => ({
              ...i,
              fieldValue: i?.fieldName === 'Quantity' ? Number(jwInfo?.pending_qty) : (i?.fieldName === 'Rate' ? Number(jwInfo?.route_line_charges?.find((item) => item?.charge_type === 'unit')?.charge_amount) : i?.field_value),
            }));
            const jwPoLines = [];
            let tenantDepartmentIdFromJwInfo = null;

            if (jwInfo?.output_materials?.length > 0) {
              const MoLineIdForDep = jwInfo.output_materials[0]?.mo_line_id_for_department ? jwInfo?.rm_tenant_department_id : null;
              const MoFgIdForDep = jwInfo.output_materials[0]?.mo_fg_id_for_department ? jwInfo?.fg_tenant_department_id : null;
              tenantDepartmentIdFromJwInfo = MoLineIdForDep || MoFgIdForDep;
            } else if (jwInfo?.processing_materials?.length > 0) {
              const MoLineIdForDep = jwInfo.processing_materials[0]?.mo_line_id_for_department ? jwInfo?.rm_tenant_department_id : null;
              const MoFgIdForDep = jwInfo.processing_materials[0]?.mo_fg_id_for_department ? jwInfo?.fg_tenant_department_id : null;
              tenantDepartmentIdFromJwInfo = MoLineIdForDep || MoFgIdForDep;
            }
            jwInfo?.output_materials?.forEach((jwPoLine) => (
              jwPoLines.push({
                ...jwPoLine,
                key: uuidv4(),
                product_sku_info: jwPoLine,
                tenant_product_id: jwPoLine?.tenant_product_id,
                product_sku_name: jwPoLine?.product_sku_name,
                quantity: Number(jwPoLine?.required_output_qty || jwPoLine?.required_processing_qty),
                price: Number(jwInfo?.route_line_charges?.find((item) => item?.charge_type === 'unit')?.charge_amount),
                child_taxes: Helpers.computeTaxation((Number(jwPoLine?.required_output_qty || jwPoLine?.required_processing_qty) * Number(jwInfo?.route_line_charges?.find((item) => item?.charge_type === 'unit')?.charge_amount)), jwPoLine?.tax_info, jwInfo?.tenant_info?.default_billing_state, jwPoLine?.seller_info?.seller_address?.state)?.tax_info?.child_taxes,
                uom_info: jwPoLine?.uom_info,
                uomInfo: jwPoLine?.uom_info,
                uomId: jwPoLine?.uom_info?.uom_id,
                tax_id: jwPoLine?.tax_info?.tax_id,
                taxId: jwPoLine?.tax_info?.tax_id,
                tax_info: jwPoLine?.tax_info,
                taxInfo: jwPoLine?.tax_info,
                uomGroup: jwPoLine?.uom_info?.group_id,
                uom_list: jwPoLine?.product_info?.uom_list,
                lineCustomFields: cfs,
                lineDiscountType: 'Percent',
                secondary_uom_qty: jwPoLine.secondary_uom_qty,
                secondaryUomUqc: jwPoLine?.product_sku_info?.secondary_uom_info?.uqc,
                secondaryUomId: jwPoLine?.product_sku_info?.secondary_uom_id,
                showUnitDiscount: false,
                // lineCustomFields: lineCFs?.map((i) => ({
                //   ...i,
                //   fieldValue: i?.fieldName === 'Rate' ? Number(jwInfo?.route_line_charges?.find((item) => item?.charge_type === 'unit')?.charge_amount) : (i?.fieldName === 'Quantity' ? Number(jwPoLine?.required_output_qty || jwPoLine?.required_processing_qty) : i?.fieldValue),
                // })),
                jw_wise_breakup: jwPoLine?.jw_wise_breakup,
              })
            ));
            this.setState({
              data: jwPoLines,
              tenantDepartmentId: tenantDepartmentIdFromJwInfo,
              seller: jwInfo?.seller_info,
              gstNumber: jwInfo?.seller_info?.gst_number,
              vendorAddress: jwInfo?.seller_info?.seller_address,
              vendorAddressId: jwInfo?.seller_info?.seller_address?.address_id,
              paymentTerms: jwInfo?.seller_info?.default_payment_terms?.due_days,
              currentSelectedSeller: Number(jwInfo?.seller_info?.tenant_seller_id) || null,
              tenantSellerInfo: {
                tenant_seller_id: jwInfo?.seller_info?.tenant_seller_id,
                seller_id: jwInfo?.seller_info?.seller_id,
                seller_name: jwInfo?.seller_info?.seller_name,
                email_id: jwInfo?.seller_info?.email_id_1,
                mobile: jwInfo?.seller_info?.mobile_1,
              },
              toRecipients: [jwInfo?.seller_info?.email_id_1],
              cfPurchaseOrdersDoc: CustomFieldHelpers.getCfStructure(cfData?.data?.document_custom_fields, true),
              cfPurchaseOrdersLine: lineCFs,
              visibleColumns: CustomFieldHelpers.updateVisibleColumns(cfData?.data?.document_line_custom_fields, visibleColumns),
            });
          } else {
            this.setState({
              cfPurchaseOrdersDoc: CustomFieldHelpers.getCfStructure(cfData?.data?.document_custom_fields, true),
              cfPurchaseOrdersLine: lineCFs,
              visibleColumns: CustomFieldHelpers.updateVisibleColumns(cfData?.data?.document_line_custom_fields, visibleColumns),
              data: this.state.data?.map((item) => ({
                ...item,
                lineCustomFields: lineCFs?.map((i) => ({
                  ...i,
                  fieldValue: i?.fieldName === 'Rate' ? Number(item?.price || 0) : (i?.fieldName === 'Quantity' ? Number(item?.moq || 1) : i?.fieldValue),
                })),
              })),
            });
          }
        });
      }
      if (jwInfo?.seller_info?.seller_name || seller?.name) {
        getSellers(jwInfo?.seller_info?.seller_name || seller?.name || '', (selectedTenant || tenantId), 1, 30, moInfo?.seller?.seller_id || '', () => { }, true, !!moInfo);
      }
      if (seller?.seller_id) {
        getAddresses(1, seller.seller_id, 'SELLER');
      }
      getAddresses(1, selectedTenant || tenantId, 'TENANT');
      if (!purchaseWorkflows) {
        getPurchaseWorkflows();
      }
      if (tenantId) {
        getTenantById(selectedTenant || tenantId);
      }
      const defaultExpiryDays = user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.default_expiry_days;
      this.setState({
        expiryDate: defaultExpiryDays ? dayjs().add(defaultExpiryDays, 'day') : dayjs(),
        sendWhatsappNotification: user?.tenant_info?.is_po_automatic_notification_enabled,
        tenantDepartmentId: user?.tenant_info?.default_store_id,
        purchaseGroup: [user?.tenant_info?.user_id],
      });
      getCharges(user?.tenant_info?.org_id, 'PURCHASE', (charges) => {
        const freight = charges?.find((i) => (i?.ledger_name === 'Freight Charge' && i?.is_system_charge));
        if (freight) {
          this.setState({ freightSacCode: freight?.charge_sac_code });
        }
      });
    }
    if (!isCreatePage) {
      getTenants(1, '', true, user?.tenant_info?.org_id);
      const payload = {
        orgId: user?.tenant_info?.org_id,
        entityName: 'PURCHASE_ORDERS',
      };
      getDocCFV2(payload);
      const poId = match.params?.poId || createClonePoId;
      if (poId) {
        getPurchaseOrderById(
          poId,
          Helpers.getTenantEntityPermission(
            user?.user_tenants,
            Helpers.permissionEntities.PURCHASE_ORDER,
            Helpers.permissionTypes.READ,
          ).join(','),
          (sellerId) => {
            getGRN(
              Helpers.getTenantEntityPermission(
                user?.user_tenants,
                Helpers.permissionEntities.GOOD_RECEIVING,
                Helpers.permissionTypes.READ,
              ).join(','),
              null,
              poId,
              'PURCHASE_ORDER',
              'DRAFT,ISSUED',
              10,
              1,
              null,
            );
            getAddresses(1, sellerId, 'SELLER');
            getCurrencies();
          },
        );
      }
      this.setState({ data: [] });
    }

    // condition for cart
    if (location?.state?.dataForPrefillPO) {
      const payload = {
        orgId: user?.tenant_info?.org_id,
        entityName: 'PURCHASE_ORDERS',
      };
      getDocCFV2(payload);
    }
    getTaxes(user?.tenant_info?.org_id, 1, 1000, null, true);
  }

  componentDidUpdate(prevProps, prevState) {
    const { selectedPurchaseOrder, getPurchaseIndents } = this.props;

    const { selectedPurchaseIndentsData } = this.state;

    if (selectedPurchaseIndentsData && prevProps.selectedPurchaseOrder !== selectedPurchaseOrder && selectedPurchaseIndentsData.length === 0) {
      const tempSelectedPIs = selectedPurchaseOrder?.pi_details?.map((item) => item?.pi_id) || [];
      if (tempSelectedPIs.length > 0) {
        getPurchaseIndents({ query: { pi_ids: tempSelectedPIs } }, (purchaseIndents) => {
          const purchaseIndentData = purchaseIndents?.data || [];
          this.setState({
            selectedPurchaseIndentsData: purchaseIndentData,
          });
        });
      }
    }
  }

  componentWillUnmount() {
    const { getPurchaseOrderByIdSuccess, location, } = this.props;
    if (!location?.state?.dataForPrefillPO) {
      getPurchaseOrderByIdSuccess(null);
      this.setState({
        data: [],
        poNumber: null,
        initialPoNumber: null,
        isAutomaticConversionRate: null,
        currencyConversionRate: '',
      });
    }

  }

  getPurchaseOrderErrors() {
    const {
      cfPurchaseOrdersDoc, poNumber, poDate, deliveryDate, vendorAddress, seller, checkedRecipients, toRecipients, data, cfPurchaseOrdersLine,
      isCreatePage, isClonePo, purchaseOrderTypeValue, isInterTenantPo, destinationTenantId,
    } = this.state;
    const { user } = this.props;
    const { docLevelError, lineLevelError } = poerrorList({
      cfPurchaseOrdersDoc, poNumber, poDate, deliveryDate, vendorAddress, seller, checkedRecipients, toRecipients, data, cfPurchaseOrdersLine, isClonePo, isCreatePage, purchaseOrderTypeValue, isInterTenantPo, destinationTenantId, user,
    });
    return { docLevelError: docLevelError ?? [], lineLevelError: lineLevelError ?? [] };
  }

  static getDerivedStateFromProps(props, state) {
    const {
      tenantId, selectedPurchaseOrder, CurrenciesResults, location, match, user, selectedPI, cfV2DocPurchaseOrders, getDepartments
    } = props;
    const {
      billingAddress, vendorAddress, isLineWiseDiscount, discountPercentage, isAutomaticConversionRate, visibleColumns, piId,
    } = state;
    const purchaseRequestData = location.state?.purchaseRequestData;
    const isCreatePage = !match.params?.poId && !location.state?.clonePoId;
    const isClonePo = !!location.state?.clonePoId;
    const defaultExpiryDays = user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.default_expiry_days;
    const dataFromCartToPO = location?.state?.dataForPrefillPO;
    const autoPrintDescription = user?.tenant_info?.global_config?.settings?.print_description_automatically;
    const handleCheckboxChangeBlanketPo = (value) => {
      const { visibleColumns } = state;
      const newData = {
        ...visibleColumns,
        UNIT_PRICE: {
          label: 'Unit Price',
          visible: !value,
          disabled: true,
        },
        TAX: {
          label: 'Tax',
          visible: !value,
          disabled: true,
        },
        DISCOUNT: {
          label: 'Discount',
          visible: !value,
          disabled: true,
        },
        LINE_TOTAL: {
          label: 'Line Total',
          visible: !value,
          disabled: true,
        },
      };
      return newData;
    };
    if (CurrenciesResults && CurrenciesResults?.length > 0) {
      const defaultCurrency = CurrenciesResults?.find((currency) => currency.is_default === true);
      if (defaultCurrency && !state.selectedCurrencyID) {
        return {
          ...state,
          selectedCurrencyID: selectedPurchaseOrder
            ? selectedPurchaseOrder?.org_currency_info?.org_currency_id
            : defaultCurrency?.org_currency_id,
          selectedCurrencyName: selectedPurchaseOrder
            ? selectedPurchaseOrder?.org_currency_info
            : defaultCurrency,
          currencyConversionRate: selectedPurchaseOrder
            ? (selectedPurchaseOrder?.conversion_rate)
            : (user?.tenant_info?.global_config?.settings?.automatic_conversion_rate ? defaultCurrency?.automatic_conversion_rate : defaultCurrency?.conversion_rate),

          isAutomaticConversionRate: selectedPurchaseOrder ? false : user?.tenant_info?.global_config?.settings?.automatic_conversion_rate,
        };
      }
    }
    if (dataFromCartToPO && !state.isUserReadySyncCartData && props.cfV2DocPurchaseOrders?.data?.success) {
      const lineCFs = CustomFieldHelpers.getCfStructure(props.cfV2DocPurchaseOrders?.data?.document_line_custom_fields?.filter((item) => item?.is_active), false);
      return {
        ...state,
        cfPurchaseOrdersDoc: CustomFieldHelpers.getCfStructure(props.cfV2DocPurchaseOrders?.data?.document_custom_fields, true),
        cfPurchaseOrdersLine: lineCFs,
        visibleColumns: CustomFieldHelpers.updateVisibleColumns(props.cfV2DocPurchaseOrders?.data?.document_line_custom_fields, state.visibleColumns),
        isUserReadySyncCartData: true,
        data: dataFromCartToPO?.map((item) => {
          const discountValue = item?.is_discount_in_percent ? Number.parseFloat(Number(item?.line_discount_percentage).toFixed(DEFAULT_CUR_ROUND_OFF)) : Number.parseFloat(Number(item?.line_discount_amount).toFixed(DEFAULT_CUR_ROUND_OFF));
          const taxableValue = item?.is_discount_in_percent
            ? (item.quantity * item.offer_price) * (1 - discountValue / 100)
            : Math.max(item.quantity * item.offer_price - discountValue, 0);
          const unitDiscountValue = Number(discountValue / Number(item?.quantity)).toPrecision(DEFAULT_CUR_ROUND_OFF);
          return {
            key: uuidv4(),
            tenant_product_id: item?.tenant_product_id,
            asset1: item?.product_info?.assets?.[0]?.url || '',
            product_sku_name: item?.product_info?.product_sku_name,
            product_sku_id: item?.product_info?.product_sku_id,
            quantity: item.quantity,
            uomId: item?.uom_id || '',
            uomInfo: item?.uom_info,
            uom_info: item?.uom_info,
            uomGroup: item?.group_id,
            taxId: item?.tax_info?.tax_id,
            tax_id: item?.tax_info?.tax_id,
            taxInfo: item?.tax_info,
            sku: item?.product_info?.product_sku_id,
            lot: item?.total_price,
            child_taxes: Helpers.computeTaxation(taxableValue, item?.tax_info, billingAddress?.state, vendorAddress?.state)?.tax_info?.child_taxes,
            uom_list: item?.product_info?.uom_list,
            unitDiscount: unitDiscountValue,
            product_sku_info: item?.product_info,
            remark: autoPrintDescription ? item?.product_info?.description?.replace(/<[^>]+>/g, '') : '',
            remarkRequired: autoPrintDescription ? !!item?.product_info?.description?.replace(/<[^>]+>/g, '') : false,
            lineDiscountType: item?.is_discount_in_percent ? 'Percent' : 'Amount',
            discount: isLineWiseDiscount ? discountPercentage : 0,
            showUnitDiscount: !item?.is_discount_in_percent && unitDiscountValue > 0,
            vendorMoq: item?.sellerOffers?.[0]?.moq,
            price: item?.sellerOffers?.[0]?.offer_price,
            lastUpdatedPrice: item?.sellerOffers?.[0]?.offer_price,
            OriginalPrice: item?.sellerOffers?.[0]?.offer_price,
            lineCustomFields: FormHelpers.lineSystemFieldValue(
              lineCFs,
              [...(item?.custom_fields ?? []), ...(item?.system_custom_fields ?? [])],
            )?.map((val) => ({
              ...val,
              fieldValue: val?.fieldName === 'Rate' ? Number(item?.sellerOffers?.[0]?.offer_price || 0) : (val?.fieldName === 'Quantity' ? Number(item.quantity || 1) : val?.fieldValue),
            })),
          };
        }),
      };
    }

    // po from pr single vendor po
    if (purchaseRequestData && !state.isUserReadySyncPrData && props.cfV2DocPurchaseOrders?.data?.success && props?.selectedTenant) {

      const purchaseRequestTenantId = purchaseRequestData?.issuer_tenant_id || purchaseRequestData?.tenant_id;
      const purchaseRequestTenantDepartmentId = purchaseRequestData?.issuer_tenant_department_id || purchaseRequestData?.tenant_department_id;
      getDepartments('', purchaseRequestTenantId, 1, 100);
      const lineCFs = CustomFieldHelpers.getCfStructure(props.cfV2DocPurchaseOrders?.data?.document_line_custom_fields?.filter((item) => item?.is_active), false);

      const _data = purchaseRequestData?.purchase_requistion_lines?.map((item) => {
        const discountValue = item?.is_discount_in_percent ? Number.parseFloat(Number(item?.line_discount_percentage).toFixed(DEFAULT_CUR_ROUND_OFF)) : Number.parseFloat(Number(item?.line_discount_amount).toFixed(DEFAULT_CUR_ROUND_OFF));
        const taxableValue = item?.is_discount_in_percent
          ? (item.quantity * item.offer_price) * (1 - discountValue / 100)
          : Math.max(item.quantity * item.offer_price - discountValue, 0);
        const unitDiscountValue = Number(discountValue / Number(item?.quantity)).toPrecision(DEFAULT_CUR_ROUND_OFF);
        return {
          key: uuidv4(),
          pr_id: purchaseRequestData?.purchase_requisition_id || null,
          pr_line_id: item?.pr_line_id || null,
          tenant_product_id: item?.tenant_product_id,
          asset1: item?.product_sku_info?.assets?.[0]?.url || '',
          product_sku_name: item?.product_sku_info?.product_sku_name,
          product_sku_id: item?.product_sku_info?.product_sku_id,
          original_qty: item?.quantity,
          ordered_quantity: item?.ordered_quantity,
          pending_quantity: item?.pending_quantity,
          quantity: Math.max(item?.pending_quantity, 0),
          purchase_status: item?.purchase_status,
          uomId: item?.uom_id || '',
          uomInfo: item?.uom_info,
          uom_info: item?.uom_info,
          uomGroup: item?.group_id,
          taxId: item?.product_sku_info?.tax_info?.tax_id,
          tax_id: item?.product_sku_info?.tax_info?.tax_id,
          taxInfo: item?.product_sku_info?.tax_info,
          sku: item?.product_sku_info?.product_sku_id,
          lot: item?.total_price,
          child_taxes: Helpers.computeTaxation(taxableValue, item?.tax_info, billingAddress?.state, vendorAddress?.state)?.tax_info?.child_taxes,
          uom_list: item?.product_sku_info?.uom_list,
          unitDiscount: unitDiscountValue,
          product_sku_info: item?.product_sku_info,
          remark: autoPrintDescription ? (item?.remark || item?.product_sku_info?.remark?.replace(/<[^>]+>/g, '')) : '',
          remarkRequired: autoPrintDescription ? (!!item?.remark || !!item?.product_sku_info?.remark?.replace(/<[^>]+>/g, '')) : false,
          lineDiscountType: item?.is_discount_in_percent ? 'Percent' : 'Amount',
          discount: isLineWiseDiscount ? discountPercentage : 0,
          showUnitDiscount: !item?.is_discount_in_percent && unitDiscountValue > 0,
          // vendorMoq: item?.sellerOffers?.[0]?.moq,
          // price: item?.sellerOffers?.[0]?.offer_price || 0,
          price: 0,
          // lastUpdatedPrice: item?.sellerOffers?.[0]?.offer_price,
          // OriginalPrice: item?.sellerOffers?.[0]?.offer_price,
          lineCustomFields: FormHelpers.lineSystemFieldValue(
            lineCFs,
            [...(item?.custom_fields ?? []), ...(item?.system_custom_fields ?? [])],
          )?.map((val) => ({
            ...val,
            fieldValue: val?.fieldName === 'Rate' ? Number(item?.sellerOffers?.[0]?.offer_price || 0) : (val?.fieldName === 'Quantity' ? Number(Math.max(item?.pending_quantity, 0)) : val?.fieldValue),
          })),
        };
      });

      return {
        ...state,
        shippingAddress: props?.selectedTenant?.default_shipping_address_info,
        billingAddress: props?.selectedTenant?.default_billing_address_info,
        selectedTenant: purchaseRequestTenantId,
        tenantDepartmentId: purchaseRequestTenantDepartmentId,
        selectedTags: purchaseRequestData?.tags,
        fileList: purchaseRequestData?.attachments,
        cfPurchaseOrdersDoc: CustomFieldHelpers.getCfStructure(props.cfV2DocPurchaseOrders?.data?.document_custom_fields, true),
        cfPurchaseOrdersLine: lineCFs,
        visibleColumns: CustomFieldHelpers.updateVisibleColumns(props.cfV2DocPurchaseOrders?.data?.document_line_custom_fields, state.visibleColumns),
        isUserReadySyncPrData: true,
        data: _data,
        savedData: _data,
      };
    }

    if (isCreatePage) {
      // FROM PURCHASE INDENT
      if (!!selectedPI && cfV2DocPurchaseOrders?.data?.success && !piId && !purchaseRequestData) {
        const oldCustomField = selectedPI?.custom_fields || [];
        const oldLineCustomField = selectedPI?.purchase_indent_lines?.[0]?.custom_fields || [];
        const lineCFs = CustomFieldHelpers.getCfStructure(props.cfV2DocPurchaseOrders?.data?.document_line_custom_fields?.filter((item) => item?.is_active), false);

        const _data = selectedPI?.purchase_indent_lines?.map((item) => {
          const oldCf = item?.custom_fields || [];
          return {
            key: uuidv4(),
            tenant_product_id: item?.tenant_product_id,
            asset1: item?.product_sku_info?.assets?.[0]?.url || '',
            product_sku_name: item?.product_sku_info?.product_sku_name,
            product_sku_id: item?.product_sku_info?.product_sku_id,
            original_qty: item?.quantity,
            ordered_quantity: item?.ordered_quantity,
            pending_quantity: item?.pending_quantity,
            quantity: Math.max(item?.pending_quantity, 0),
            purchase_status: item?.purchase_status,
            price: 0,
            discount: 0,
            showUnitDiscount: false,
            lineDiscountType: 'Amount',
            unitDiscount: 0,
            lineCustomFields: FormHelpers.lineSystemFieldValue(
              lineCFs || [],
              oldCf || [],
            )?.map((val) => ({
              ...val,
              fieldValue: val?.fieldName === 'Rate' ? Number(item?.sellerOffers?.[0]?.offer_price || 0) : (val?.fieldName === 'Quantity' ? Math.max(item?.pending_quantity, 0) : val?.fieldValue),
            })),
            uom_info: item?.uom_info?.[0],
            uomInfo: item?.uom_info?.[0],
            uomId: item?.uom_info?.[0]?.uom_id,
            uomGroup: item?.group_id,
            uom_list: item?.product_sku_info?.uom_list,
            taxId: item?.product_sku_info?.tax_id,
            tax_id: item?.product_sku_info?.tax_id,
            tax_info: item?.product_sku_info?.tax_info,
            taxInfo: item?.product_sku_info?.tax_info,
            child_taxes: Helpers.computeTaxation(0, item?.product_sku_info?.tax_info, billingAddress?.state, vendorAddress?.state)?.tax_info?.child_taxes,
            sku: item?.product_sku_info?.product_sku_id,
            product_sku_info: item?.product_sku_info,
            remarkRequired: autoPrintDescription ? !!item?.product_sku_info?.description?.replace(/<[^>]+>/g, '') : false,
            pi_line_id: item?.pi_line_id,
            delivery_date: item?.delivery_date,
          };
        });

        return {
          ...state,
          piId: selectedPI?.pi_id,
          selectedTenant: selectedPI?.tenant_id,
          tenantDepartmentId: selectedPI?.tenant_department_id,
          selectedTags: selectedPI?.tags,
          cfPurchaseOrdersDoc: CustomFieldHelpers?.mergeCustomFields(cfV2DocPurchaseOrders?.data?.document_custom_fields || [], oldCustomField) || [],
          cfPurchaseOrdersLine: CustomFieldHelpers.mergeCustomFields(cfV2DocPurchaseOrders?.data?.document_line_custom_fields || [], oldLineCustomField) || [],
          visibleColumns: CustomFieldHelpers.updateVisibleColumns(CustomFieldHelpers.postCfStructure(CustomFieldHelpers.mergeCustomFields(cfV2DocPurchaseOrders?.data?.document_line_custom_fields || [], oldLineCustomField)), visibleColumns),
          fileList: selectedPI?.attachments,
          data: _data,
          savedData: _data,
          selectedPurchaseIndentIds: [selectedPI?.pi_id],
          selectedPurchaseIndentsData: [selectedPI],
          savedSelectedPurchaseIndentIds: [selectedPI?.pi_id],
          deliveryDate: toISTDate(selectedPI?.delivery_date),
        };
      }
      if (props?.tenantsConfiguration && !state?.tenantsConfiguration) {
        return {
          ...state,
          tenantsConfiguration: props?.tenantsConfiguration,
        };
      }
      if (props?.user?.tenant_info && !state?.shippingAddress && !tenantId && !purchaseRequestData) {
        props.getTenantsConfiguration(props.user?.tenant_info?.tenant_id);
        return {
          ...state,
          shippingAddress:
            props?.user?.tenant_info?.default_shipping_address_info,
          billingAddress:
            props?.user?.tenant_info?.default_billing_address_info,
        };
      }
      if (props?.user?.tenant_info && !state?.shippingAddress && props?.selectedTenant && tenantId && !purchaseRequestData) {
        return {
          ...state,
          shippingAddress:
            props?.selectedTenant?.default_shipping_address_info,
          billingAddress:
            props?.selectedTenant?.default_billing_address_info,
        };
      }
    } else {
      if (
        props.selectedPurchaseOrder
        && !state.data?.length
        && !props.getPurchaseOrderByIdLoading
        && props.cfV2DocPurchaseOrders?.data?.success
        && !state?.isUserReadyUpdateCase
      ) {
        const { getAddresses } = props;
        getAddresses(
          1,
          selectedPurchaseOrder?.tenant_info?.tenant_id,
          'TENANT',
        );
        const tempSelectedPIs = selectedPurchaseOrder?.pi_details?.map((item) => item?.pi_id) || [];

        const oldCustomField = props.selectedPurchaseOrder?.custom_fields || [];
        const oldLineCustomField = props?.selectedPurchaseOrder?.purchase_order_lines[0]?.po_line_custom_fields;
        const checkVisibleColumns = selectedPurchaseOrder?.purchase_order_without_costing ? handleCheckboxChangeBlanketPo(true) : (selectedPurchaseOrder?.pi_details?.length > 1 ? {
          ...state.visibleColumns, REQUESTED_QUANTITY: {
            label: 'Requested Quantity',
            visible: true,
            disabled: true,
          }
        } : state.visibleColumns);
        return {
          ...state,
          cfPurchaseOrdersDoc: CustomFieldHelpers.mergeCustomFields(props.cfV2DocPurchaseOrders?.data?.document_custom_fields, oldCustomField),
          cfPurchaseOrdersLine: CustomFieldHelpers.mergeCustomFields(props.cfV2DocPurchaseOrders?.data?.document_line_custom_fields, oldLineCustomField || []),
          visibleColumns: CustomFieldHelpers.updateVisibleColumns(CustomFieldHelpers.postCfStructure(CustomFieldHelpers.mergeCustomFields(props.cfV2DocPurchaseOrders?.data?.document_line_custom_fields, oldLineCustomField || [])), checkVisibleColumns),
          seller: selectedPurchaseOrder?.seller_info,
          currentSelectedSeller: Number(
            selectedPurchaseOrder?.tenant_seller_id,
          ),
          tenantDepartmentId: selectedPurchaseOrder?.tenant_department_id,
          purchaseOrderTypeValue: selectedPurchaseOrder?.purchase_order_without_costing ? 3 : (selectedPurchaseOrder?.po_type === 'WORK_ORDER' ? 2 : 1),
          tenantSellerInfo: selectedPurchaseOrder?.tenant_seller_info || {
            tenant_seller_id: selectedPurchaseOrder?.tenant_seller_id,
            seller_id: selectedPurchaseOrder?.seller_info?.seller_id,
            seller_name: selectedPurchaseOrder?.seller_info?.seller_name,
            email_id: selectedPurchaseOrder?.seller_info?.mobile_id_1,
            mobile: selectedPurchaseOrder?.seller_info?.mobile_1,
          },
          gstNumber: selectedPurchaseOrder?.tenant_seller_info?.gst_number,
          isInterTenantPo: selectedPurchaseOrder?.is_inter_tenant_purchase_order,
          destinationTenantId: selectedPurchaseOrder?.destination_tenant_id,
          destinationDepartmentId: selectedPurchaseOrder?.destination_department_id,
          createAutomaticSo: selectedPurchaseOrder?.create_automatic_sales_order,
          vendorAddress: selectedPurchaseOrder?.seller_address,
          vendorAddressId: selectedPurchaseOrder?.seller_address?.address_id,
          shippingAddress: selectedPurchaseOrder?.shipping_address,
          shippingAddressId:
            selectedPurchaseOrder?.shipping_address?.address_id,
          billingAddress: selectedPurchaseOrder?.billing_address,
          billingAddressId: selectedPurchaseOrder?.billing_address?.address_id,
          advancePayment: selectedPurchaseOrder?.payment_terms?.[0]
            ?.advance_amount || '',
          paymentTerms:
            selectedPurchaseOrder?.payment_terms?.[0]?.due_days || 0,
          remarks: selectedPurchaseOrder?.payment_terms?.[0]?.remark || '',
          poDate: isClonePo ? dayjs() : toISTDate(selectedPurchaseOrder?.po_date),
          poNumber: selectedPurchaseOrder?.po_number,
          deliveryDate: isClonePo ? null : (selectedPurchaseOrder?.edd
            ? toISTDate(selectedPurchaseOrder.edd)
            : null),
          expiryDate: (isClonePo && defaultExpiryDays) ? dayjs().add(defaultExpiryDays, 'day') : (selectedPurchaseOrder?.po_expiry_date
            ? toISTDate(selectedPurchaseOrder?.po_expiry_date)
            : null),
          terms: selectedPurchaseOrder?.t_and_c,
          tncId: selectedPurchaseOrder?.tc_id || null,
          fileList: selectedPurchaseOrder?.attachments,
          blanketPO: selectedPurchaseOrder?.purchase_order_without_costing,
          toRecipients: selectedPurchaseOrder?.notification_recipients || [],
          checkedRecipients:
            selectedPurchaseOrder?.is_automatic_notification_enabled,
          sendWhatsappNotification:
            selectedPurchaseOrder?.is_po_automatic_notification_enabled,
          chargeData:
            selectedPurchaseOrder?.other_charges?.map((otherCharge) => ({
              ...otherCharge,
              chargeKey: uuidv4(),
              chargesTaxId: otherCharge?.tax_info?.tax_id,
              chargesSacCode: otherCharge?.charge_sac_code,
              chargesTaxInfo: otherCharge?.tax_info,
              chargesTax: otherCharge?.tax_info?.tax_value,
              chargesTaxData: {
                ...otherCharge?.tax_info,
                child_taxes: Helpers.computeTaxation(otherCharge?.charge_amount, otherCharge?.tax_info, selectedPurchaseOrder?.billing_address?.state, selectedPurchaseOrder?.seller_address?.state)?.tax_info?.child_taxes,
              },
            })) || [],
          charge1Name: selectedPurchaseOrder?.charge_1_name || 'Freight',
          charge1Value: selectedPurchaseOrder?.charge_1_value,
          freightTaxId: selectedPurchaseOrder?.freight_tax_id || 'Not Applicable',
          freightTax: selectedPurchaseOrder?.freight_tax_info?.tax_value,
          freightTaxInfo: selectedPurchaseOrder?.freight_tax_info,
          freightTaxData: {
            ...selectedPurchaseOrder?.freight_tax_info,
            child_taxes: Helpers.computeTaxation(selectedPurchaseOrder?.charge_1_value, selectedPurchaseOrder?.freight_tax_info, selectedPurchaseOrder?.billing_address?.state, selectedPurchaseOrder?.seller_address?.state)?.tax_info?.child_taxes,
          },
          freightSacCode: selectedPurchaseOrder?.freight_sac_code,
          taxType: selectedPurchaseOrder?.tcs_id,
          taxTypeId: selectedPurchaseOrder?.tcs_info?.tax_id,
          taxTypeInfo: selectedPurchaseOrder?.tcs_info,
          taxTypeName: selectedPurchaseOrder?.tcs_info?.tax_type_name,
          isLineWiseDiscount: !selectedPurchaseOrder?.is_line_wise_discount,
          discountPercentage: selectedPurchaseOrder?.is_discount_in_percent ? selectedPurchaseOrder?.discount_percentage : selectedPurchaseOrder?.discount_amount,
          discountType: selectedPurchaseOrder?.is_discount_in_percent ? 'Percent' : 'Amount',
          selectedTags: selectedPurchaseOrder?.tags || [],
          purchaseGroup: selectedPurchaseOrder?.purchase_group || [user?.tenant_info?.user_id],
          data: selectedPurchaseOrder?.purchase_order_lines?.map((item) => {
            const discountValue = item?.is_discount_in_percent ? Number.parseFloat(Number(item?.line_discount_percentage).toFixed(DEFAULT_CUR_ROUND_OFF)) : Number.parseFloat(Number(item?.line_discount_amount).toFixed(DEFAULT_CUR_ROUND_OFF));
            const taxableValue = item?.is_discount_in_percent
              ? (item.quantity * item.offer_price) * (1 - discountValue / 100)
              : Math.max(item.quantity * item.offer_price - discountValue, 0);
            const unitDiscountValue = Number(discountValue / Number(item?.quantity)).toPrecision(DEFAULT_CUR_ROUND_OFF);
            const lineData = {
              key: uuidv4(),
              tenant_product_id: item?.tenant_product_id,
              po_line_id: item.po_line_id,
              asset1: item?.product_sku_info?.assets?.[0]?.url || '',
              product_sku_name: item?.product_sku_info?.product_sku_name,
              product_sku_id: item?.product_sku_info?.product_sku_id,
              quantity: item.quantity,
              secondary_uom_qty: item.secondary_uom_qty,
              secondaryUomUqc: item?.product_sku_info?.secondary_uom_info?.uqc,
              secondaryUomId: item?.product_sku_info?.secondary_uom_id,
              price: item.offer_price,
              uomId: item?.uom_id || '',
              uomInfo: item?.uom_info?.[0],
              uom_info: item?.uom_info?.[0],
              uomGroup: item?.group_id,
              taxId: item?.tax_group_info?.tax_id,
              taxInfo: item?.tax_group_info,
              child_taxes: Helpers.computeTaxation(taxableValue, item?.tax_group_info, selectedPurchaseOrder?.billing_address?.state, selectedPurchaseOrder?.seller_address?.state)?.tax_info?.child_taxes,
              lot: item.total_price,
              uom_list: item?.product_sku_info?.uom_list,
              discount: discountValue,
              unitDiscount: unitDiscountValue,
              product_sku_info: item?.product_sku_info,
              productCategoryInfo: item?.product_sku_info?.product_category_info || {},
              remark: item?.remark?.trim(),
              remarkRequired: !!item?.remark?.trim()?.replace(/<[^>]+>/g, ''),
              lineDiscountType: item?.is_discount_in_percent ? 'Percent' : 'Amount',
              showUnitDiscount: !item?.is_discount_in_percent && unitDiscountValue > 0,
              lineCustomFields: CustomFieldHelpers.mergeCustomFields(props.cfV2DocPurchaseOrders?.data?.document_line_custom_fields, item?.po_line_custom_fields || [])?.map((i) => ({
                ...i,
                fieldValue: i?.fieldName === 'Rate' ? Number(item.offer_price || 0) : (i?.fieldName === 'Quantity' ? Number(item?.quantity) : i?.fieldValue),
              })),
              delivery_date: item?.delivery_date ? dayjs(item?.delivery_date) : null,
            };

            if (item.pi_id) {
              lineData.pi_line_id = item.pi_line_id;
              lineData.pi_id = item.pi_id;
              lineData.pi_number = item.pi_number;
            }

            return lineData;
          }),

          selectedCurrencyID: selectedPurchaseOrder
            ? selectedPurchaseOrder?.org_currency_info?.org_currency_id
            : state.selectedCurrencyID,
          selectedCurrencyName: selectedPurchaseOrder
            ? selectedPurchaseOrder?.org_currency_info
            : state.selectedCurrencyName,
          currencyConversionRate: selectedPurchaseOrder
            ? (selectedPurchaseOrder?.conversion_rate)
            : state.currencyConversionRate,
          isAutomaticConversionRate: selectedPurchaseOrder ? false : state.isAutomaticConversionRate,
          isManualControlCF: true,
          selectedTenant: props?.selectedPurchaseOrder?.tenant_id,
          selectedPurchaseIndentIds: tempSelectedPIs,
          savedSelectedPurchaseIndentIds: tempSelectedPIs,
          isUserReadyUpdateCase: true,
        };
      }
    }

    return state;
  }

  handleChangeTextArea = (html) => {
    this.setState({ terms: html });
  };

  handleDelete = (key) => {
    const { data, savedData, cfPurchaseOrdersLine, selectedPurchaseIndentIds, selectedPurchaseIndentsData } = this.state;
    const { match, location, moFgLines, selectedPI, selectedPurchaseOrder } = this.props;
    const isCreatePage = !match.params?.poId && !location.state?.clonePoId;

    if (data?.length === 1 && ((isCreatePage && !moFgLines))) {
      const remainingStates = (selectedPurchaseIndentIds.length > 0 && !selectedPI) ? {
        selectedPurchaseIndentIds: [],
        savedSelectedPurchaseIndentIds: [],
        selectedPurchaseIndentsData: [],
        savedData: [],
      } : {};

      this.setState({
        data: [
          {
            key: uuidv4(),
            asset1: '',
            product_name: '',
            quantity: '',
            price: '',
            lot: 0,
            taxId: 0,
            child_taxes: [{
              tax_amount: 0,
              tax_type_name: '',
            }],
            lineCustomFields: cfPurchaseOrdersLine,
          },
        ],
        ...remainingStates,
      });
    } else if (data?.length === 1 && (moFgLines || selectedPurchaseOrder)) {
      notification.error({
        message: 'Atleast one line item is required.',
        placement: 'top',
        duration: 4,
      });
    } else if (selectedPurchaseIndentIds?.length && !selectedPI) {
      const copyData = data?.filter((item) => item.key !== key);
      const tempSelectedPurchaseIndentIds = selectedPurchaseIndentIds?.filter((item) => copyData?.some((data) => data?.pi_id === item));

      const tempSelectedPurchaseIndentsData = selectedPurchaseIndentsData?.filter((item) => tempSelectedPurchaseIndentIds.includes(item.pi_id));

      this.setState((prevState) => ({
        data: copyData,
        selectedPurchaseIndentIds: tempSelectedPurchaseIndentIds,
        savedSelectedPurchaseIndentIds: tempSelectedPurchaseIndentIds,
        selectedPurchaseIndentsData: tempSelectedPurchaseIndentsData,
        savedData: tempSelectedPurchaseIndentIds?.length !== selectedPurchaseIndentIds?.length ? savedData?.filter((item) => item.key !== key) : prevState.savedData,
        fileList: tempSelectedPurchaseIndentsData.map((item) => item?.attachments)?.flat(),
        selectedTags: [...new Set(tempSelectedPurchaseIndentsData?.map((item) => item?.tags).filter(Boolean)?.flat())],
      }), () => {
        if (this.state?.isLineWiseDiscount) this.handleDiscountPercentageChange(this.state.discountPercentage);
      });
    } else {
      const copyData = data?.filter((item) => item.key !== key);
      this.setState({ data: copyData }, () => {
        if (this.state?.isLineWiseDiscount) this.handleDiscountPercentageChange(this.state.discountPercentage);
      });
    }
  };

  handleDeleteCharge = (chargeKey) => {
    const { chargeData } = this.state;
    const copyChargeData = chargeData.filter(
      (item) => item.chargeKey !== chargeKey,
    );
    this.setState({ chargeData: copyChargeData });
  };

  onBulkUpload(updatedData, importedData) {
    const { billingAddress, vendorAddress, cfPurchaseOrdersLine } = this.state;
    const finalData = updatedData?.map((item, index) => ({
      key: uuidv4(),
      tenant_product_id: item?.tenant_product?.[0]?.tenant_product_id,
      tenantProductId: item?.tenant_product?.[0]?.tenant_product_id,
      product_sku_info: {
        product_sku_id: item.product_sku_id,
        product_sku_name: item?.product_sku_name,
        internal_sku_code: item?.internal_sku_code,
      },
      product_sku_id: item.product_sku_id,
      product_sku_name: item?.product_sku_name,
      quantity: importedData.find((i) => i?.sku_id === item?.internal_sku_code)
        ?.quantity,
      uom_info: item.uom_info,
      expiry_days: item?.expiry_days,
      expiryDate:
        item?.expiry_days >= 0
          ? toISTDate(dayjs())
            .add(item?.expiry_days || 0, 'day')
            .format('YYYY-MM-DD')
          : INFINITE_EXPIRY_DATE,
      availableQuantity: importedData.find(
        (i) => i?.sku_id === item?.internal_sku_code,
      )?.quantity,
      asset1: item?.product_info?.assets?.[0]?.url || '',
      price: item?.tenant_product?.[0]?.sku_offers?.[0]?.offer_price || 0,
      taxInfo: item?.tax_info || '',
      child_taxes: item?.tax_info ? Helpers.computeTaxation((importedData.find((i) => i?.sku_id === item?.internal_sku_code)
        ?.quantity * (item?.tenant_product?.[0]?.sku_offers?.[0]?.offer_price || 0)), item?.tax_info, billingAddress?.state, vendorAddress?.state)?.tax_info?.child_taxes : [{
          tax_amount: 0,
          tax_type_name: '',
        }],
      taxId: item?.tax_id || '',
      uomGroup: item?.uom_group || '',
      uomInfo: item?.purchase_uom_info || '',
      uomId: Number(item?.purchase_uom_info?.uom_id) || '',
      uom_list: item?.uom_list,
      lineCustomFields: FormHelpers.lineSystemFieldValue(
        cfPurchaseOrdersLine,
        [...(item?.custom_fields ?? [])],
      )?.map((val) => ({
        ...val,
        fieldValue: val?.fieldName === 'Rate' ? Number(0) : (val?.fieldName === 'Quantity' ? importedData.find((i) => i?.sku_id === item?.internal_sku_code)?.quantity : ''),
      })),
    }));
    this.setState({ data: finalData });
  }

  getLineTotals() {
    const {
      data, chargeData, taxTypeInfo, taxTypeName, charge1Value, freightTax, freightTaxData,
    } = this.state;
    let totalAmount = 0;
    let totalDiscount = 0;
    let totalBase = 0;
    let totalTcs = 0;
    let totalTax = 0;
    let totalOtherCharge = 0;
    let poTotal = 0;
    let freightCharge = Number(charge1Value);
    let totalOtherChargesForTaxableAmount = 0;
    let freightTaxAmount = 0;

    // Taxes Bifurcation
    const totalTaxValue = Helpers.groupAndSumByTaxName(data?.map((item) => item?.child_taxes)?.flat())?.reduce((acc, curr) => acc + curr?.tax_amount, 0);
    for (let i = 0; i < chargeData?.length; i++) {
      let currentOtherCharge = 0;
      let chargesTaxAmountLinesWise = 0;
      if (chargeData[i]?.charge_amount) {
        chargesTaxAmountLinesWise = Helpers.groupAndSumByTaxName([...chargeData[i]?.chargesTaxData?.child_taxes?.flat()])?.reduce((acc, curr) => acc + curr?.tax_amount, 0);
        currentOtherCharge = chargeData?.[i]?.chargesTax ? chargeData?.[i]?.charge_amount + (chargesTaxAmountLinesWise) : chargeData?.[i]?.charge_amount;
        // currentOtherCharge = chargeData?.[i]?.chargesTax ? chargeData?.[i]?.charge_amount * (1 + chargesTaxAmountLinesWise / 100) : chargeData?.[i]?.charge_amount;
        if (chargeData?.[i]?.chargesTax) {
          totalOtherChargesForTaxableAmount += chargeData?.[i]?.charge_amount;
        }
      }

      totalOtherCharge += currentOtherCharge;
    }
    for (let i = 0; i < data?.length; i++) {
      let currentAmount = 0;
      let currentDiscount = 0;
      let currentBase = 0;
      let currentTax = 0;
      let currentPO = 0;
      if (data[i].quantity) {
        const discountValue = Number(data[i].discount) || 0;

        currentAmount += data[i].quantity * data[i].price;
        currentDiscount += data[i].discount
          ? (data[i]?.lineDiscountType === 'Percent' ? (data[i].quantity * data[i].price * (data[i].discount / 100)) : discountValue)
          : 0;

        if (data[i].discount) {
          if (data[i]?.lineDiscountType === 'Percent') {
            currentBase
              += data[i].quantity
              * data[i].price
              * (data[i].discount ? (Number(100 - data[i].discount) / 100) : 1);
          } else if (data[i]?.lineDiscountType === 'Amount') {
            currentBase += (data[i].quantity
              * data[i].price) - discountValue;
          }
        } else {
          currentBase
            += data[i].quantity
            * data[i].price;
        }
      }
      // Add TCS from currentBase
      if (taxTypeInfo && taxTypeName === 'TCS') {
        const tcsRate = taxTypeInfo?.tax_value / 100;
        const tcsAmount = currentBase * tcsRate;
        totalTcs += tcsAmount;
      }

      if (data[i].taxInfo?.tax_value) { currentTax = currentBase * (data[i].taxInfo?.tax_value / 100); }
      if (currentBase) currentPO = currentBase;

      totalAmount += currentAmount;
      totalDiscount += currentDiscount;
      totalBase += currentBase;
      totalTax += currentTax;
      poTotal += currentPO;
    }

    if (freightTax) {
      freightTaxAmount = Helpers.groupAndSumByTaxName([...freightTaxData?.child_taxes?.flat()])?.reduce((acc, curr) => acc + curr?.tax_amount, 0);

      totalBase += freightCharge;
      freightCharge += freightTaxAmount;
      if (taxTypeInfo && taxTypeName === 'TCS') {
        const tcsRate = taxTypeInfo?.tax_value / 100;
        totalTcs = totalBase * tcsRate;
      }
    }

    // Add TCS from to total amount including GST ,Freight Other charges
    if (taxTypeInfo && taxTypeName === 'TCS') {
      const tcsRate = taxTypeInfo?.tax_value / 100;

      // If we have freight tax then add only freight tax amount as freight charge is already included in totalBase or if freight tax  is not present add freightCharge
      const tcsAmount = (totalBase + totalTaxValue + Number(totalOtherCharge) + (freightTax ? freightTaxAmount : freightCharge)) * tcsRate;
      totalTcs = tcsAmount;
    }

    poTotal += (Number(totalOtherCharge) + freightCharge) + totalTaxValue + totalTcs;
    totalBase += totalOtherChargesForTaxableAmount;
    return {
      totalAmount,
      totalDiscount,
      totalBase,
      totalTax,
      totalTcs,
      poTotal,
    };
  }

  handleProductChange = (tenantSku, key, productData, isMultiMode, callback) => {
    const {
      data, currentSelectedSeller, billingAddress, vendorAddress, isLineWiseDiscount, discountType, discountPercentage, cfPurchaseOrdersLine, currencyConversionRate,
    } = this.state;

    const { getTenantSkuOffer, user } = this.props;

    const autoPrintDescription = user?.tenant_info?.global_config?.settings?.print_description_automatically;

    if (isMultiMode && Array.isArray(tenantSku)) {
      const tenantProductIds = tenantSku?.map((item) => item?.tenant_product_id)?.join(',');
      getTenantSkuOffer(
        '',
        currentSelectedSeller,
        (sellerOffers) => {
          const copyData = [];
          for (let i = 0; i < tenantSku?.length; i++) {
            const tenantProductId = tenantSku[i]?.tenant_product_id;

            const tenantProductSellerOffer = sellerOffers[tenantProductId]?.[0];

            const newData = {
              key: uuidv4(),
              lineDiscountType: 'Percent',
              lineCustomFields: cfPurchaseOrdersLine,
            };

            const productSKUData = productData?.find((item) => item?.product_sku_id === tenantSku[i]?.product_sku_id);

            const newDataRow = this.createData({
              tenantSku: tenantSku[i],
              dataItem: newData,
              sellerOffer: tenantProductSellerOffer,
              productData: productSKUData,
              discountPercentage,
              currencyConversionRate,
              billingAddress,
              vendorAddress,
              autoPrintDescription,
              isLineWiseDiscount,
              discountType,
              cfPurchaseOrdersLine,
            });

            copyData.push(newDataRow);
          }

          if (callback) callback();

          this.setState((prevState) => ({
            data: [...prevState.data, ...copyData],
          }));
        },
        tenantProductIds,
      );
    } else {
      getTenantSkuOffer(
        tenantSku?.tenant_product_id,
        currentSelectedSeller,
        (sellerOffer) => {
          const copyData = JSON.parse(JSON.stringify(data));

          for (let i = 0; i < copyData.length; i++) {
            if (copyData[i].key === key) {
              copyData[i] = this.createData({
                tenantSku,
                dataItem: copyData[i],
                sellerOffer,
                productData,
                discountPercentage,
                currencyConversionRate,
                billingAddress,
                vendorAddress,
                autoPrintDescription,
                isLineWiseDiscount,
                discountType,
                cfPurchaseOrdersLine,
              });
            }
          }
          this.setState({ data: copyData }, () => {
            if (isLineWiseDiscount) this.handleDiscountPercentageChange(discountPercentage);
          });
        },
      );
    }
  };

  handleMultiProductChange = (selectedProductData, key, productData, callback) => {
    this.handleProductChange(selectedProductData, key, productData, true, callback);
  };

  createData = ({
    tenantSku, dataItem, sellerOffer, productData, discountPercentage, currencyConversionRate, billingAddress, vendorAddress, autoPrintDescription, isLineWiseDiscount, discountType, cfPurchaseOrdersLine,
  }) => {
    const { seller, currentSelectedSeller } = this.state;
    const { taxesGroup, user, selectedPurchaseOrder } = this.props;
    const copyDataItem = JSON.parse(JSON.stringify(dataItem));
    const taxableValue = ((sellerOffer?.moq || 1) * sellerOffer?.offer_price) * (1 - (discountPercentage || 0) / 100);

    const isVendorOverseas = (seller?.seller_type === 'OVERSEAS' || currentSelectedSeller?.seller_info?.seller_type === 'OVERSEAS' || selectedPurchaseOrder?.seller_type === 'OVERSEAS') && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;

    copyDataItem.tenant_product_id = tenantSku?.tenant_product_id;
    copyDataItem.secondaryUomUqc = tenantSku?.secondary_uom_info?.uqc;
    copyDataItem.secondaryAvailableQty = tenantSku?.secondary_available_qty;
    copyDataItem.secondary_uom_qty = 0;
    copyDataItem.secondaryUomId = tenantSku?.secondary_uom_id;
    copyDataItem.product_sku_id = tenantSku?.product_info?.product_sku_id;
    copyDataItem.product_sku_info = { ...tenantSku?.product_info, product_category_info: tenantSku?.product_category_info || {} };
    copyDataItem.po_line_id = tenantSku?.po_line_id;
    copyDataItem.product_sku_name = `${tenantSku?.product_info.product_sku_name}`;
    copyDataItem.asset1 = tenantSku?.product_info?.assets?.[0]?.url || '';
    copyDataItem.quantity = sellerOffer?.moq || '1';
    copyDataItem.vendorMoq = sellerOffer?.moq || null;
    copyDataItem.price = ((sellerOffer?.offer_price || 0) / (currencyConversionRate || 1)).toFixed(DEFAULT_CUR_ROUND_OFF) || 0;
    copyDataItem.lastUpdatedPrice = ((sellerOffer?.offer_price || 0) / (currencyConversionRate || 1)).toFixed(DEFAULT_CUR_ROUND_OFF) || 0;
    copyDataItem.OriginalPrice = sellerOffer?.offer_price || 0;
    copyDataItem.sku = tenantSku?.product_info?.product_sku_id || '';
    copyDataItem.taxInfo = isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : tenantSku?.tax_info;
    copyDataItem.child_taxes = Helpers.computeTaxation(taxableValue, isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : tenantSku?.tax_info, billingAddress?.state, vendorAddress?.state)?.tax_info?.child_taxes;
    copyDataItem.uom_info = tenantSku?.purchase_uom_info;
    copyDataItem.lot = tenantSku?.total_price || 0;
    copyDataItem.uomInfo = tenantSku?.purchase_uom_info;
    copyDataItem.uomId = tenantSku?.purchase_uom_info?.uom_id;
    copyDataItem.productCategoryInfo = tenantSku?.product_category_info || {};
    copyDataItem.tax_id = isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : tenantSku?.product_info?.tax_id || 0;
    copyDataItem.taxId = isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : tenantSku?.product_info?.tax_id || 0;
    copyDataItem.uomId = tenantSku?.purchase_uom_info?.uom_id || 0;
    copyDataItem.uomGroup = tenantSku?.group_id || tenantSku?.purchase_uom_info?.group_id;
    copyDataItem.uom_list = tenantSku?.uom_list;
    copyDataItem.remark = autoPrintDescription ? (productData?.description || '') : '';
    copyDataItem.remarkRequired = !!productData?.description?.replace(/<[^>]+>/g, '');
    copyDataItem.discount = isLineWiseDiscount ? (discountPercentage || 0) : 0;
    copyDataItem.lineDiscountType = isLineWiseDiscount ? discountType : 'Percent';
    copyDataItem.lineCustomFields = FormHelpers.lineSystemFieldValue(
      cfPurchaseOrdersLine,
      [...(tenantSku?.custom_fields ?? []), ...(productData?.system_custom_fields ?? [])],
    )?.map((val) => ({
      ...val,
      fieldValue: val?.fieldName === 'Rate' ? Number(sellerOffer?.offer_price || 0) : (val?.fieldName === 'Quantity' ? Number(sellerOffer?.moq || 1) : val?.fieldValue),
    }));
    return copyDataItem;
  };

  handleProductChangeValue = (value, key) => {
    const { data, discountPercentage, isLineWiseDiscount } = this.state;
    const copyData = JSON.parse(JSON.stringify(data));
    for (let i = 0; i < copyData.length; i++) {
      if (copyData[i].key === key) {
        const copyDataItem = copyData[i];
        copyDataItem.product_sku_name = value;
        copyData[i] = copyDataItem;
      }
    }
    this.setState({ data: copyData });
  };

  addNewRow() {
    const { data, cfPurchaseOrdersLine } = this.state;
    const copyData = JSON.parse(JSON.stringify(data));
    copyData.push({
      key: uuidv4(),
      asset1: '',
      product_sku_name: '',
      quantity: '',
      price: '',
      taxId: '',
      lot: null,
      lineDiscountType: 'Percent',
      child_taxes: [{
        tax_amount: 0,
        tax_type_name: '',
      }],
      lineCustomFields: cfPurchaseOrdersLine,
    });
    this.setState({ data: copyData });
  }

  addNewChargesRow() {
    const { chargeData } = this.state;
    const copychargeData = JSON.parse(JSON.stringify(chargeData));
    copychargeData.push({
      chargeKey: uuidv4(),
      charge_name: '',
      charge_amount: 0,
      chargesTaxData: {
        child_taxes: [
          {
            tax_amount: 0,
            tax_type_name: '',
          },
        ],
      },
    });
    this.setState({ chargeData: copychargeData });
  }

  isDataValid() {
    const {
      data, poDate, deliveryDate, visibleColumns, purchaseOrderTypeValue,
    } = this.state;
    const { user } = this.props;

    const deliveryDateMandatory = user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.delivery_date_mandatory;

    if (!poDate) return false;

    for (let i = 0; i < data?.length; i++) {
      const item = data[i];
      const quantity = Number(item.quantity);
      const productSkuName = item.product_sku_name;
      const price = purchaseOrderTypeValue === 3 ? 1 : item?.price === '' ? -1 : Number(item?.price);
      const { taxId } = item;
      const requiredCfs = item?.lineCustomFields?.filter((cf) => cf.fieldName !== 'Rate');
      const customFieldsValid = CustomFieldHelpers.isCfValid(requiredCfs);
      if (
        quantity <= 0
        || !productSkuName
        || price < 0
        || !taxId
        || !customFieldsValid
      ) {
        return false;
      }
    }

    if (!deliveryDate && deliveryDateMandatory) {
      return false;
    }

    const { docLevelError, lineLevelError } = this.getPurchaseOrderErrors();

    if (docLevelError?.length > 0 || lineLevelError?.length > 0) {
      return false;
    }

    return true;
  }

  isDataValid2() {
    const { chargeData } = this.state;
    let isDataValid = true;
    if (chargeData?.length) {
      for (let i = 0; i < chargeData?.length; i++) {
        if (!chargeData[i].charge_name || !chargeData[i].charge_amount) {
          isDataValid = false;
        }
      }
    }
    return isDataValid;
  }

  createPurchaseOrder(withApproval) {
    this.setState({ withApproval });
    const {
      createPurchaseOrder, history, user, moFgLines, createTag, moCallback, tenantId, jwInfo, location, moInfo, getCartV2, removeFromCartV2, selectedPI,
    } = this.props;
    const clonePoTenantId = location?.state?.selectedTenantId;
    const purchaseRequestData = location?.state?.purchaseRequestData;
    const {
      deliveryDate, expiryDate, poDate, data, createAutomaticSo, isInterTenantPo, destinationTenantId, destinationDepartmentId,
      terms, shippingAddress, vendorAddress, paymentTerms,
      currentSelectedSeller, billingAddress, fileList, tenantSellerInfo, gstNumber,
      remarks, toRecipients, checkedRecipients, chargeData, discountPercentage, sendWhatsappNotification, blanketPO,
      isLineWiseDiscount, purchaseOrderType, selectedTags, tenantDepartmentId, discountType, cfPurchaseOrdersDoc, poNumber, docSeqId,
      purchaseOrderTypeValue, selectedCurrencyName, taxTypeInfo, taxType, charge1Value, charge1Name, freightTaxInfo, freightTaxId, freightSacCode, initialPoNumber, selectedRmBearer, purchaseGroup, currencyConversionRate, selectedTenant, piId, seller, tncId,
    } = this.state;
    const vendorAddressMandatory = user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.seller_address_required;
    const isPurchaseGroupEnabled = user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.enable_purchasing_group;
    const purchaseOrderLines = [];
    this.setState({
      formSubmitted: true,
      data: JSON.parse(JSON.stringify(data)),
    });
    if (
      this.isDataValid()
      && this.isDataValid2()
      && (checkedRecipients ? toRecipients?.length : true)
      && (currentSelectedSeller || destinationTenantId)
      && shippingAddress && (vendorAddressMandatory ? vendorAddress : true)
      && CustomFieldHelpers.isCfValid(cfPurchaseOrdersDoc) && poNumber
    ) {
      for (let i = 0; i < data?.length; i++) {
        let lineDiscount = 0;
        lineDiscount = data[i]?.lineDiscountType === 'Percent' ? Number(data?.[i]?.discount || 0) : ((Number(data?.[i]?.discount || 0) / (Number(data[i].quantity) * parseFloat(data[i].price))) * 100);
        let remainingQty = Number(data[i].quantity);
        const poLine = {
          pi_line_id: data?.[i]?.pi_line_id || null,
          pr_line_id: data?.[i]?.pr_line_id || null,
          secondary_uom_qty: data[i]?.secondaryUomId ? data[i]?.secondary_uom_qty : 0,
          tenant_product_id: data[i].tenant_product_id,
          product_sku_name: data[i].product_sku_name,
          product_sku_id: data[i]?.product_sku_info?.product_sku_id,
          offer_price: blanketPO ? 0 : parseFloat(data[i].price),
          product_info: data[i]?.product_info,
          tax_id: data[i]?.taxId,
          uom_id: data[i]?.uomId,
          tax_group_info: data[i]?.taxInfo,
          uom_info: [data[i]?.uomInfo],
          quantity: Number(data[i].quantity),
          line_discount_percentage: lineDiscount,
          remark: data[i]?.remark,
          mo_fg_id: data[i]?.mo_fg_id,
          production_route_line_id: data[i]?.production_route_line_id,
          is_discount_in_percent: data[i]?.lineDiscountType === 'Percent',
          custom_fields: CustomFieldHelpers.postCfStructure(data[i]?.lineCustomFields),
          mo_line_id: data[i]?.mo_line_id,
          jw_wise_breakup: data[i]?.jw_wise_breakup?.map((jwItem) => {
            let newQty;
            if (remainingQty >= jwItem.quantity) {
              newQty = Number(jwItem.quantity);
              remainingQty -= jwItem.quantity;
            } else {
              newQty = remainingQty;
              remainingQty = 0;
            }
            return ({
              ...jwItem,
              quantity: newQty,
            });
          }),
          delivery_date: data[i]?.delivery_date ? dayjs(data[i]?.delivery_date).format('YYYY-MM-DD') : null,
        };
        purchaseOrderLines.push(poLine);
      }
      const payload = {
        destination_tenant_id: isInterTenantPo ? destinationTenantId : null,
        destination_department_id: isInterTenantPo ? destinationDepartmentId : null,
        is_inter_tenant_purchase_order: isInterTenantPo,
        create_automatic_sales_order: isInterTenantPo ? createAutomaticSo : false,
        subcontractor_mo_id: moInfo ? moInfo?.mo_id : (jwInfo ? jwInfo?.mo_id : null),
        subcontractor_bears_rm_cost: !selectedRmBearer,
        po_type: (moFgLines || jwInfo)
          ? 'WORK_ORDER'
          : purchaseOrderType[purchaseOrderTypeValue],
        po_number: initialPoNumber?.toLowerCase()?.trim() === poNumber?.toLowerCase()?.trim() ? null : poNumber,
        seq_id: docSeqId || null,
        tenant_seller_id: currentSelectedSeller,
        tenant_department_id: tenantDepartmentId,
        tenant_seller_info: {
          ...tenantSellerInfo, gst_number: gstNumber, seller_type: seller?.seller_type,
        },
        tenant_id: selectedTenant || clonePoTenantId || tenantId,
        purchase_order_lines: purchaseOrderLines,
        shipping_address_id: shippingAddress?.address_id,
        shipping_address: shippingAddress,
        billing_address_id: billingAddress?.address_id,
        billing_address: billingAddress,
        seller_address_id: vendorAddress?.address_id,
        seller_address: vendorAddress,
        t_and_c: global.isBlankString(terms) ? '' : terms,
        tc_id: tncId || null,
        payment_terms: [
          {
            advance_amount: 0,
            due_days: paymentTerms || 0,
            remark: remarks,
          },
        ],
        notification_recipients: toRecipients,
        is_automatic_notification_enabled: checkedRecipients,
        is_po_automatic_notification_enabled: sendWhatsappNotification,
        purchase_order_without_costing: blanketPO,
        attachments:
          fileList?.map((attachment) => ({
            url: attachment?.response?.response?.location || attachment?.url,
            type: attachment.type,
            name: attachment.name,
            uid: attachment.uid,
          })) || [],
        edd: deliveryDate ? dayjs(deliveryDate).format('YYYY-MM-DD') : null,
        po_date: poDate ? dayjs(poDate, 'YYYY/MM/DD') : null,
        custom_fields: CustomFieldHelpers.postCfStructure(cfPurchaseOrdersDoc),
        other_charges:
          chargeData?.map((charge) => ({
            charge_name: charge?.charge_name,
            charge_amount: charge?.charge_amount,
            charge_type: '',
            charge_sac_code: charge?.chargesSacCode || null,
            tax_info: charge?.chargesTaxInfo || null,
            ledger_name: charge?.ledger_name || null,
          })) || [],
        discount_percentage: discountType === 'Percent' ? discountPercentage : (discountPercentage / this.getLineTotals().totalAmount || 0) * 100,
        is_line_wise_discount: !isLineWiseDiscount,
        status: withApproval ? 'ISSUED' : 'DRAFT',
        po_expiry_date: user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.po_expiry_date ? expiryDate : null,
        conversion_rate: currencyConversionRate || selectedCurrencyName?.conversion_rate,
        org_currency_id: selectedCurrencyName?.org_currency_id,
        tcs_id: taxTypeInfo ? taxType : null,
        tcs_info: taxTypeInfo ? {
          tax_id: taxTypeInfo?.tax_id,
          tax_name: taxTypeInfo?.tax_name,
          tax_value: taxTypeInfo?.tax_value,
          tax_type_name: taxTypeInfo?.tax_type_name,
        } : null,
        charge_1_name: charge1Name,
        charge_1_value: Number(charge1Value),
        freight_tax_id: freightTaxId === 'Not Applicable' ? null : freightTaxId,
        freight_tax_info: freightTaxInfo,
        freight_sac_code: freightSacCode,
        is_discount_in_percent: discountType === 'Percent',
        is_job_works_po: !!jwInfo,
        production_route_id: jwInfo ? jwInfo?.production_route_id : null,
        // pi_id: piId,
        mrp_id: selectedPI?.mrp_id,
        ref_pr_ids: purchaseRequestData?.purchase_requisition_id?.toString() || null,
        purchase_group: isPurchaseGroupEnabled ? purchaseGroup : null,
      };
      const allProductSkuIds = data?.map((item) => item.product_sku_id);
      createPurchaseOrder([payload], (createdPo) => {
        if (location?.state?.dataForPrefillPO) {
          removeFromCartV2({ product_sku_ids: allProductSkuIds, tenant_id: selectedTenant }, () => {
            getCartV2({ tenantId: selectedTenant });
          });
        }
        if (selectedTags?.length) {
          const tagPayload = {
            entity_name: 'PURCHASE_ORDER',
            entity_id: createdPo?.purchase_orders?.[0]?.po_id,
            action: 'ADD',
            tag: selectedTags.join(','),
          };
          createTag(tagPayload, () => {
            if (moCallback) {
              moCallback();
            } else {
              history.push(`/approval?type=po&id=${createdPo?.purchase_orders?.[0]?.po_id}`);
            }
          });
        } else if (moCallback) {
          moCallback();
        } else {
          history.push(`/approval?type=po&id=${createdPo?.purchase_orders?.[0]?.po_id}`);
        }
      }, withApproval);
    } else {
      notification.open({
        type: 'error',
        duration: '5',
        message: 'Please provide input for all mandatory fields',
        placement: 'top',
      });
    }
  }

  updatePurchaseOrder(withApproval) {
    this.setState({
      actionType: withApproval ? 'SAVE_AND_ISSUED' : 'SAVE_AS_DRAFT',
    });
    const { selectedPurchaseOrder, location } = this.props;
    const {
      data, seller, shippingAddress, cfPurchaseOrdersDoc,
    } = this.state;
    this.setState({
      formSubmitted: true,
      withApproval,
      data: JSON.parse(JSON.stringify(data)),
    });
    if (
      CustomFieldHelpers.isCfValid(cfPurchaseOrdersDoc)
    ) {
      if (this.isDataValid() && seller && shippingAddress) {
        this.updatePO(withApproval);
      } else {
        notification.open({
          type: 'error',
          duration: '5',
          message: 'Please provide input for all mandatory fields',
          placement: 'top',
        });
      }
    }
  }

  updatePO(withApproval) {
    const {
      updatePurchaseOrder, history, match,
      selectedPurchaseOrder, createTag, user,
    } = this.props;
    const { poId } = match.params;
    const {
      seller, taxTypeInfo, taxType, data, shippingAddress, vendorAddress, remarks, chargeData, discountPercentage, isLineWiseDiscount, terms, paymentTerms, destinationDepartmentId,
      poDate, deliveryDate, expiryDate, moFgLines, billingAddress, fileList, discountType, tenantSellerInfo, gstNumber, toRecipients, checkedRecipients, purchaseOrderType, purchaseOrderTypeValue, cfPurchaseOrdersDoc, cfPurchaseOrdersLine, selectedTags,
      selectedCurrencyName, sendWhatsappNotification, tenantDepartmentId, charge1Value, charge1Name, freightTaxInfo, createAutomaticSo, isInterTenantPo, destinationTenantId,
      freightTaxId, freightSacCode, blanketPO, poNumber, purchaseGroup, currencyConversionRate, selectedTenant, updateDocumentReason, tncId,
    } = this.state;
    const purchaseOrderLines = [];
    const isPurchaseGroupEnabled = user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.enable_purchasing_group;
    if (
      this.isDataValid()
      && (checkedRecipients ? toRecipients?.length : true)
      && seller
      && shippingAddress
    ) {
      for (let i = 0; i < data?.length; i++) {
        let lineDiscount = 0;
        lineDiscount = data[i]?.lineDiscountType === 'Percent' ? Number(data?.[i]?.discount || 0) : ((Number(data?.[i]?.discount || 0) / (Number(data[i].quantity) * parseFloat(data[i].price))) * 100);
        const poLine = {
          secondary_uom_qty: data[i]?.secondaryUomId ? data[i]?.secondary_uom_qty : 0,
          po_line_id: data[i]?.po_line_id ? data[i].po_line_id : null,
          product_sku_id: data[i]?.product_sku_info?.product_sku_id,
          tenant_product_id: data[i]?.tenant_product_id,
          offer_price: blanketPO ? 0 : Number.parseFloat(data[i].price),
          tax_id: data[i].taxId,
          uom_id: data[i]?.uomId,
          tax_group_info: data[i]?.taxInfo,
          uom_info: [data[i]?.uomInfo],
          quantity: Number(data[i].quantity),
          line_discount_percentage: lineDiscount,
          product_sku_name: data[i]?.product_sku_name,
          remark: data[i]?.remark,
          mo_fg_id: data[i]?.mo_fg_id,
          production_route_line_id: data[i]?.production_route_line_id,
          is_discount_in_percent: data[i]?.lineDiscountType === 'Percent',
          custom_fields: CustomFieldHelpers.postCfStructure(data[i]?.lineCustomFields),
          delivery_date: data[i]?.delivery_date ? dayjs(data[i]?.delivery_date).format('YYYY-MM-DD') : null,
          pi_line_id: data?.[i]?.pi_line_id || null,
        };
        purchaseOrderLines.push(poLine);
      }
      const payload = {
        destination_tenant_id: destinationTenantId,
        destination_department_id: destinationDepartmentId,
        is_inter_tenant_purchase_order: isInterTenantPo,
        create_automatic_sales_order: createAutomaticSo,
        purchase_order_lines: purchaseOrderLines,
        po_type: moFgLines
          ? 'WORK_ORDER'
          : purchaseOrderType[purchaseOrderTypeValue],
        po_id: poId,
        po_number: poNumber,
        shipping_address_id: shippingAddress?.address_id,
        tenant_department_id: tenantDepartmentId,
        shipping_address: shippingAddress,
        seller_address_id: vendorAddress?.address_id,
        seller_address: vendorAddress,
        billing_address_id: billingAddress?.address_id,
        billing_address: billingAddress,
        t_and_c: global.isBlankString(terms) ? '' : terms,
        tc_id: tncId || null,
        payment_terms: (paymentTerms || remarks)
          ? [
            {
              advance_amount: 0,
              due_days: paymentTerms,
              remark: remarks,
            },
          ]
          : [],
        notification_recipients: toRecipients,
        is_automatic_notification_enabled: checkedRecipients,
        is_po_automatic_notification_enabled: sendWhatsappNotification,
        purchase_order_without_costing: blanketPO,
        status: withApproval ? 'ISSUED' : 'DRAFT',
        edd: deliveryDate ? dayjs(deliveryDate).format('YYYY-MM-DD') : null,
        po_date: dayjs(poDate, 'YYYY/MM/DD'),
        po_expiry_date: user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.po_expiry_date ? expiryDate : null,
        tenant_seller_id: selectedPurchaseOrder?.tenant_seller_id,
        tenant_seller_info: {
          ...tenantSellerInfo,
          gst_number: gstNumber,
          seller_type: tenantSellerInfo?.seller_type,
        },
        tenant_id: selectedTenant,
        other_charges:
          chargeData?.map((charge) => ({
            charge_name: charge?.charge_name,
            charge_amount: charge?.charge_amount,
            charge_type: '',
            charge_sac_code: charge?.chargesSacCode || null,
            tax_info: charge?.chargesTaxInfo || null,
            ledger_name: charge?.ledger_name || null,
          })) || [],
        discount_percentage: discountType === 'Percent' ? discountPercentage : (discountPercentage / this.getLineTotals().totalAmount || 0) * 100,
        is_line_wise_discount: !isLineWiseDiscount,
        attachments:
          fileList?.map((attachment) => ({
            url: attachment?.response?.response?.location || attachment?.url,
            type: attachment.type,
            name: attachment.name,
            uid: attachment.uid,
          })) || [],
        // conversion_rate: selectedCurrencyName?.conversion_rate,
        conversion_rate: currencyConversionRate || selectedCurrencyName?.conversion_rate,
        org_currency_id: selectedCurrencyName?.org_currency_id,
        tcs_id: taxTypeInfo ? taxType : null,
        tcs_info: taxTypeInfo ? {
          tax_id: taxTypeInfo?.tax_id,
          tax_name: taxTypeInfo?.tax_name,
          tax_value: taxTypeInfo?.tax_value,
          tax_type_name: taxTypeInfo?.tax_type_name,
        } : null,
        charge_1_name: charge1Name,
        charge_1_value: Number(charge1Value),
        freight_tax_id: freightTaxId === 'Not Applicable' ? null : freightTaxId,
        freight_tax_info: freightTaxInfo,
        freight_sac_code: freightSacCode,
        is_discount_in_percent: discountType === 'Percent',
        custom_fields: CustomFieldHelpers.postCfStructure(cfPurchaseOrdersDoc),
        update_document_reason: updateDocumentReason,
        purchase_group: isPurchaseGroupEnabled ? purchaseGroup : null,
      };
      updatePurchaseOrder(
        payload,
        () => {
          if (selectedTags?.length) {
            const tagPayload = {
              entity_name: 'PURCHASE_ORDER',
              entity_id: selectedPurchaseOrder?.po_id,
              action: 'REPLACE',
              tag: selectedTags.join(','),
            };
            createTag(tagPayload, () => {
              if (poId) {
                history.push(`/approval?type=po&id=${poId}`);
                this.setState({ actionType: '' });
              } else {
                history.push('purchase/purchase-orders');
                this.setState({ actionType: '' });
              }
            });
          } else if (poId) {
            history.push(`/approval?type=po&id=${poId}`);
            this.setState({ actionType: '' });
          } else {
            history.push('purchase/purchase-orders');
            this.setState({ actionType: '' });
          }
        },
        withApproval,
      );
    } else {
      notification.open({
        type: 'error',
        duration: '5',
        message: 'Please provide input for all mandatory fields',
        placement: 'top',
      });
    }
  }

  customInputChange(fieldValue, cfId) {
    const { cfPurchaseOrdersDoc } = this.state;
    const newCustomField = cfPurchaseOrdersDoc.map((customField) => {
      if (customField?.cfId === cfId) {
        if (customField?.fieldType === 'ATTACHMENT') {
          return {
            ...customField,
            fieldValue: fieldValue?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url,
              type: attachment.type,
              name: attachment.name,
              uid: attachment.uid,
            })),
          };
        }
        return {
          ...customField,
          fieldValue,
        };
      }
      return {
        ...customField,
      };
    });
    this.setState({
      cfPurchaseOrdersDoc: newCustomField,
    });
  }

  customLineInputChange(fieldValue, key, type, isManualControlCustomField) {
    const {
      data, billingAddress, vendorAddress, currencyConversionRate,
    } = this.state;
    const { user } = this.props;
    const copyData = JSON.parse(JSON.stringify(data));
    for (let i = 0; i < copyData.length; i++) {
      if (copyData[i]?.key === key) {
        copyData[i].lineCustomFields = fieldValue;
        const rateCf = fieldValue?.find((j) => j?.fieldName === 'Rate');
        const qtyCf = fieldValue?.find((j) => j?.fieldName === 'Quantity');
        if (type === 'Quantity' && !isManualControlCustomField) {
          copyData[i].price = rateCf?.fieldValue >= 0 ? (rateCf?.fieldValue / currencyConversionRate).toFixed(DEFAULT_CUR_ROUND_OFF) : '0';
        } else {
          copyData[i].price = rateCf?.fieldValue >= 0 ? rateCf?.fieldValue : '0';
        }
        copyData[i].quantity = qtyCf?.fieldValue > 0 ? qtyCf?.fieldValue : '';
        let discountValue = Number.parseFloat(copyData[i].discount) || 0;
        if (copyData[i]?.showUnitDiscount) {
          discountValue = Number.parseFloat(Number(Number(copyData[i].unitDiscount) * Number(qtyCf?.fieldValue)).toFixed(DEFAULT_CUR_ROUND_OFF));
        }
        const taxableValue = copyData[i]?.lineDiscountType === 'Percent'
          ? (qtyCf?.fieldValue * copyData[i].price) * (1 - discountValue / 100)
          : Math.max(qtyCf?.fieldValue * copyData[i].price - discountValue, 0);
        copyData[i].discount = copyData[i]?.showUnitDiscount ? discountValue : copyData[i]?.discount;
        copyData[i].child_taxes = Helpers.computeTaxation(taxableValue, copyData[i].taxInfo, vendorAddress?.state, billingAddress?.state)?.tax_info?.child_taxes;
      }
    }

    this.setState({ data: copyData }, () => {
      if (this.state?.isLineWiseDiscount) this.handleDiscountPercentageChange(this.state.discountPercentage);
    });
  }

  handleCheckboxChangeBlanketPo = (value) => {
    const { visibleColumns } = this.state;
    const newData = {
      ...visibleColumns,
      UNIT_PRICE: {
        label: 'Unit Price',
        visible: !value,
        disabled: true,
      },
      TAX: {
        label: 'Tax',
        visible: !value,
        disabled: true,
      },
      DISCOUNT: {
        label: 'Discount',
        visible: !value,
        disabled: true,
      },
      LINE_TOTAL: {
        label: 'Line Total',
        visible: !value,
        disabled: true,
      },
    };
    this.setState({ visibleColumns: newData, blanketPO: value });
  };

  handleDiscountPercentageChange = (value) => {
    const { discountType, data, billingAddress, vendorAddress } = this.state;

    const copyData = JSON.parse(JSON.stringify(data));
    copyData.map((item) => {
      const totalValue = copyData?.reduce((acc, cur) => acc + (cur.quantity * cur.price), 0);
      const discountValue = discountType === 'Percent' ? parseFloat(value) : ((item.quantity * item.price) / parseFloat(totalValue)) * parseFloat(value);

      const taxableValue = discountType === 'Percent'
        ? (item?.quantity * item?.price) * (1 - discountValue / 100)
        : Math.max(item.quantity * item?.price - discountValue, 0);

      item.discount = discountValue;
      item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, billingAddress?.state, vendorAddress?.state)?.tax_info?.child_taxes;
      return item;
    });

    this.setState({
      data: copyData,
      discountPercentage: parseFloat(value),
    });
  };

  handleMultiPIChange(selectedPIs) {
    const { getPurchaseIndents, cfV2DocPurchaseOrders, user } = this.props;
    const {
      billingAddress, vendorAddress, savedSelectedPurchaseIndentIds, data, savedData, selectedPurchaseIndentsData, fileList,
    } = this.state;

    if (this.timeOutRef.current) clearTimeout(this.timeOutRef.current);
    this.timeOutRef.current = setTimeout(() => {

      if (selectedPIs.length === 0) {
        this.setState((prevState) => ({
          visibleColumns: {
            ...prevState.visibleColumns, REQUESTED_QUANTITY: {
              label: 'Requested Quantity',
              visible: false,
              disabled: true,
            }
          },
        }));
      } else {
        this.setState((prevState) => ({
          visibleColumns: {
            ...prevState.visibleColumns, REQUESTED_QUANTITY: {
              label: 'Requested Quantity',
              visible: true,
              disabled: true,
            }
          },
        }));
      }

      const tempSelectedPIs = selectedPIs.filter((item) => !savedSelectedPurchaseIndentIds.includes(item));

      const tempSelectedPurchaseIndentsData = selectedPurchaseIndentsData.filter((item) => selectedPIs.includes(item?.pi_id));

      const allFileListUid = selectedPurchaseIndentsData?.map((item) => item?.attachments)?.flat()?.map((item) => item?.uid);

      const remainingFileList = fileList?.filter((item) => !allFileListUid.includes(item?.uid));

      const oldLines = data.filter((item) => selectedPIs.includes(item?.pi_id) || !item?.pi_id);

      const oldSavedLines = savedData.filter((item) => selectedPIs.includes(item?.pi_id) || !item?.pi_id);

      if (tempSelectedPIs.length > 0) {
        const combinedLines = [];

        getPurchaseIndents({ query: { pi_ids: tempSelectedPIs } }, (purchaseIndents) => {
          const autoPrintDescription = user?.tenant_info?.global_config?.settings?.print_description_automatically;

          const purchaseIndentData = purchaseIndents?.data || [];
          if (purchaseIndentData.length > 0) {

            const lineCFs = CustomFieldHelpers.getCfStructure(cfV2DocPurchaseOrders?.data?.document_line_custom_fields?.filter((item) => item?.is_active), false);

            for (const piData of purchaseIndentData) {
              const _data = piData?.purchase_indent_lines?.map((item) => {
                const oldCf = item?.custom_fields || [];
                return {
                  key: uuidv4(),
                  tenant_product_id: item?.tenant_product_id,
                  asset1: item?.product_sku_info?.assets?.[0]?.url || '',
                  product_sku_name: item?.product_sku_info?.product_sku_name,
                  product_sku_id: item?.product_sku_info?.product_sku_id,
                  original_qty: item?.quantity,
                  ordered_quantity: item?.ordered_quantity,
                  pending_quantity: item?.pending_quantity,
                  quantity: Math.max(item?.pending_quantity, 0),
                  purchase_status: item?.purchase_status,
                  price: 0,
                  discount: 0,
                  showUnitDiscount: false,
                  lineDiscountType: 'Amount',
                  unitDiscount: 0,
                  lineCustomFields: FormHelpers.lineSystemFieldValue(
                    lineCFs || [],
                    oldCf || [],
                  )?.map((val) => ({
                    ...val,
                    fieldValue: val?.fieldName === 'Rate' ? Number(item?.sellerOffers?.[0]?.offer_price || 0) : (val?.fieldName === 'Quantity' ? Math.max(item?.pending_quantity, 0) : val?.fieldValue),
                  })),
                  uom_info: item?.uom_info?.[0],
                  uomInfo: item?.uom_info?.[0],
                  uomId: item?.uom_info?.[0]?.uom_id,
                  uomGroup: item?.group_id,
                  uom_list: item?.product_sku_info?.uom_list,
                  taxId: item?.product_sku_info?.tax_id,
                  tax_id: item?.product_sku_info?.tax_id,
                  tax_info: item?.product_sku_info?.tax_info,
                  taxInfo: item?.product_sku_info?.tax_info,
                  child_taxes: Helpers.computeTaxation(0, item?.product_sku_info?.tax_info, billingAddress?.state, vendorAddress?.state)?.tax_info?.child_taxes,
                  sku: item?.product_sku_info?.product_sku_id,
                  product_sku_info: item?.product_sku_info,
                  remarkRequired: autoPrintDescription ? !!item?.product_sku_info?.description?.replace(/<[^>]+>/g, '') : false,
                  pi_line_id: item?.pi_line_id,
                  pi_id: item?.pi_id,
                  pi_number: piData?.pi_number,
                };
              });

              combinedLines.push(..._data);
            }
          }
          const _tempPIData = [...tempSelectedPurchaseIndentsData, ...purchaseIndentData];
          this.setState({
            data: [...oldLines, ...combinedLines],
            savedData: [...oldSavedLines, ...combinedLines],
            fileList: [...remainingFileList, ..._tempPIData.flatMap((item) => item.attachments)],
            selectedTags: [...new Set(_tempPIData.map((item) => item?.tags).filter(Boolean)?.flat())],
            savedSelectedPurchaseIndentIds: selectedPIs,
            selectedPurchaseIndentsData: _tempPIData,
          });
        });

      } else {
        this.setState({
          data: oldLines?.length > 0 ? oldLines : [
            {
              key: uuidv4(),
              asset1: '',
              product_sku_name: '',
              quantity: '',
              price: '',
              lot: 0,
              taxId: 0,
              discountType: 'Percent',
              child_taxes: [{
                tax_amount: 0,
                tax_type_name: '',
              }],
              lineCustomFields: [],
            },
          ],
          savedData: oldSavedLines,
          fileList: [...remainingFileList, ...tempSelectedPurchaseIndentsData.flatMap((item) => item.attachments)],
          selectedTags: [...new Set(tempSelectedPurchaseIndentsData?.map((item) => item?.tags).filter(Boolean)?.flat())],
          savedSelectedPurchaseIndentIds: selectedPIs,
          selectedPurchaseIndentsData: tempSelectedPurchaseIndentsData,
        });
      }

    }, 1000);
  }

  render() {
    const {
      user, getDocConfigPOLoading, createPurchaseOrderLoading, getAddresses, updatePoStatusLoading, updatePurchaseOrderLoading,
      getPurchaseOrderByIdLoading, selectedPurchaseOrder, grnData, match, location, MONEY, moFgLines, tenantId, jwInfo, moInfo, priceMasking, getTenantsConfiguration, getDocConfig, selectedPI, taxesGroup, getTaxesLoading, cfV2DocPurchaseOrders, getPurchaseIndentsLoading
    } = this.props;
    const {
      seller, data, savedData, formSubmitted, showNewVendorModal, currentSelectedSeller, vendorAddress, shippingAddress, isInterTenantPo, createAutomaticSo, destinationTenantId, docSeqId, destinationDepartmentId,
      terms, tncId, withApproval, selectedPaymentTerm, poDate, showAddressDrawer, selectedAddressType, deliveryDate, expiryDate, poNumber, billingAddress, sendWhatsappNotification, selectedTags,
      cfPurchaseOrdersDoc, fileList, toRecipients, chargeData, isLineWiseDiscount, discountPercentage, blanketPO,
      actionType, gstNumber, remarks, paymentTerms, checkedRecipients, isMarketplaceSeller, purchaseOrderTypeValue, productType, taxTypeId, visibleColumns, cfPurchaseOrdersLine,
      tenantSellerInfo, selectedCurrencyID, selectedCurrencyName, tenantDepartmentId, charge1Name, charge1Value, discountType, freightTaxId, freightTaxInfo, openFreightTax, freightSacCode, freightTax, selectedRmBearer, isAutomaticConversionRate, currencyConversionRate, isManualControlCF, selectedTenant, updateDocumentReason, freightTaxData, purchaseGroup, selectedPurchaseIndentIds, selectedPurchaseIndentsData,
    } = this.state;
    const isCreatePage = !match.params?.poId && !location.state?.clonePoId;
    const isClonePo = !!location.state?.clonePoId;
    const purchaseRequestData = location.state?.purchaseRequestData;
    const clonePoTenantId = location?.state?.selectedTenantId;
    const deliveryDateMandatory = user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.delivery_date_mandatory;
    const isPurchaseOrderEnable = user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.is_active;
    const vendorAddressMandatory = user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.seller_address_required;
    const accountingGSTTransactionAsPerMaster = user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.gst_details_in_transaction === 'AS_PER_MASTER';
    const isPurchaseGroupEnabled = user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.enable_purchasing_group;
    const purchaseRequestTenantId = purchaseRequestData?.issuer_tenant_id || purchaseRequestData?.tenant_id;

    let copyDocLevelError;
    let copyLineLevelError;
    if (formSubmitted) {
      const { docLevelError, lineLevelError } = this.getPurchaseOrderErrors();
      copyDocLevelError = docLevelError;
      copyLineLevelError = lineLevelError;
    }

    const { isDataMaskingPolicyEnable, isHideCostPrice } = priceMasking;

    const isSecondaryUomEnabled = user?.tenant_info?.global_config?.settings?.enable_secondary_uom;

    const splitChargesData = (charge) => {
      const chargeWithTaxName = charge?.filter((line) => line?.chargesTaxInfo?.tax_id) || [];
      const chargeWithoutTaxName = charge?.filter((line) => !line?.chargesTaxInfo?.tax_id) || [];
      return { chargeWithTaxName, chargeWithoutTaxName };
    };

    const companyOptions = [
      {
        label: 'My Company',
        value: true,
      },
      {
        label: 'Subcontractor',
        value: false,
      },
    ];

    const renderCharges = (charge) => charge?.map((line) => (
      <div
        key={line?.chargeKey}
        className="form-calculator__field"
      >
        <div className="form-calculator__field-name">
          {!user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.use_custom_charges ? (
            <div className="select_extra_charge_wrapper">
              <SelectExtraCharge
                containerClassName="orgInputContainer"
                selectedChargeName={line.charge_name}
                disabled={
                  createPurchaseOrderLoading
                  || updatePurchaseOrderLoading
                  || updatePoStatusLoading || getDocConfigPOLoading || getDocConfigPOLoading || !isPurchaseOrderEnable
                }
                onChange={(value) => {
                  const copyChargeData = JSON.parse(JSON.stringify(chargeData));
                  copyChargeData.map((item) => {
                    if (item.chargeKey === line.chargeKey) {
                      item.charge_name = value?.ledger_name;
                      item.chargesSacCode = (value?.charge_sac_code) || null;
                    }
                  });
                  this.setState({ chargeData: copyChargeData });
                }}
                customStyle={{
                  width: '220px',
                  backgroundColor: 'white',
                }}
                excludeCharges={chargeData?.map((item) => item?.charge_name)}
                entityName="PURCHASE"
              />
              {line?.chargesTax && (
                <div style={{
                  color: '#2d7df7',
                  fontWeight: '400',
                  fontSize: '12px',
                }}
                >
                  {`tax@${line?.chargesTax}%`}
                </div>
              )}
            </div>
          ) : (
            <H3FormInput
              value={line?.charge_name}
              type="text"
              containerClassName={`orgInputContainer ${formSubmitted
                && Number(line?.charge_name) <= 0
                ? 'form-error__input'
                : ''
                }`}
              placeholder="Charge name.."
              labelClassName="orgFormLabel"
              inputClassName="orgFormInput"
              onChange={(e) => {
                const copyChargeData = JSON.parse(
                  JSON.stringify(chargeData),
                );
                copyChargeData.map((item) => {
                  if (item.chargeKey === line.chargeKey) {
                    // eslint-disable-next-line no-param-reassign
                    item.charge_name = e.target.value;
                  }
                  return data;
                });
                this.setState({ chargeData: copyChargeData });
              }}
            />
          )}

        </div>
        <div className="form-calculator__field-value" style={{ display: 'flex', gap: '0px' }}>
          <div style={{ width: '140px', marginRight: '-27px' }}>
            <H3FormInput
              value={line?.charge_amount}
              type="number"
              containerClassName={`orgInputContainer ${formSubmitted
                && Number(line?.charge_amount) <= 0
                ? 'form-error__input'
                : ''
                }`}
              labelClassName="orgFormLabel"
              inputClassName="orgFormInput"
              onChange={(e) => {
                const copyChargeData = JSON.parse(
                  JSON.stringify(chargeData),
                );
                copyChargeData.map((item) => {
                  if (item.chargeKey === line.chargeKey) {
                    const updatedChargeAmount = parseFloat(e.target.value) || 0;

                    // Update charge_amount
                    item.charge_amount = updatedChargeAmount;
                    // Compute chargesTaxData with updated values
                    const computedTaxData = Helpers.computeTaxation(
                      updatedChargeAmount,
                      line.chargesTaxInfo,
                      billingAddress?.state,
                      vendorAddress?.state,
                    );
                    // Update chargesTaxData
                    item.chargesTaxData = {
                      ...line?.chargesTaxInfo,
                      child_taxes: computedTaxData?.tax_info?.child_taxes || [],
                    };
                  }
                  return data;
                });
                this.setState({ chargeData: copyChargeData });
              }}
              allowNegative
            />
          </div>
          {!isVendorOverseas && (<ChargesTaxInput
            selectedTenant={selectedTenant}
            openChargesTax={line?.openChargesTax}
            setOpenChargesTax={(value) => {
              const copyChargeData = JSON.parse(
                JSON.stringify(chargeData),
              );
              copyChargeData.map((item) => {
                if (item.chargeKey === line.chargeKey) {
                  // eslint-disable-next-line no-param-reassign
                  item.openChargesTax = value;
                }
                return data;
              });
              this.setState({ chargeData: copyChargeData });
            }}
            sacCode={line?.chargesSacCode}
            setSacCode={(value) => {
              const copyChargeData = JSON.parse(
                JSON.stringify(chargeData),
              );
              copyChargeData.map((item) => {
                if (item.chargeKey === line.chargeKey) {
                  // eslint-disable-next-line no-param-reassign
                  item.chargesSacCode = value;
                }
                return data;
              });
              this.setState({ chargeData: copyChargeData });
            }}
            chargesTaxId={line?.chargesTaxId}
            setChargesTaxData={(value) => {
              const updatedChargeData = chargeData?.map((item) => {
                if (item.chargeKey === line.chargeKey) {
                  return {
                    ...item,
                    chargesTaxId: !value ? 'Not Applicable' : value?.tax_id,
                    chargesTax: !value ? null : value?.tax_value,
                    chargesTaxInfo: !value ? null : value,
                    chargesTaxData: !value ? {
                      child_taxes: [
                        {
                          tax_amount: 0,
                          tax_type_name: '',
                        },
                      ],
                    } : {
                      ...value,
                      child_taxes: Helpers.computeTaxation(line?.charge_amount, value, billingAddress?.state, vendorAddress?.state)?.tax_info?.child_taxes,
                    },
                  };
                }
                return item;
              });
              this.setState({ chargeData: updatedChargeData });
            }}
          />)}
          <div
            className="form-calculator__delete-line-button"
            onClick={() => this.handleDeleteCharge(line?.chargeKey)}
          >
            <FontAwesomeIcon
              icon={faCircleXmark}
              size="sm"
              style={{ color: '#6f7276' }}
            />
          </div>
        </div>
      </div>
    ));

    const getDataSource = (dataSource) => {
      const copyData = JSON.parse(JSON.stringify(dataSource));
      return [...copyData.filter((item) => item?.product_sku_id), ...copyData.filter((item) => !item?.product_sku_id)];
    };

    const allTenant = Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.PURCHASE_ORDER, Helpers.permissionTypes.CREATE);

    const isVendorOverseas = (seller?.seller_type === 'OVERSEAS' || selectedPurchaseOrder?.seller_info?.seller_type === 'OVERSEAS') && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;

    const hasPIReadPermission = Helpers.getPermission(Helpers.permissionEntities.PURCHASE_INDENT, Helpers.permissionTypes.READ, user);

    return (
      <Fragment>
        <Drawer
          open={showAddressDrawer}
          onClose={() => this.setState({ showAddressDrawer: false, selectedAddressType: '' })}
          width="360"
          destroyOnClose
        >
          <AddressSelector
            title={
              selectedAddressType === 'SELLER'
                ? 'Vendor Address'
                : (selectedAddressType === 'TENANT_SHIPPING'
                  ? 'Delivery Address'
                  : 'Billing Address')
            }
            addressType={selectedAddressType === 'DESTINATION_TENANT_BILLING' ? 'TENANT' : selectedAddressType?.split('_')[0]}
            selectedAddressId={
              selectedAddressType === 'DESTINATION_TENANT_BILLING'
                ? vendorAddress?.address_id
                : selectedAddressType === 'TENANT_SHIPPING'
                  ? shippingAddress?.address_id
                  : selectedAddressType === 'TENANT_BILLING'
                    ? billingAddress?.address_id
                    : vendorAddress?.address_id
            }
            onAddressChange={(address) => {
              if (selectedAddressType === 'DESTINATION_TENANT_BILLING' && isInterTenantPo) {
                this.setState({
                  vendorAddress: address,
                  showAddressDrawer: false,
                });
              } else if (selectedAddressType === 'TENANT_SHIPPING') {
                this.setState({
                  shippingAddress: address,
                  showAddressDrawer: false,
                });
              } else if (selectedAddressType === 'TENANT_BILLING') {
                this.setState({
                  billingAddress: address,
                  showAddressDrawer: false,
                  data: data?.map((record) => {
                    const discountValue = Number(record?.discount) || 0;
                    const taxableValue = record?.is_discount_in_percent
                      ? (record?.quantity * record?.price) * (1 - discountValue / 100)
                      : Math.max(record?.quantity * record?.price - discountValue, 0);
                    return {
                      ...record,
                      child_taxes: data?.[0]?.product_sku_name ? Helpers.computeTaxation(taxableValue, record.taxInfo, address?.state, vendorAddress?.state)?.tax_info?.child_taxes : [{
                        tax_amount: 0,
                        tax_type_name: '',
                      }],
                    };
                  }),
                });
              } else {
                this.setState({
                  vendorAddress: address,
                  showAddressDrawer: false,
                  data: data?.map((record) => {
                    const discountValue = Number(record?.discount) || 0;
                    const taxableValue = record?.is_discount_in_percent
                      ? (record?.quantity * record?.price) * (1 - discountValue / 100)
                      : Math.max(record.quantity * record?.price - discountValue, 0);
                    return {
                      ...record,
                      child_taxes: data?.[0]?.product_sku_name ? Helpers.computeTaxation(taxableValue, record.taxInfo, billingAddress?.state, address?.state)?.tax_info?.child_taxes : [{
                        tax_amount: 0,
                        tax_type_name: '',
                      }],
                    };
                  }),
                });
              }
            }}
            entityId={
              selectedAddressType === 'DESTINATION_TENANT_BILLING'
                ? destinationTenantId
                : selectedAddressType === 'SELLER'
                  ? seller?.seller_id
                  : isCreatePage
                    ? (selectedTenant || clonePoTenantId || tenantId)
                    : selectedPurchaseOrder?.tenant_info?.tenant_id
            }
            entityType={selectedAddressType === 'SELLER' ? 'SELLER' : 'TENANT'}
            seller={currentSelectedSeller}
            tenantId={selectedTenant || clonePoTenantId || tenantId}
          />
        </Drawer>
        {getPurchaseOrderByIdLoading && !isCreatePage ? (
          <FormLoadingSkull />
        ) : (
          <React.Fragment>

            <div
              className="form__wrapper"
              style={{ paddingTop: (moFgLines || jwInfo || !!selectedPI) ? '0px' : '90px' }}
            >
              <div className="ant-row">
                <div className="ant-col-md-24">
                  <div className="form__section">
                    <div className="flex-display flex-align-c mg-bottom-5 pd-right-15">
                      <H3Text text="PART A" className="form__section-title" />
                      <div className="form__section-line" />
                      <div style={{ display: 'flex', justifyContent: 'end', alignItems: 'center' }}>
                        <CurrencyConversionV2
                          selectedCurrencyName={selectedCurrencyName}
                          selectedCurrencyID={selectedCurrencyID}
                          isAutomaticConversionRate={isAutomaticConversionRate}
                          currencyConversionRate={currencyConversionRate}
                          setCurrencyConversionRate={(val) => this.setState({ currencyConversionRate: val })}
                          setIsAutomaticConversionRate={(val) => this.setState({ isAutomaticConversionRate: val })}
                          setSelectedCurrencyName={(val) => this.setState({ selectedCurrencyName: val })}
                          setSelectedCurrencyID={(val) => this.setState({ selectedCurrencyID: val })}
                          callback={(conversionRate) => {
                            const updatedData = data?.map((item) => {
                              const convertedPrice = (conversionRate && item?.OriginalPrice)
                                ? (Number(item?.OriginalPrice) / conversionRate).toFixed(DEFAULT_CUR_ROUND_OFF)
                                : item.price;
                              const convertedLastUpdatedPrice = (conversionRate && item?.OriginalPrice)
                                ? (Number(item?.OriginalPrice) / conversionRate).toFixed(DEFAULT_CUR_ROUND_OFF)
                                : item.lastUpdatedPrice;
                              return {
                                ...item,
                                price: convertedPrice,
                                lastUpdatedPrice: convertedLastUpdatedPrice,
                              };
                            });
                            this.setState({
                              data: updatedData,
                            });
                          }}
                        />
                        <CustomDocumentInputs
                          customFields={cfPurchaseOrdersDoc}
                          updateCustomFields={(cf) => this.setState({ cfPurchaseOrdersDoc: cf })}
                        />
                      </div>
                    </div>
                    <div className="form__section-inputs mg-bottom-20">
                      {(formSubmitted && (copyDocLevelError?.length > 0 || copyLineLevelError?.length > 0)) && (
                        <ErrorHandle message="Mandatory fields required" docLevelErrors={copyDocLevelError} lineLevelErrors={copyLineLevelError} />
                      )}
                      <div className="ant-row">
                        <div className="ant-col-md-24">
                          {!moFgLines && !jwInfo && (
                            <div className="form__input-row"
                              style={{
                                alignItems: 'center', marginBottom: '15px', display: 'flex', justifyContent: 'space-between',
                              }}
                            >
                              <Radio.Group
                                disabled={
                                  createPurchaseOrderLoading || updatePoStatusLoading || getDocConfigPOLoading || selectedPI || purchaseRequestData?.purchase_requisition_id || (selectedPurchaseOrder?.ref_pr_ids && !isClonePo) || selectedPurchaseOrder?.pi_details?.length
                                }
                                onChange={(event) => {
                                  const remainingStates = selectedPurchaseIndentIds.length > 0 ? {
                                    selectedPurchaseIndentIds: [],
                                    savedSelectedPurchaseIndentIds: [],
                                    selectedPurchaseIndentsData: [],
                                    savedData: [],
                                    data: [],
                                    fileList: [],
                                    selectedTags: [],
                                  } : {};
                                  this.setState({
                                    purchaseOrderTypeValue: event.target.value,
                                    productType:
                                      event.target.value === 2 ? 'SERVICE' : '',
                                    formSubmitted: false,
                                    ...remainingStates,
                                  });
                                  this.handleCheckboxChangeBlanketPo(event.target.value === 3);
                                }}
                                value={purchaseOrderTypeValue}
                                className="mg-top-5"
                              >
                                <Radio value={3}>Blanket Order</Radio>
                                <Radio value={1}>Purchase Order </Radio>
                                <Radio value={2}>Work Order</Radio>
                              </Radio.Group>
                              <div>
                                <Checkbox
                                  disabled={
                                    createPurchaseOrderLoading || updatePoStatusLoading || getDocConfigPOLoading || match?.params?.poId
                                  }
                                  checked={isInterTenantPo}
                                  onChange={(e) => {
                                    const lineCFs = CustomFieldHelpers.getCfStructure(cfV2DocPurchaseOrders?.data?.document_line_custom_fields?.filter((item) => item?.is_active), false);
                                    this.setState({
                                      isInterTenantPo: e.target.checked,
                                      createAutomaticSo: e.target.checked,
                                      gstNumber: '',
                                      cfPurchaseOrdersDoc: CustomFieldHelpers.getCfStructure(cfV2DocPurchaseOrders?.data?.document_custom_fields, true),
                                      cfPurchaseOrdersLine: lineCFs,
                                      visibleColumns: CustomFieldHelpers.updateVisibleColumns(
                                        cfV2DocPurchaseOrders?.data?.document_line_custom_fields,
                                        visibleColumns
                                      ),
                                      ...((purchaseRequestData || selectedPI || (!selectedPI && selectedPurchaseIndentIds?.length)) ? {} : {
                                        data: [
                                          {
                                            key: uuidv4(),
                                            asset1: '',
                                            product_sku_name: '',
                                            quantity: '',
                                            price: '',
                                            lot: 0,
                                            taxId: 0,
                                            discountType: 'Percent',
                                            child_taxes: [
                                              {
                                                tax_amount: 0,
                                                tax_type_name: '',
                                              },
                                            ],
                                            lineCustomFields: [],
                                          },
                                        ],
                                        selectedTags: [],
                                      }),
                                      isMarketplaceSeller: false,
                                      seller: null,
                                      showNewVendorModal: false,
                                      currentSelectedSeller: null,
                                      vendorAddress: '',
                                      paymentTerms: '',
                                      tenantSellerInfo: null,
                                      toRecipients: [],
                                      selectedCurrencyID: '',
                                      selectedCurrencyName: '',
                                      currencyConversionRate: '',
                                      remarks: '',
                                      destinationTenantId: '',
                                      destinationDepartmentId: '',
                                      purchaseGroup: [],
                                    });
                                  }}
                                />
                                <span
                                  style={{
                                    fontWeight: '500',
                                    fontSize: '12px',
                                    marginLeft: '5px',
                                    paddingRight: '15px',
                                  }}
                                >
                                  Inter Business Unit Purchase Order
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                        <DocumentNumberSeqInput
                          valueFromProps={poNumber}
                          updateCase={match?.params?.poId}
                          setInitialDocSeqNumber={(value) => this.setState({ initialPoNumber: value })}
                          entityName="PURCHASE_ORDER"
                          docSeqId={docSeqId}
                          tenantId={selectedTenant || clonePoTenantId || tenantId}
                          onChangeFromProps={(event, newValue, seqId) => {
                            this.setState({
                              poNumber: newValue ? (newValue || '') : (event?.target?.value || ''),
                              docSeqId: seqId,
                            });
                          }}
                          docTitle="Purchase Order#"
                          formSubmitted={formSubmitted}
                        />
                        <div className="ant-col-md-6">
                          <TenantSelector
                            selectedTenant={selectedTenant}
                            showSearch
                            onChange={(value) => {
                              this.setState({
                                tncId: null,
                                selectedTenant: value,
                                destinationTenantId: '',
                                destinationDepartmentId: '',
                                tenantDepartmentId: user?.user_tenants?.find((item) => item?.tenant_id === value)?.default_store_id,
                                sellerId: '',
                                sellerName: '',
                                sellerGst: '',
                                tenantSellerId: '',
                                vendorAddress: '',
                                finishedGoods: [{
                                  key: uuidv4(), asset1: '', product_name: '', quantity: '', unitPrice: '', lot: 0, taxId: '', discount: 0,
                                }],
                                bomByProducts: [],
                                extraCharges: [],
                                bomLines: [],
                                productionRoutes: [],
                                currentTab: '/finished-goods',
                                currentSelectedSeller: '',
                                data: [
                                  {
                                    key: uuidv4(),
                                    asset1: '',
                                    product_sku_name: '',
                                    quantity: '',
                                    price: '',
                                    lot: 0,
                                    taxId: 0,
                                    discountType: 'Percent',
                                    child_taxes: [{
                                      tax_amount: 0,
                                      tax_type_name: '',
                                    }],
                                    lineCustomFields: [],
                                  },
                                ],
                                gstNumber: '',
                                selectedTags: [],
                                selectedPurchaseIndentIds: [],
                                selectedPurchaseIndentsData: [],
                                savedSelectedPurchaseIndentIds: [],
                                savedData: [],
                                fileList: [],
                              });
                              getTenantsConfiguration(value, (tenantData) => {
                                this.setState({
                                  billingAddress: tenantData?.default_billing_address_info,
                                  shippingAddress: tenantData?.default_shipping_address_info,
                                });
                              });
                            }}
                            showAll={false}
                            title="Location"
                            customStyle={{ height: '28px', border: 'none' }}
                            labelClassName="form__input-row__label"
                            inputClassName="form__input-row__input"
                            containerClassName="form__input-row"
                            noDropdownAlign
                            placeholder="Select Business Unit"
                            includedTenants={Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.PURCHASE_ORDER, Helpers.permissionTypes.CREATE)}
                            // disabled={selectedMO}
                            disabled={!!selectedPurchaseOrder && !clonePoTenantId || !!selectedPI || !!purchaseRequestData?.purchase_requisition_id}
                          />
                        </div>
                        <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <H3Text text="Department" className="form__input-row__label" />
                            <div className="form__input-row__input">
                              <SelectDepartment
                                hideTitle
                                tenantId={selectedTenant || clonePoTenantId || selectedPurchaseOrder?.tenant_info?.tenant_id}
                                selectedDepartment={tenantDepartmentId}
                                noDropdownAlign
                                onChange={(value) => {
                                  const remainingStates = selectedPurchaseIndentIds.length > 0 ? {
                                    selectedPurchaseIndentIds: [],
                                    savedSelectedPurchaseIndentIds: [],
                                    selectedPurchaseIndentsData: [],
                                    savedData: [],
                                    data: [],
                                    fileList: [],
                                    selectedTags: [],
                                  } : {};
                                  this.setState({
                                    tenantDepartmentId: value?.tenant_department_id,
                                    ...remainingStates,
                                  });
                                }}
                                tenentLevelDepartment
                                emptyNotAllowed
                                loading={createPurchaseOrderLoading || updatePurchaseOrderLoading}
                                disabled={createPurchaseOrderLoading || updatePurchaseOrderLoading || match?.params?.poId || moFgLines || jwInfo || !selectedTenant}
                                labelClassName="mo-form__input-row__label"
                                inputClassName="orgFormInput input"
                              />
                            </div>
                          </div>
                        </div>
                        {purchaseOrderTypeValue === 1 && user?.tenant_info?.purchase_config?.sub_modules?.purchase_indent?.is_active && (!purchaseRequestData && !moFgLines && !jwInfo && selectedPurchaseOrder ? selectedPurchaseOrder?.pi_details?.length : true) && (<div className="ant-col-md-6">
                          <div className="form__input-row">
                            <H3Text text="Purchase Indent" className="form__input-row__label" />
                            <div className="form__input-row__input">
                              <PurchaseIndentSelector
                                selectedPurchaseIndentIds={selectedPurchaseIndentIds}
                                selectedTenant={selectedTenant}
                                selectedTenantDepartment={tenantDepartmentId}
                                status={['ISSUED']}
                                limit={30}
                                mode={'multiple'}
                                disabled={createPurchaseOrderLoading || updatePurchaseOrderLoading || !hasPIReadPermission || selectedPurchaseOrder?.pi_details?.length === 1 || selectedPI}
                                loading={createPurchaseOrderLoading || updatePurchaseOrderLoading}
                                onChange={(selectedPIs) => {
                                  this.setState({
                                    selectedPurchaseIndentIds: selectedPIs,
                                  });
                                  this.handleMultiPIChange(selectedPIs);
                                }}
                              />
                            </div>
                          </div>
                        </div>)}
                        <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <div className="form__input-row__label">
                              Purchase Order Date
                              <span style={{ color: 'red' }}>{'  *'}</span>
                            </div>
                            <div className={`form__input-row__input ${(formSubmitted && !poDate) ? 'form__input-row__input-error' : ''}`}>
                              <DatePicker
                                value={poDate}
                                onChange={(value) => {
                                  this.setState({ poDate: value });
                                }}
                                format="DD-MM-YYYY"
                                style={{
                                  border: '1px solid rgba(68, 130, 218, 0.2)',
                                  borderRadius: '2px',
                                  height: '28px',
                                  padding: '1px 3px',
                                  width: '100%',
                                  background: 'white',
                                  marginBottom:
                                    formSubmitted && !poDate ? '0px' : '10px',
                                }}
                              />
                            </div>
                          </div>
                        </div>
                        <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <H3Text
                              text="Delivery Date"
                              className="form__input-row__label"
                              required={deliveryDateMandatory}
                            />
                            <div className={`form__input-row__input ${(formSubmitted && !deliveryDate && deliveryDateMandatory) ? 'form__input-row__input-error' : ''}`}>
                              <DatePicker
                                value={deliveryDate}
                                onChange={(value) => {
                                  this.setState({ deliveryDate: value });
                                }}
                                format="DD-MM-YYYY"
                                style={{
                                  border: (formSubmitted && !deliveryDate && deliveryDateMandatory) ? '1px solid red' : '1px solid rgba(68, 130, 218, 0.2)',
                                  borderRadius: '2px',
                                  height: '28px',
                                  padding: '1px 3px',
                                  width: '100%',
                                  background: 'white',
                                  marginBottom: formSubmitted && !deliveryDate ? '0px' : '10px',
                                }}
                              />
                            </div>
                          </div>
                        </div>
                        {!isInterTenantPo ? (
                          <div className="ant-col-md-6">
                            {(isCreatePage) || isClonePo ? (
                              <SelectSellerV2
                                selectedSeller={currentSelectedSeller}
                                onChange={(value) => {
                                  const docCf = CustomFieldHelpers.postCfStructure(cfPurchaseOrdersDoc?.filter((item) => (item?.isActive && item?.visible))) || [];
                                  const vendorCf = value?.custom_field_values?.filter((item) => (item?.is_active)) || [];
                                  const mergedCf = CustomFieldHelpers.mergeCustomFields(docCf, vendorCf) || [];

                                  const isVendorOverseas = (value?.seller_info?.seller_type === 'OVERSEAS') && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;

                                  this.setState({
                                    isMarketplaceSeller: value?.seller_info?.is_marketplace_seller,
                                    seller: value?.seller_info,
                                    currentSelectedSeller: value?.tenant_seller_id,
                                    vendorAddress: value?.seller_info?.office_address_details,
                                    paymentTerms: value?.seller_info?.default_payment_terms?.due_days,
                                    tenantSellerInfo: {
                                      tenant_seller_id: value?.tenant_seller_id,
                                      seller_id: value?.seller_info?.seller_id,
                                      seller_name: value?.seller_info?.seller_name,
                                      email_id: value?.seller_info?.email_id_1,
                                      mobile: value?.seller_info?.mobile_1,
                                    },
                                    gstNumber: value?.seller_info?.gst_number,
                                    toRecipients: value?.seller_info?.email_id_1
                                      ? [value?.seller_info?.email_id_1]
                                      : [],
                                    data: data?.map((record) => {
                                      const discountValue = Number(record?.discount) || 0;
                                      const taxableValue = record?.is_discount_in_percent
                                        ? (record?.quantity * record?.price) * (1 - discountValue / 100)
                                        : Math.max(record?.quantity * record?.price - discountValue, 0);
                                      return {
                                        ...record,
                                        child_taxes: data?.[0]?.product_sku_name ? Helpers.computeTaxation(taxableValue, isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : record.taxInfo, billingAddress?.state, value?.seller_info?.office_address_details?.state)?.tax_info?.child_taxes : [{
                                          tax_amount: 0,
                                          tax_type_name: '',
                                        }],
                                        taxInfo: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0) : record?.taxInfo,
                                        taxId: isVendorOverseas ? taxesGroup?.data?.find((tax) => tax?.tax_value === 0)?.tax_id : record?.taxId,
                                      };
                                    }),
                                    selectedCurrencyID: value?.org_currency_id,
                                    selectedCurrencyName: value?.currency_info,
                                    currencyConversionRate: isAutomaticConversionRate ? value?.currency_info?.automatic_conversion_rate : value?.currency_info?.conversion_rate,
                                    cfPurchaseOrdersDoc: selectedPI ? (cfPurchaseOrdersDoc) : mergedCf,
                                  });
                                  getAddresses(1, value?.seller_id, 'SELLER');
                                }}
                                containerClass="orgInputContainer form__input-row"
                                inputClassName={`form__input-row__input ${(formSubmitted && !currentSelectedSeller) ? 'form__input-row__input-error' : ''}`}
                                labelClassName="form__input-row__label"
                                disabled={
                                  createPurchaseOrderLoading
                                  || updatePoStatusLoading || getDocConfigPOLoading
                                }
                                showAddVendor
                                isSubcontractor={!!moInfo}
                                tenantId={selectedTenant || clonePoTenantId || tenantId}
                              />
                            ) : (
                              <div
                                className="form__input-row"
                                style={{ marginBottom: '10px' }}
                              >
                                <H3Text
                                  text="Vendor"
                                  className="form__input-row__label"
                                />
                                <H3Text
                                  text={jwInfo ? seller?.seller_name : selectedPurchaseOrder?.tenant_seller_info?.seller_name}
                                  className="form__input-row__text"
                                />
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="ant-col-md-6">
                            <TenantSelector
                              selectedTenant={destinationTenantId}
                              showSearch
                              onChange={(value) => {
                                const matchedTenant = user?.user_tenants?.find((item) => item?.tenant_id === value);
                                this.setState({
                                  destinationTenantId: value,
                                  destinationDepartmentId: user?.user_tenants?.find((item) => item?.tenant_id === value)?.tenant_department_info?.department_id,
                                  gstNumber: matchedTenant?.gst_number,
                                  vendorAddress: matchedTenant?.default_billing_address_info,
                                });
                              }}
                              showAll={false}
                              title="Destination Location"
                              customStyle={{ height: '28px', border: 'none' }}
                              labelClassName="form__input-row__label"
                              inputClassName="form__input-row__input"
                              containerClassName="form__input-row"
                              noDropdownAlign
                              placeholder="Select Business Unit"
                              includedTenants={allTenant?.filter((item) => item !== selectedTenant)}
                              disabled={!!selectedPurchaseOrder && !clonePoTenantId}
                            />
                          </div>
                        )}
                        {isInterTenantPo && <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <H3Text text="Destination Department" className="form__input-row__label" />
                            <div className="form__input-row__input">
                              <SelectDepartment
                                hideTitle
                                tenantId={destinationTenantId}
                                selectedDepartment={destinationDepartmentId}
                                noDropdownAlign
                                onChange={(value) => {
                                  this.setState({
                                    destinationDepartmentId: value?.department_id,
                                  });
                                }}
                                emptyNotAllowed
                                loading={createPurchaseOrderLoading || updatePurchaseOrderLoading}
                                disabled={!!selectedPurchaseOrder && !clonePoTenantId}
                                labelClassName="mo-form__input-row__label"
                                inputClassName="orgFormInput input"
                              />
                            </div>
                          </div>
                        </div>}
                        <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <H3Text
                              text="GST Number"
                              className="form__input-row__label"
                            />
                            <H3FormInput
                              name="GST Number"
                              type="text"
                              disabled={
                                !seller
                                || createPurchaseOrderLoading
                                || updatePoStatusLoading || getDocConfigPOLoading
                                || accountingGSTTransactionAsPerMaster
                              }
                              containerClassName="orgInputContainer form__input-row__input"
                              labelClassName="orgFormLabel"
                              inputClassName="orgFormInput input"
                              placeholder=""
                              onChange={(event) => {
                                this.setState({ gstNumber: event.target.value });
                              }}
                              value={gstNumber}
                            />
                          </div>
                        </div>
                        <div className="ant-col-md-6">
                          <SelectPaymentTerm
                            selectedPaymentTerm={paymentTerms}
                            onChange={(value) => {
                              this.setState({ paymentTerms: value?.due_days });
                            }}
                            callback={(value) => {
                              this.setState({ paymentTerms: Number(value) });
                            }}
                            containerClassName="orgInputContainer form__input-row"
                            inputClassName=" form-seller__selector form__input-row__input"
                            labelClassName="form__input-row__label"
                            showError={formSubmitted && !selectedPaymentTerm}
                            disabled={
                              createPurchaseOrderLoading || updatePoStatusLoading || getDocConfigPOLoading
                            }
                            showAddPaymentTerm
                            placeholder="Select Payment Term"
                          />
                        </div>
                        <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <H3Text text="Payment Remarks" className="form__input-row__label" />
                            <H3FormInput
                              name="payment terms remarks"
                              type="text"
                              containerClassName="orgInputContainer form__input-row__input"
                              labelClassName="orgFormLabel"
                              inputClassName="orgFormInput input"
                              onChange={(event) => this.setState({ remarks: event.target.value })}
                              maxlength="100"
                              value={remarks}
                              disabled={
                                createPurchaseOrderLoading || updatePoStatusLoading || getDocConfigPOLoading
                              }
                            />
                          </div>
                        </div>
                        {user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.po_expiry_date && (
                          <div className="ant-col-md-6">
                            <div className="form__input-row">
                              <H3Text text="Expiry Date" className="form__input-row__label" />
                              <div className="form__input-row__input">
                                <DatePicker
                                  value={expiryDate}
                                  format="DD-MM-YYYY"
                                  onChange={(value) => {
                                    this.setState({ expiryDate: value });
                                  }}
                                  style={{
                                    height: '28px',
                                    padding: '1px 3px',
                                    width: '100%',
                                    marginBottom: '10px',
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                        )}
                        {isPurchaseGroupEnabled && (<div className="ant-col-md-6">
                          <div className="form__input-row">
                            <div style={{ display: 'flex' }}>
                              <H3Text
                                text="Purchase Group"
                                className="form__input-row__label"
                              />
                            </div>
                            <div className="form__input-row__input">
                              <SelectAppUser
                                hideTitle
                                containerClassName="orgInputContainer"
                                labelClassName="orgFormLabel"
                                inputClassName="orgFormInput"
                                selectedUser={purchaseGroup}
                                onChange={(value) =>
                                  this.setState({ purchaseGroup: value })
                                }
                                disabled={
                                  createPurchaseOrderLoading ||
                                  this.updatePurchaseOrderLoading ||
                                  getDocConfigPOLoading
                                }
                                isMultiple
                                maxTagCount="responsive"
                                placeholder="Select Purchase Group"
                              />
                            </div>
                          </div>
                        </div>)}
                        <div className="ant-col-md-6">
                          <div className="form__input-row ">
                            <div className="form__input-row__label">
                              Labels
                            </div>
                            <TagSelector
                              hideTitle
                              entityType="PURCHASE_ORDER"
                              selectedTags={selectedTags}
                              isMultiple
                              showSearch
                              onChange={(value) => {
                                this.setState({ selectedTags: value });
                              }}
                              placeholder="Select Tags"
                              isForm
                              containerWrapper="form__input-row__input"
                              disabled={
                                createPurchaseOrderLoading || updatePoStatusLoading || getDocConfigPOLoading
                              }
                              maxTagCount="responsive"
                            />
                          </div>
                        </div>
                        {moInfo && (
                          <div className="ant-col-md-6">
                            <div className="form__input-row ">
                              <div className="form__input-row__label">
                                Who will bear the cost of Raw Material?
                              </div>
                              <div className="form__input-row__input">
                                <PRZSelect
                                  value={selectedRmBearer}
                                  onChange={(value) => this.setState({ selectedRmBearer: value })}
                                  placeholder="Select Bearer"
                                  options={companyOptions}
                                />
                              </div>
                            </div>
                          </div>
                        )}
                        <CustomFieldV3
                          customFields={cfPurchaseOrdersDoc}
                          formSubmitted={formSubmitted}
                          customInputChange={(value, cfId) => this.customInputChange(value, cfId)}
                          wrapperClassName="ant-col-md-6"
                          containerClassName="form__input-row"
                          labelClassName="form__input-row__label"
                          inputClassName="form__input-row__input"
                          errorClassName="form__input-row__input-error"
                          disableCase={
                            createPurchaseOrderLoading || updatePoStatusLoading || getDocConfigPOLoading
                          }
                          hideTitle
                          isCarryForward={true}
                        />
                      </div>
                    </div>
                  </div>
                  <div className="form__section">
                    <div className="flex-display flex-align-c mg-bottom-5 pd-right-15">
                      <H3Text text="PART B" className="form__section-title" />
                      <div className="form__section-line" />
                    </div>
                    <div className="form__section-inputs">
                      <div className="ant-row">
                        <div className="ant-col-md-8">
                          <div className="form__input-row">
                            <H3Text
                              text={isInterTenantPo ? 'Destination Billing Address' : 'Vendor\'s Address'}
                              className="form__input-row__label"
                              required={vendorAddressMandatory}
                            />
                            <div className={`form__input-row__address__wrapper ${(formSubmitted && vendorAddressMandatory && !vendorAddress) ? 'form__input-row__address-error' : ''} ${(isInterTenantPo ? !destinationTenantId : !currentSelectedSeller) ? 'form__input-row__address-disabled' : ''}`}>
                              <div className="form__input-row__address">
                                {vendorAddress && (
                                  <div className="form__input-row__address-info">
                                    <div className="form__input-row__address-l1">
                                      {vendorAddress?.address1}
                                    </div>
                                    <div className="form__input-row__address-l2">
                                      {`${vendorAddress?.city}, ${vendorAddress?.state}, ${vendorAddress?.postal_code}, ${vendorAddress?.country}`}
                                    </div>
                                  </div>
                                )}
                                {!vendorAddress && (
                                  <H3Text
                                    text="Select address.."
                                    className="form__input-row__address-placeholder"
                                  />
                                )}
                                <div
                                  className="form__input-row__address-icon"
                                  onClick={() => {
                                    if (currentSelectedSeller || destinationTenantId) {
                                      this.setState({
                                        selectedAddressType: isInterTenantPo ? 'DESTINATION_TENANT_BILLING' : 'SELLER',
                                        showAddressDrawer: true,
                                      });
                                    }
                                  }}
                                >
                                  <EditFilled />
                                  {' '}
                                  Update
                                </div>
                              </div>
                              {formSubmitted && !vendorAddress && vendorAddressMandatory && (
                                <div className="input-error">
                                  *Please select vendor address
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="ant-col-md-8">
                          <div className="form__input-row">
                            <H3Text
                              text="Billing Address"
                              className="form__input-row__label"
                              required
                            />
                            <div className="form__input-row__address__wrapper">
                              <div className="form__input-row__address">
                                {billingAddress && (
                                  <div className="form__input-row__address-info">
                                    <div className="form__input-row__address-l1">
                                      {billingAddress?.address1}
                                    </div>
                                    <div className="form__input-row__address-l2">
                                      {`${billingAddress?.city}, ${billingAddress?.state}, ${billingAddress?.postal_code}, ${billingAddress?.country}`}
                                    </div>
                                  </div>
                                )}
                                {!billingAddress && (
                                  <H3Text
                                    text="Select address.."
                                    className="form__input-row__address-placeholder"
                                    required
                                  />
                                )}
                                <div
                                  className="form__input-row__address-icon"
                                  onClick={() => this.setState({
                                    selectedAddressType: 'TENANT_BILLING',
                                    showAddressDrawer: true,
                                  })}
                                >
                                  <EditFilled />
                                  {' '}
                                  Update
                                </div>
                              </div>
                              {formSubmitted && !billingAddress && (
                                <div className="input-error">
                                  *Please select billing address
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="ant-col-md-8">
                          <div className="form__input-row">
                            <H3Text
                              text="Delivery Address"
                              className="form__input-row__label"
                              required
                            />
                            <div className="form__input-row__address__wrapper">
                              <div className="form__input-row__address">
                                {shippingAddress && (
                                  <div className="form__input-row__address-info">
                                    <div className="form__input-row__address-l1">
                                      {shippingAddress?.address1}
                                    </div>
                                    <div className="form__input-row__address-l2">
                                      {`${shippingAddress?.city}, ${shippingAddress?.state}, ${shippingAddress?.postal_code}, ${billingAddress?.country}`}
                                    </div>
                                  </div>
                                )}
                                {!shippingAddress && (
                                  <H3Text
                                    text="Select address.."
                                    className="form__input-row__address-placeholder"
                                  />
                                )}
                                <div
                                  className="form__input-row__address-icon"
                                  onClick={() => this.setState({
                                    selectedAddressType: 'TENANT_SHIPPING',
                                    showAddressDrawer: true,
                                  })}
                                >
                                  <EditFilled />
                                  {' '}
                                  Update
                                </div>
                              </div>
                              {formSubmitted && !shippingAddress && (
                                <div className="input-error">
                                  *Please select delivery address
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="ant-col-md-24">
                          {user?.tenant_info?.integration_config?.sub_modules?.whatsapp?.is_active && (
                            <div className="form__input-row" style={{ marginTop: '10px' }}>
                              <Checkbox
                                disabled={
                                  createPurchaseOrderLoading || updatePoStatusLoading || getDocConfigPOLoading
                                }
                                checked={sendWhatsappNotification}
                                onChange={() => {
                                  this.setState({
                                    sendWhatsappNotification: !sendWhatsappNotification,
                                  });
                                }}
                              />
                              <span
                                style={{
                                  fontWeight: '500',
                                  fontSize: '12px',
                                  marginLeft: '5px',
                                }}
                              >
                                Send automatic whatsapp message when order is Issued.
                              </span>
                            </div>
                          )}
                          {isInterTenantPo && <div className="form__input-row" style={{ marginTop: '10px' }}>
                            <Checkbox
                              disabled={
                                createPurchaseOrderLoading || updatePoStatusLoading || getDocConfigPOLoading || match?.params?.poId
                              }
                              checked={createAutomaticSo}
                              onChange={() => {
                                this.setState({
                                  createAutomaticSo: !createAutomaticSo,
                                });
                              }}
                            />
                            <span
                              style={{
                                fontWeight: '500',
                                fontSize: '12px',
                                marginLeft: '5px',
                              }}
                            >
                              Create automatic Sales Order in destination business unit.
                            </span>
                          </div>}
                          <div className="form__input-row" style={{ marginTop: '10px' }}>
                            <Checkbox
                              disabled={
                                createPurchaseOrderLoading || updatePoStatusLoading || getDocConfigPOLoading
                              }
                              checked={checkedRecipients}
                              onChange={() => {
                                this.setState({
                                  checkedRecipients: !checkedRecipients,
                                });
                              }}
                            />
                            <span
                              style={{
                                fontWeight: '500',
                                fontSize: '12px',
                                marginLeft: '5px',
                              }}
                            >
                              Send automatic email when order is issued.
                            </span>
                          </div>
                        </div>
                        {checkedRecipients && (
                          <div className="ant-col-md-8">
                            <div className="form__input-row__input">
                              <PRZSelect
                                className={(formSubmitted && checkedRecipients && !toRecipients?.length) ? 'form__recipients__input-error' : ''}
                                mode="tags"
                                value={toRecipients}
                                filterOption={false}
                                maxTagCount="responsive"
                                onChange={(value) => {
                                  const recipients = [];
                                  for (let i = 0; i < value?.length; i++) {
                                    if (Helpers.validateEmail(value[i])) {
                                      recipients.push(value[i]);
                                    }
                                  }
                                  this.setState({ toRecipients: recipients });
                                }}
                                disabled={createPurchaseOrderLoading || updatePoStatusLoading || getDocConfigPOLoading}
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="form__lines-wrapper">
                <PoLines
                  title={() => (
                    <div className="form-title__wrapper">
                      <H3Text
                        text={`Purchase Order Products (${data?.filter(
                          (item) => item.product_sku_name?.length > 0,
                        )?.length
                          })`}
                        className="form__input-row__label"
                      />
                      <div className="form-title-left">
                        {(selectedPI || purchaseRequestData || (selectedPurchaseIndentIds?.length > 0)) && (<div>
                          <Dropdown menu={{
                            items: [
                              {
                                label: (
                                  <div onClick={() => {
                                    const previouslyRemovedLines = savedData?.filter((line) => !data?.some((selectedLine) => selectedLine?.key === line?.key));
                                    this.setState({
                                      data: [...data, ...previouslyRemovedLines],
                                    });
                                  }}>
                                    Reset Lines
                                  </div>
                                ),
                                key: 'reset_lines',
                                icon: <FontAwesomeIcon icon={faRotateRight} size="sm" />,
                              },
                              {
                                label: (
                                  <div onClick={() => {
                                    const updatedLines = data?.filter((line) => line?.original_qty > line?.ordered_quantity);

                                    if (!selectedPI && selectedPurchaseIndentIds.length > 0) {
                                      const tempSelectedPurchaseIndentIds = selectedPurchaseIndentIds?.filter((item) => updatedLines?.some((data) => data?.pi_id === item));
                                      const tempSelectedPurchaseIndentsData = selectedPurchaseIndentsData?.filter((item) => tempSelectedPurchaseIndentIds.includes(item.pi_id));

                                      this.setState((prevState) => ({
                                        data: updatedLines,
                                        selectedPurchaseIndentIds: tempSelectedPurchaseIndentIds,
                                        savedSelectedPurchaseIndentIds: tempSelectedPurchaseIndentIds,
                                        selectedPurchaseIndentsData: tempSelectedPurchaseIndentsData,
                                        savedData: tempSelectedPurchaseIndentIds?.length !== selectedPurchaseIndentIds?.length ? savedData?.filter((item) => tempSelectedPurchaseIndentIds.includes(item?.pi_id) || !item?.pi_id) : prevState.savedData,
                                        fileList: tempSelectedPurchaseIndentsData.map((item) => item?.attachments)?.flat(),
                                        selectedTags: [...new Set(tempSelectedPurchaseIndentsData?.map((item) => item?.tags).filter(Boolean)?.flat())],
                                      }));
                                    } else {
                                      this.setState({
                                        data: updatedLines,
                                      });
                                    }
                                  }}>
                                    Remove Fulfilled Lines
                                  </div>
                                ),
                                key: 'remove_fulfilled_lines',
                                icon: <FontAwesomeIcon icon={faTrash} size="sm" />,
                                danger: true,
                              },
                            ],
                          }} trigger={['click']} placement='topRight' arrow>
                            <div style={{ marginRight: '5px' }} className="custom-doc-columns__wrapper" onClick={e => e.preventDefault()}>
                              Actions
                            </div>
                          </Dropdown>
                        </div>)}
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            marginRight: '5px',
                          }}
                        >
                          <Checkbox
                            checked={isLineWiseDiscount}
                            onChange={() => {
                              const copyData = JSON.parse(JSON.stringify(data));
                              copyData.map((i) => {
                                i.discount = 0;
                                i.unitDiscount = 0;
                                i.showUnitDiscount = false;
                                i.lineDiscountType = 'Percent';

                                i.child_taxes = Helpers.computeTaxation(i?.quantity * i?.price, i?.taxInfo, billingAddress?.state, vendorAddress?.state)?.tax_info?.child_taxes;
                              });
                              this.setState({
                                data: copyData,
                                isLineWiseDiscount: !isLineWiseDiscount,
                                discountPercentage: null,
                                discountType: 'Percent',
                              });
                            }}
                            disabled={
                              createPurchaseOrderLoading
                              || updatePoStatusLoading || getDocConfigPOLoading
                            }
                          />
                          <div style={{ marginLeft: '5px', fontWeight: '500' }}>
                            Order Level Discount&nbsp;&nbsp;
                          </div>
                        </div>
                        <div style={{ marginLeft: '5px' }}>
                          <CustomDocumentColumns
                            visibleColumns={visibleColumns}
                            setVisibleColumns={(dt) => this.setState({ visibleColumns: dt })}
                            customColumns={cfPurchaseOrdersLine}
                            data={data}
                            updateData={(updatedData) => this.setState({ data: updatedData })}
                          />
                        </div>
                      </div>
                    </div>
                  )}
                  isMarketplaceSeller={isMarketplaceSeller}
                  handleDelete={this.handleDelete}
                  handleProductChange={this.handleProductChange}
                  handleProductChangeValue={this.handleProductChangeValue}
                  selectedSeller={currentSelectedSeller}
                  selectedSellerName={tenantSellerInfo?.seller_name}
                  selectedTenant={selectedTenant}
                  data={getDataSource(data)}
                  updateData={(updatedData) => this.setState({ data: updatedData })}
                  formSubmitted={formSubmitted}
                  isLineWiseDiscount={isLineWiseDiscount}
                  moFgLines={moFgLines}
                  jwInfo={jwInfo}
                  productType={productType}
                  purchaseOrderTypeValue={purchaseOrderTypeValue}
                  selectedCurrencyName={selectedCurrencyName}
                  billFromState={billingAddress?.state}
                  billToState={vendorAddress?.state}
                  visibleColumns={visibleColumns}
                  cfPurchaseOrdersLine={cfPurchaseOrdersLine}
                  customLineInputChange={(value, key, type, isManualControlCustomField) => this.customLineInputChange(value, key, type, isManualControlCustomField)}
                  isManualControlCF={isManualControlCF}
                  updateIsManualControlCF={(value) => this.setState({ isManualControlCF: value })}
                  isCarryForward={jwInfo || moInfo}
                  isSecondaryUomEnabled={isSecondaryUomEnabled}
                  selectedPurchaseOrder={selectedPurchaseOrder}
                  isClonePo={isClonePo}
                  handleMultiProductChange={this.handleMultiProductChange}
                  tenantDepartmentId={tenantDepartmentId}
                  sellerInfo={seller}
                  destinationTenantId={destinationTenantId}
                  loading={getPurchaseIndentsLoading}
                />
              </div>

              <div className="form__data-wrapper">
                <div className="ant-row">
                  <div className="ant-col-md-24">
                    <div className="flex-display">
                      {!moFgLines && !(purchaseRequestData || selectedPI) && (
                        <div
                          className={`new-row-button ${data?.find((item) => !(item?.product_sku_id || item?.product_sku_info?.product_sku_id)) ? 'new-row-button__disabled' : ''}`}
                          onClick={() => {
                            if (!data?.find((item) => !(item?.product_sku_id || item?.product_sku_info?.product_sku_id))) this.addNewRow();
                          }}
                        >
                          <FontAwesomeIcon icon={faPlusCircle} />
                          &nbsp;New Item
                        </div>
                      )}
                      &nbsp;
                      {(isCreatePage || isClonePo) && !(purchaseRequestData || selectedPI) && (!moFgLines && !purchaseRequestTenantId) && (
                        <BulkUpload
                          disableBatchCreate
                          disabled={!currentSelectedSeller || destinationTenantId}
                          selectedSeller={currentSelectedSeller}
                          onBulkUpload={(updatedData, importedData) => this.onBulkUpload(updatedData, importedData)}
                          adjustmentType="purchase_order"
                          customClass="new-row-button"
                        />
                      )}
                    </div>
                  </div>
                  <div className="ant-col-md-12">
                    <div className="form__data-tc">
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <label className="orgFormLabel" style={{ marginRight: 8 }}>
                          Terms and Conditions
                        </label>
                        <TnCSelector
                          entityName="PURCHASE_ORDER"
                          selectedTnC={terms}
                          updateTnC={(value) => this.handleChangeTextArea(value)}
                          disabled={createPurchaseOrderLoading || updatePoStatusLoading || getDocConfigPOLoading}
                          tenantId={selectedTenant || clonePoTenantId || tenantId}
                          updateTncId={(value) => this.setState({ tncId: value })}
                          tncId={tncId}
                        />
                      </div>
                      <RichTextEditor
                        onChange={(value) => this.handleChangeTextArea(value)}
                        disabled={createPurchaseOrderLoading || updatePoStatusLoading || getDocConfigPOLoading}
                        value={terms}
                      />
                    </div>
                    <div className="form__data-attachment">
                      <label className="orgFormLabel">Attachment(s)</label>
                      <Upload
                        action={Constants.UPLOAD_FILE}
                        listType="picture-card"
                        fileList={fileList}
                        disabled={
                          createPurchaseOrderLoading || updatePoStatusLoading || getDocConfigPOLoading
                        }
                        multiple
                        onChange={(fileListData) => {
                          this.setState({
                            fileList: fileListData?.fileList?.map((item) => ({
                              ...item,
                              url:
                                item?.response?.response?.location || item?.url,
                            })),
                          });
                        }}
                      >
                        {fileList?.length >= 20 ? null : uploadButton}
                      </Upload>
                    </div>
                  </div>
                  <div className="ant-col-md-12">
                    {!blanketPO && (
                      <div className="form-calculator__wrapper">
                        <div className="form-calculator">
                          <div className="form-calculator__field">
                            <H3Text
                              text="Sub Total"
                              className="form-calculator__field-name"
                            />
                            <H3Text
                              text={MONEY((this.getLineTotals().totalAmount || '0'), selectedCurrencyName?.currency_code)}
                              className="form-calculator__field-value"
                              hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                              popOverMessage="You don't have access to view sub total"
                            />
                          </div>
                          {!isLineWiseDiscount && (
                            <div className="form-calculator__field">
                              <H3Text
                                text="Discount"
                                className="form-calculator__field-name"
                              />
                              <H3Text
                                text={MONEY((this.getLineTotals().totalDiscount || '0'), selectedCurrencyName?.currency_code)}
                                className="form-calculator__field-value"
                                hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                                popOverMessage="You don't have access to view discount"
                              />
                            </div>
                          )}
                          {isLineWiseDiscount && (
                            <div className="form-calculator__field">
                              <H3Text
                                text="Discount"
                                className="form-calculator__field-name"
                              />
                              <div
                                className="form-calculator__field-value"
                                style={{ display: 'flex' }}
                              >
                                <div style={{ width: '112px' }}>
                                  <H3FormInput
                                    value={discountPercentage}
                                    type="number"
                                    containerClassName={`${formSubmitted
                                      && Number(discountPercentage) <= 0
                                      ? 'form-error__input'
                                      : ''
                                      }`}
                                    labelClassName="orgFormLabel"
                                    inputClassName="orgFormInput"
                                    onChange={(e) => {
                                      const copyData = JSON.parse(JSON.stringify(data));
                                      copyData.map((item) => {
                                        const totalValue = copyData?.reduce((acc, cur) => acc + (cur.quantity * cur.price), 0);
                                        const discountValue = discountType === 'Percent' ? parseFloat(e.target.value || 0) : ((item.quantity * item.price) / parseFloat(totalValue)) * parseFloat(e.target.value || 0);

                                        const taxableValue = discountType === 'Percent'
                                          ? (item?.quantity * item?.price) * (1 - discountValue / 100)
                                          : Math.max(item.quantity * item?.price - discountValue, 0);

                                        item.discount = discountValue;
                                        item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, billingAddress?.state, vendorAddress?.state)?.tax_info?.child_taxes;
                                        return data;
                                      });
                                      this.setState({
                                        data: copyData,
                                        discountPercentage: parseFloat(e.target.value || 0),
                                      });
                                    }}
                                  />
                                </div>
                                <div className="form-calculator__discount-type">
                                  <PRZSelect
                                    value={discountType}
                                    onChange={(value) => {
                                      const copyData = JSON.parse(JSON.stringify(data));
                                      copyData.map((item) => {
                                        const totalValue = copyData?.reduce((acc, cur) => acc + (cur.quantity * cur.price), 0);
                                        const discountValue = value === 'Percent' ? Number(discountPercentage) : ((item.quantity * item.price) / parseFloat(totalValue)) * Number(discountPercentage);

                                        const taxableValue = value === 'Percent'
                                          ? (item?.quantity * item?.price) * (1 - discountValue / 100)
                                          : Math.max(item.quantity * item?.price - discountValue, 0);

                                        item.discount = discountValue;
                                        item.lineDiscountType = value;
                                        item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, billingAddress?.state, vendorAddress?.state)?.tax_info?.child_taxes;
                                        return item;
                                      });
                                      this.setState({
                                        data: copyData,
                                        discountType: value,
                                      });
                                    }}
                                    // disabled
                                    options={[
                                      {
                                        key: 'Amount',
                                        value: 'Amount',
                                        label: selectedCurrencyName?.currency_symbol,
                                      },
                                      {
                                        key: 'Percent',
                                        value: 'Percent',
                                        label: '%',
                                      },
                                    ]
                                    }
                                  />
                                </div>
                              </div>
                            </div>
                          )}
                          {/* <div className="form-calculator__field">
                          <H3Text
                            text="Discount Amount"
                            className="form-calculator__field-name"
                          />
                          <H3Text
                            text={MONEY(this.getLineTotals().totalDiscount || '0')}
                            className="form-calculator__field-value"
                          />
                        </div> */}

                          <div className="form-calculator__field">
                            <div
                              className="form-calculator__field-name"
                              style={{ display: 'flex', alignItems: 'center' }}
                            >
                              <H3Text
                                text={charge1Name}
                                style={{ marginRight: '10px' }}
                              />
                              {freightTax && (
                                <div style={{
                                  color: '#2d7df7',
                                  fontWeight: '400',
                                  fontSize: '12px',
                                }}
                                >
                                  {`tax@${freightTax}%`}
                                </div>
                              )}
                            </div>
                            <div
                              className="form-calculator__field-value"
                              style={{ display: 'flex' }}
                            >
                              <div style={{ width: '112px' }}>
                                <H3FormInput
                                  value={charge1Value}
                                  type="number"
                                  containerClassName={`${formSubmitted && Number(charge1Value) < 0
                                    ? 'form-error__input'
                                    : ''
                                    }`}
                                  labelClassName="orgFormLabel"
                                  inputClassName="orgFormInput"
                                  onChange={(e) => {
                                    this.setState({
                                      charge1Value: e.target.value,
                                      freightTaxData: {
                                        ...freightTaxData,
                                        child_taxes: Helpers.computeTaxation(e.target.value, freightTaxInfo, billingAddress?.state, vendorAddress?.state)?.tax_info?.child_taxes,
                                      },
                                    });
                                  }}
                                />
                              </div>
                              {!isVendorOverseas && (<FreightTaxInput
                                freightTaxId={freightTaxId}
                                openFreightTax={openFreightTax}
                                sacCode={freightSacCode}
                                setOpenFreightTax={(value) => this.setState({ openFreightTax: value })}
                                setFreightTaxData={(value) => {
                                  this.setState({
                                    freightTaxId: !value ? 'Not Applicable' : value?.tax_id,
                                    freightTax: !value ? null : value?.tax_value,
                                    freightTaxInfo: !value ? null : value,
                                    freightTaxData: !value ? {
                                      child_taxes: [
                                        {
                                          tax_amount: 0,
                                          tax_type_name: '',
                                        },
                                      ],
                                    } : {
                                      ...value,
                                      child_taxes: Helpers.computeTaxation(charge1Value, value, billingAddress?.state, vendorAddress?.state)?.tax_info?.child_taxes,
                                    },
                                  });
                                }}
                                setSacCode={(value) => this.setState({ freightSacCode: value })}
                              />)}
                            </div>
                          </div>
                          {/* we are showing other charges based on taxes applied in above */}
                          {renderCharges(splitChargesData(chargeData)?.chargeWithTaxName)}
                          <div className="form-calculator__field">
                            <H3Text
                              text="Taxable Amount"
                              className="form-calculator__field-name"
                            />
                            <H3Text
                              text={MONEY((this.getLineTotals().totalBase || '0'), selectedCurrencyName?.currency_code)}
                              className="form-calculator__field-value"
                              hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                              popOverMessage="You don't have access to view taxable amount"
                            />
                          </div>
                          {!isVendorOverseas && (data?.[0]?.child_taxes?.[0]?.tax_type_name) && Helpers.groupAndSumByTaxName(FormHelpers.childTaxesData([...data, freightTaxData, ...chargeData?.flatMap((charge) => charge?.chargesTaxData)]))?.map((taxType, i) => (
                            <Fragment key={i}>
                              <div className="form-calculator__field">
                                <H3Text
                                  text={taxType?.tax_type_name}
                                  className="form-calculator__field-name"
                                />
                                <H3Text
                                  text={MONEY((taxType?.tax_amount || '0'), selectedCurrencyName?.currency_code)}
                                  className="form-calculator__field-value"
                                  hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                                  popOverMessage={`You don't have access to view ${taxType?.tax_type_name?.toLowerCase()} amount`}
                                />
                              </div>
                            </Fragment>
                          ))}

                          {user?.tenant_info?.global_config?.settings?.enable_tds_tcs && user?.tenant_info?.country_code === 'IN' && !isVendorOverseas && (
                            <div className="form-calculator__field">
                              <div className="form-calculator__field-name flex-display">
                                <H3Text
                                  text="TCS"
                                />
                                <SelectTaxType
                                  containerClassName="orgInputContainer"
                                  selectedTaxType={taxTypeId}
                                  disabled={
                                    createPurchaseOrderLoading || updatePoStatusLoading || getDocConfigPOLoading
                                  }
                                  onChange={(value) => {
                                    this.setState({
                                      taxTypeId: value?.tax_id,
                                      taxTypeInfo: value,
                                      taxTypeName: value?.tax_type_name,
                                      taxType: value?.tax_type,
                                    });
                                  }}
                                  taxTypeName="TCS"
                                  customStyle={{
                                    marginLeft: '10px',
                                    backgroundColor: 'white',
                                  }}
                                />
                              </div>
                              <H3Text
                                text={MONEY(this.getLineTotals().totalTcs || '0', selectedCurrencyName?.currency_code)}
                                className="form-calculator__field-value"
                                hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                                popOverMessage="You don't have access to view tcs amount"
                              />
                            </div>
                          )}
                          {/* we are showing other charges without any tax in last */}
                          {renderCharges(splitChargesData(chargeData)?.chargeWithoutTaxName)}
                          <div
                            className="new-charge-row-button"
                            onClick={() => this.addNewChargesRow()}
                          >
                            <span className="new-charge-row-button__icon">
                              <PlusCircleFilled />
                            </span>
                            <div>Add Charges</div>
                          </div>
                          {user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.round_off_method !== 'NO_ROUND_OFF' && (
                            <div className="form-calculator__field">
                              <H3Text
                                text="Round Off"
                                className="form-calculator__field-name"
                              />
                              <Tooltip
                                title={`Round Off method for Purchase Order is set to ${user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.round_off_method?.replace(/_/g, ' ')?.toProperCase()}`}
                              >
                                <div style={{ cursor: 'pointer', }}>
                                  <FontAwesomeIcon icon={faCircleInfo} size='lg' style={{ color: '#2D7DF7', }} />
                                </div>
                              </Tooltip>
                              <H3Text
                                text={`${Helpers.configuredRoundOff(this.getLineTotals()?.poTotal, user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.round_off_method)?.roundOff < 0 ? '(-) ' : ''}${MONEY(
                                  Math.abs(Helpers.configuredRoundOff(this.getLineTotals()?.poTotal, user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.round_off_method)?.roundOff),
                                  selectedCurrencyName?.currency_code
                                )}`}
                                className="form-calculator__field-value"
                                hideText={
                                  isDataMaskingPolicyEnable && isHideCostPrice
                                }
                                popOverMessage={
                                  'You don\'t have access to view sub total'
                                }
                              />
                            </div>
                          )}
                          <div className="form-calculator__field form-calculator__field-total">
                            <H3Text
                              text="Grand Total"
                              className="form-calculator__field-name"
                            />
                            <H3Text
                              text={MONEY(Helpers.configuredRoundOff(this.getLineTotals()?.poTotal || '0', user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.settings?.round_off_method)?.value, selectedCurrencyName?.currency_code)}
                              className="form-calculator__field-value"
                              hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                              popOverMessage="You don't have access to view grand total amount"
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="form__footer">
              {(!!selectedPurchaseOrder && !location?.state?.clonePoId) ? (
                <Fragment>
                  {(['DRAFT', 'CANCELLED'].includes(selectedPurchaseOrder?.status) || (isCreatePage || isClonePo)) && (
                    <PRZConfirmationPopover
                      title="Are you sure you want to update?"
                      content={(
                        <Fragment>
                          <PRZText text="Reason" required />
                          <PRZInput
                            placeholder="Enter update reason"
                            value={updateDocumentReason}
                            onChange={(e) => this.setState({ updateDocumentReason: e.target.value })}
                          />
                        </Fragment>
                      )}
                      onConfirm={() => {
                        if (!isCreatePage && !isClonePo && !updatePurchaseOrderLoading) {
                          if (
                            grnData?.grn?.filter((item) => ['ISSUED', 'DRAFT'].includes(item?.status))?.length
                          ) {
                            notification.warning({
                              message:
                                'Sorry, you are not allowed to update a purchase order for which GRN has been created.',
                              placement: 'top',
                              duration: 4,
                            });
                          } else {
                            this.updatePurchaseOrder(false);
                          }
                        }

                        if (!createPurchaseOrderLoading && (isCreatePage || isClonePo)) {
                          this.createPurchaseOrder(false);
                        }
                      }}
                      confirmButtonText="Confirm"
                      cancelButtonText="Back"
                      confirmDisabled={!updateDocumentReason}
                    >
                      <PRZButton
                        id="save-as-draft"
                        type="default"
                        wrapperStyle={{ marginRight: '10px' }}
                        buttonStyle={{
                          width: '130px',
                          height: '40px',
                          border: '1px solid #2d7df7',
                          color: '#2d7df7',
                        }}
                        isLoading={
                          (!withApproval && createPurchaseOrderLoading)
                          || updatePoStatusLoading || getDocConfigPOLoading
                          || ((updatePurchaseOrderLoading || updatePoStatusLoading || getDocConfigPOLoading)
                            && actionType === 'SAVE_AS_DRAFT')
                        }
                        disabled={
                          createPurchaseOrderLoading
                          || updatePoStatusLoading || getDocConfigPOLoading
                          || updatePurchaseOrderLoading
                          || updatePoStatusLoading || getDocConfigPOLoading || !isPurchaseOrderEnable
                        }
                      >
                        Save as Draft
                      </PRZButton>
                    </PRZConfirmationPopover>
                  )}
                  <PRZConfirmationPopover
                    title="Are you sure you want to update?"
                    content={(
                      <Fragment>
                        <PRZText text="Reason" required />
                        <PRZInput
                          placeholder="Enter update reason"
                          value={updateDocumentReason}
                          onChange={(e) => this.setState({ updateDocumentReason: e.target.value })}
                        />
                      </Fragment>
                    )}
                    onConfirm={() => {
                      if (!isCreatePage && !isClonePo && !updatePurchaseOrderLoading) {
                        if (
                          grnData?.grn?.filter((item) => ['ISSUED', 'DRAFT'].includes(item?.status))?.length
                        ) {
                          notification.warning({
                            message:
                              'Sorry, you are not allowed to update a purchase order for which GRN has been created.',
                            placement: 'top',
                            duration: 4,
                          });
                        } else this.updatePurchaseOrder(true);
                      }
                      if (!createPurchaseOrderLoading && (isCreatePage || isClonePo)) {
                        this.createPurchaseOrder(true);
                      }
                    }}
                    confirmButtonText="Confirm"
                    cancelButtonText="Back"
                    confirmDisabled={!updateDocumentReason}
                  >
                    <PRZButton
                      id="save-and-issue"
                      isLoading={
                        (withApproval && createPurchaseOrderLoading)
                        || ((updatePurchaseOrderLoading || updatePoStatusLoading || getDocConfigPOLoading)
                          && actionType === 'SAVE_AND_ISSUED')
                      }
                      disabled={
                        createPurchaseOrderLoading
                        || updatePurchaseOrderLoading
                        || updatePoStatusLoading || getDocConfigPOLoading || getDocConfigPOLoading || !isPurchaseOrderEnable
                      }
                      buttonStyle={{ width: '130px', height: '40px' }}
                    >
                      Save and Issue
                    </PRZButton>
                  </PRZConfirmationPopover>
                </Fragment>
              ) : (
                <Fragment>
                  {(['DRAFT', 'CANCELLED'].includes(selectedPurchaseOrder?.status) || (isCreatePage || isClonePo)) && (
                    <PRZButton
                      id="save-as-draft"
                      type="default"
                      onClick={() => {
                        if (!isCreatePage && !isClonePo && !updatePurchaseOrderLoading) {
                          if (
                            grnData?.grn?.filter((item) => ['ISSUED', 'DRAFT'].includes(item?.status))?.length
                          ) {
                            notification.warning({
                              message:
                                'Sorry, you are not allowed to update a purchase order for which GRN has been created.',
                              placement: 'top',
                              duration: 4,
                            });
                          } else {
                            this.updatePurchaseOrder(false);
                          }
                        }

                        if (!createPurchaseOrderLoading && (isCreatePage || isClonePo)) {
                          this.createPurchaseOrder(false);
                        }
                      }}
                      isLoading={
                        (!withApproval && createPurchaseOrderLoading)
                        || updatePoStatusLoading || getDocConfigPOLoading
                        || ((updatePurchaseOrderLoading || updatePoStatusLoading || getDocConfigPOLoading)
                          && actionType === 'SAVE_AS_DRAFT')
                      }
                      disabled={
                        createPurchaseOrderLoading
                        || updatePoStatusLoading || getDocConfigPOLoading
                        || updatePurchaseOrderLoading
                        || updatePoStatusLoading || getDocConfigPOLoading || !isPurchaseOrderEnable
                      }
                      wrapperStyle={{ marginRight: '10px' }}
                      buttonStyle={{
                        width: '130px',
                        height: '40px',
                        border: '1px solid #2d7df7',
                        color: '#2d7df7',
                      }}
                    >
                      Save as Draft
                    </PRZButton>
                  )}
                  <PRZButton
                    id="save-and-issue"
                    onClick={() => {
                      if (!isCreatePage && !isClonePo && !updatePurchaseOrderLoading) {
                        if (
                          grnData?.grn?.filter((item) => ['ISSUED', 'DRAFT'].includes(item?.status))?.length
                        ) {
                          notification.warning({
                            message:
                              'Sorry, you are not allowed to update a purchase order for which GRN has been created.',
                            placement: 'top',
                            duration: 4,
                          });
                        } else this.updatePurchaseOrder(true);
                      }
                      if (!createPurchaseOrderLoading && (isCreatePage || isClonePo)) {
                        this.createPurchaseOrder(true);
                      }
                    }}
                    isLoading={
                      (withApproval && createPurchaseOrderLoading)
                      || ((updatePurchaseOrderLoading || updatePoStatusLoading || getDocConfigPOLoading)
                        && actionType === 'SAVE_AND_ISSUED')
                    }
                    disabled={
                      createPurchaseOrderLoading
                      || updatePurchaseOrderLoading
                      || updatePoStatusLoading || getDocConfigPOLoading || getDocConfigPOLoading || !isPurchaseOrderEnable
                    }
                    buttonStyle={{ width: '130px', height: '40px' }}
                  >
                    Save and Issue
                  </PRZButton>
                </Fragment>
              )}
              {(moFgLines || (selectedPurchaseOrder?.linked_mrps?.length > 0) || selectedPurchaseOrder?.subcontractor_mo_id) && !isClonePo && !user?.tenant_info?.permission?.editable_work_order_qty && (
                <RestrictedAccessMessage message="You do not have access to edit Quantities of Products on this page" />
              )}
              {(isDataMaskingPolicyEnable && isHideCostPrice) && <RestrictedAccessMessage message="You don't have access to view & edit rate" />}
              {!isPurchaseOrderEnable && (
                <Popconfirm
                  placement="topRight"
                  title="This feature is not available in your current plan"
                  onConfirm={() => window.Intercom('showNewMessage')}
                  okText="Contact Us"
                  cancelText="Cancel"
                >
                  <img className="barcode-restrict" style={{ marginTop: '4px', marginLeft: '5px' }} src={Crown} alt="premium" />
                </Popconfirm>
              )}
            </div>
          </React.Fragment>
        )}
        <H3Modal
          isOpen={showNewVendorModal}
          title="Add New Vendor"
          onClose={() => this.setState({ showNewVendorModal: false })}
          showHeader
        >
          <div className="form__new-vendor-wrapper">
            <VendorForm
              callback={(createdSeller) => this.setState({
                seller: createdSeller,
                showNewVendorModal: false,
                currentSelectedSeller: createdSeller.seller_id,
              })}
            />
          </div>
        </H3Modal>
      </Fragment>
    );
  }
}

const mapStateToProps = ({
  UserReducers, PurchaseOrderReducers, WorkflowsReducers, TenantReducers, CFV2Reducers, GRNReducers, DocConfigReducers, CurrenciesReducers, TagReducers, TaxReducers, GetPurchaseIndents,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  createPurchaseOrderLoading: PurchaseOrderReducers.createPurchaseOrderLoading,
  purchaseWorkflows: WorkflowsReducers.purchaseWorkflows,
  updatePoStatusLoading: PurchaseOrderReducers.updatePoStatusLoading,
  tenantsConfiguration: TenantReducers.tenantsConfiguration,
  cfV2DocPurchaseOrders: CFV2Reducers.cfV2DocPurchaseOrders,
  getPurchaseOrderByIdLoading: PurchaseOrderReducers.getPurchaseOrderByIdLoading,
  updatePurchaseOrderLoading: PurchaseOrderReducers.updatePurchaseOrderLoading,
  selectedPurchaseOrder: PurchaseOrderReducers.selectedPurchaseOrder,
  grnData: GRNReducers.grnData,
  getTenantByIdLoading: TenantReducers.getTenantByIdLoading,
  selectedTenant: TenantReducers.selectedTenant,
  docConfigPO: DocConfigReducers.docConfigPO,
  getDocConfigPOLoading: DocConfigReducers.getDocConfigPOLoading,
  CurrenciesResults: CurrenciesReducers.CurrenciesResults,
  getCurrenciesLoading: CurrenciesReducers.getCurrenciesLoading,
  createTagLoading: TagReducers.createTagLoading,
  refreshCurrencyConversionRateLoading: CurrenciesReducers.refreshCurrencyConversionRateLoading,
  priceMasking: UserReducers.priceMasking,
  taxesGroup: TaxReducers.taxesGroup,
  getTaxesLoading: TaxReducers.getTaxesLoading,
  getPurchaseIndentsLoading: GetPurchaseIndents.loading,
});

const mapDispatchToProps = (dispatch) => ({
  getSellers: (keyword, tenantId, page, limit, sellerId, callback, isActive, isSubcontractor, sellerType, isMarketplaceSeller) => dispatch(
    SellerActions.getSellers(keyword, tenantId, page, limit, sellerId, callback, isActive, isSubcontractor, sellerType, isMarketplaceSeller),
  ),
  createPurchaseOrder: (payload, callback, withApproval) => dispatch(PurchaseOrderActions.createPurchaseOrder(payload, callback, withApproval)),
  getAddresses: (page, entityId, entityType, addressId) => dispatch(AddressActions.getAddresses(page, entityId, entityType, addressId)),
  getPurchaseWorkflows: () => dispatch(WorkflowActions.getPurchaseWorkflows()),
  getTenantSkuOffer: (tenantProductId, tenantSellerId, callback, tenantProductIds) => dispatch(OfferActions.getOfferByTenantSku(tenantProductId, tenantSellerId, callback, tenantProductIds)),
  getTenantsConfiguration: (tenantId, callback) => dispatch(TenantActions.getTenantsConfiguration(tenantId, callback)),
  updatePurchaseOrder: (payload, callback, withApproval) => dispatch(PurchaseOrderActions.updatePurchaseOrder(payload, callback, withApproval)),
  getTenants: (page, keyword, isVerified, orgId) => dispatch(TenantActions.getTenants(page, keyword, isVerified, orgId)),
  getTenantById: (tenantId) => dispatch(TenantActions.getTenantById(tenantId)),
  getPurchaseOrderById: (poId, tenantId, callback) => dispatch(PurchaseOrderActions.getPurchaseOrderById(poId, tenantId, callback)),
  getGRN: (tenantId, grnId, grnEntityId, grnEntityType, status, limit, page, tenantSellerId) => dispatch(GrnActions.getGRN(tenantId, grnId, grnEntityId, grnEntityType, status, limit, page, tenantSellerId)),
  updatePoStatus: (payload, callback) => dispatch(PurchaseOrderActions.updatePoStatus(payload, callback)),
  getPurchaseOrderByIdSuccess: (selectedPurchaseOrder) => dispatch(
    PurchaseOrderActions.getPurchaseOrderByIdSuccess(selectedPurchaseOrder),
  ),
  getDocConfig: (tenantId, entityName, callback) => dispatch(DocConfigActions.getDocConfig(tenantId, entityName, callback)),
  getCurrencies: (orgId) => dispatch(CurrenciesActions.getCurrencies(orgId)),
  createTag: (payload, callback) => dispatch(TagActions.createTag(payload, callback)),
  getDocCFV2: (payload, callback) => dispatch(CFV2Actions.getDocCFV2(payload, callback)),
  removeFromCartV2: (payload, callback) => dispatch(AddToCartActions.removeFromCartV2(payload, callback)),
  getCartV2: (payload, callback) => dispatch(AddToCartActions.getCartV2(payload, callback)),
  refreshCurrencyConversionRate: (payload, callback) => dispatch(CurrenciesActions.refreshCurrencyConversionRate(payload, callback)),
  getCharges: (orgId, entityName, callback) => dispatch(ExtraChargesActions.getCharges(orgId, entityName, callback)),
  getTaxes: (orgId, page, limit, isActive, isGroup) => dispatch(TaxActions.getTaxes(orgId, page, limit, isActive, isGroup)),
  getDepartments: (keyword, tenantId, page, limit, departmentId, callback, orgId, isAdmin, multipleSelectedTenantIds) => dispatch(
    DepartmentActions.getDepartments(keyword, tenantId, page, limit, departmentId, callback, orgId, isAdmin, multipleSelectedTenantIds),
  ),
  getPurchaseIndents: (payload, callback) => dispatch(GetPurchaseIndents.actions.request(payload, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(POForm));
