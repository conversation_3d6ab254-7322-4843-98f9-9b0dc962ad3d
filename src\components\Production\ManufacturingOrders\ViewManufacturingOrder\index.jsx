import React, { Component, Fragment } from 'react';
import { <PERSON>, withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import {
  <PERSON>confirm, Button, Popover, Alert, Drawer, Menu, Dropdown, Tabs, Badge, notification, Select, Radio, Space, Tooltip, Checkbox, Avatar, DatePicker,
} from 'antd';
import {
  LoadingOutlined, EditOutlined, WarningFilled, CaretDownOutlined, CaretRightOutlined, CopyOutlined,
} from '@ant-design/icons';
import { v4 as uuidv4 } from 'uuid';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCheck, faCircleCheck, faBoxArchive, faRotateLeft, faPrint, faUnlock, faLock, faCaretDown,
} from '@fortawesome/free-solid-svg-icons';
import dayjs from 'dayjs';
import H3Text from '@Uilib/h3Text';
import H3Image from '@Uilib/h3Image';
import Constants, { toISTDate } from '@Apis/constants';
import H3FormInput from '@Uilib/h3FormInput';
import MOActions from '@Actions/moActions';
import JobWorkActions from '@Actions/production/jobWorkActions';
import Helpers from '@Apis/helpers';
import closeIcon from '@Images/icons/icon-close-blue.png';
import Attachment from '@Components/Common/Attachment';
import ViewCustomFields from '@Components/Common/CustomField/ViewCustomFields';
import PurchaseRequestActions from '@Actions/purchaseRequestActions';
import OrderSummary from '@Components/Production/ManufacturingOrders/ViewManufacturingOrder/OrderSummary';
import AttachmentActions from '@Actions/attachmentActions';
import POForm from '@Components/Purchase/PurchaseOrders/POForm';
import ActivityLog from '@Components/Common/ActivityLog';
import ActivityLogActions from '@Actions/activityLogActions';
import WorkOrdersList from '@Components/Production/ManufacturingOrders/ViewManufacturingOrder/WorkOrdersList';
import InvoiceForm from '@Components/Sales/Invoice/InvoiceForm';
import RmInvoicesList from '@Components/Production/ManufacturingOrders/ViewManufacturingOrder/RmInvoicesList';
import AnalyticsActions from '@Actions/application/analyticsActions';
import Crown from '@Images/crown2.png';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import DownloadLabels from '@Components/Common/DownloadLabels';
import { getPoLinesFromJobWorks } from '@Components/Production/JobWorks/helpers';
import CFV2Actions from '@Actions/configurations/cfV2Actions';
import RawMaterialsLines from './Tables/RawMaterialsLines';
import OtherChargesLines from './Tables/OtherChargesLines';
import ProductionRoutesLines from './Tables/ProductionRoutesLines';
import ByProductsLines from './Tables/ByProductsLines';
import FinishedProductsLines from './Tables/FinishedProductsLines';
import FinishedProductsLinesV2 from './Tables/FinishedProductsLinesV2';
import ByProduct from './Drawer/ByProduct';
import UpdateRawMaterial from './Drawer/UpdateRawMaterial';
import OtherCharges from './Drawer/OtherCharges';
import ListPurchaseRequests from './Drawer/ListPurchaseRequests';
import CreatePurchaseRequestFromMo from './Drawer/CreatePurchaseRequestFromMo';
import QualityCheckMO from './QualityCheckMO';
import qualityCheckActions from '../../../../actions/quality/qualityCheckActions';
import quickUpdateActions from '../../../../actions/quickUpdate/quickUpdateActions';
import ReservationMO from './ReservationMO';
import UpdateReservation from '../../../Sales/SalesOrder/ViewSalesOrder/UpdateReservation';
import documentUINamesConstant from '../../../../apis/documentUINamesConstant';
import './style.scss';
import ViewLoadingSkull from '../../../Common/ViewLoadingSkull';
import PRZSelect from '../../../Common/UI/PRZSelect';
import PurchaseIndentForm from '../../../Purchase/PurchaseIndent/PurchaseIndentForm';

const { TabPane } = Tabs;

class ViewManufacturingOrder extends Component {
  constructor(props) {
    super(props);
    this.state = {
      currentTab: '/order-information',
      isUserReady: false,
      currentUpdate: '',
      isShowDrawerForUpdateFinishedProduct: false,
      isShowDrawerForUpdateByProduct: false,
      isShowDrawerForUpdateRawMaterial: false,
      showCreateInvoiceDrawer: false,
      costingMethod: this.props.user?.tenant_info?.production_config?.sub_modules?.manufacturing_order?.settings?.default_costing_method,
      rmViewType: 'FIRST_LEVEL',
      cancelReservation: 'yes',
      isGenerateLabelModalOpen: false,
      isLabelAvailable: false,
      showPoForm: false,
      showCreatePI: false,
      selectedRowKeys: [],
      selectedRows: [],
      jwPoPayload: false,
      visibleColumnsFG: {
        PRODUCT: {
          label: 'Product#',
          visible: true,
          disabled: true,
        },
        PRODUCTION: {
          label: 'Production',
          visible: true,
          disabled: true,
        },
        UNIT_COST: {
          label: 'Unit Cost',
          visible: true,
          disabled: true,
        },
        EXPECTED_COST: {
          label: 'Expected Cost',
          visible: true,
          disabled: true,
        },
        INCURRED_PRICE: {
          label: 'Incurred Cost',
          visible: true,
          disabled: true,
        },
      },
      visibleColumnsRM: {
        PRODUCT: {
          label: 'Product#',
          visible: true,
          disabled: true,
        },
        INCURRED_PRICE: {
          label: 'Incurred Cost',
          visible: true,
          disabled: true,
        },
        USAGE: {
          label: 'Usage',
          visible: true,
          disabled: true,
        },
      },
      documentUINames: {},
    };
  }

  componentDidMount() {
    const {
      getMOById, user, match, getPurchaseRequests, getActivityLogSuccess, getDocCFV2,
    } = this.props;
    const { costingMethod } = this.state;
    if (match?.params?.moId) {
      getMOById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.MO, Helpers.permissionTypes.READ).join(',') || user?.tenant_info?.tenant_id, match?.params?.moId, (costingMethod || user?.tenant_info?.production_config?.sub_modules?.manufacturing_order?.settings?.default_costing_method));
      getPurchaseRequests(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.MO, Helpers.permissionTypes.READ).join(',') || user?.tenant_info?.tenant_id, '', '', '', '', '', '', 1, 30, '', '', '', match?.params?.moId);
    }
    getActivityLogSuccess(null);

    // for batch custom fields in AddBatchV2
    const payload = {
      orgId: user?.tenant_info?.org_id,
      entityName: 'MANUFACTURING_ORDER,BATCH',
    };
    getDocCFV2(payload);
  }

  static getDerivedStateFromProps(props, state) {
    const {
      user, match, selectedMO, getJobWorkByMoId,
    } = props;

    const { moId } = match.params;
    if (props?.selectedMO && !state.isUserReadyForPrefillData) {
      const isListScreen = true;
      getJobWorkByMoId(user?.tenant_info?.org_id, props?.selectedMO?.tenant_id, moId, isListScreen);
      const lineCfFG = selectedMO?.mo_finished_goods?.[0]?.custom_fields;
      const lineCfRM = selectedMO?.mo_lines_first_level_exploded_view?.[0]?.mo_line_custom_fields;
      return {
        ...state,
        fileList: selectedMO?.attachments,
        isUserReadyForPrefillData: true,
        updateScheduledDate: selectedMO?.scheduled_date ? (toISTDate(selectedMO?.scheduled_date)) : null,
        updateDeliveryDate: selectedMO?.delivery_date ? (toISTDate(selectedMO?.delivery_date)) : null,
        cfFGLine: lineCfFG,
        cfRMLine: lineCfRM,
        visibleColumnsFG: CustomFieldHelpers.updateVisibleColumns(lineCfFG, state.visibleColumnsFG),
        visibleColumnsRM: CustomFieldHelpers.updateVisibleColumns(lineCfRM, state.visibleColumnsRM),
      };
    }
    if (props?.selectedAttachment && state.isGetAttachment && !state.getAttachmentByIdLoading && !state.updateAttachmentLoading) {
      return {
        ...state,
        fileList: props?.selectedAttachment,
        isGetAttachment: false,
      };
    }
    if (props?.selectedMO?.mo_finished_goods?.length > 1) {
      return {
        ...state,
        multipleMo: true,

      };
    }
    if (user?.tenant_info) {
      return {
        ...state,
        documentUINames: documentUINamesConstant(user),
      };
    }
    return state;
  }

  componentWillUnmount() {
    const { getMOByIdSuccess } = this.props;
    getMOByIdSuccess(null);
  }

  getQcData = () => {
    const { selectedMO } = this.props;
    const requiredData = [];

    const finishedGoodsAdjustments = selectedMO?.mo_finished_goods?.map((item) => item?.finished_good_adjustments)?.flat(1);
    const qualityChecks = finishedGoodsAdjustments?.map((finishedGoodsAdjustment) => finishedGoodsAdjustment?.quality_checks)?.flat(1);
    const sfgAdjustments = selectedMO?.mo_lines?.map((item) => item?.sfg_adjustments)?.flat(1);
    const sfgQualityChecks = sfgAdjustments?.map((sfgAdjustment) => sfgAdjustment?.quality_checks)?.flat(1);

    const qualityChecksArr = [];

    sfgQualityChecks?.map((qualityCheck) => {
      selectedMO?.mo_lines.map((moLine) => {
        if (qualityCheck?.entity_line_id === moLine?.mo_line_id) {
          qualityChecksArr.push({
            ...qualityCheck,
            ordered_qty: Number(moLine?.quantity) + Number(moLine?.wastage_quantity),
            tenant_product_info: moLine?.tenant_product_info,
            available_batches: moLine?.tenant_product_info?.product_batches || [],
            product_batches: moLine?.tenant_product_info?.product_batches,
            tax_info: moLine?.tax_info,
            uom_info: moLine?.uom_info,
            uom_list: moLine?.tenant_product_info?.uom_list,
          });
        }
      });
    });

    qualityChecks?.map((qualityCheck) => {
      selectedMO?.mo_finished_goods.map((moFG) => {
        if (qualityCheck?.entity_line_id === moFG?.mo_fg_id) {
          qualityChecksArr.push({
            ...qualityCheck,
            ordered_qty: moFG?.ordered_qty,
            tenant_product_info: moFG?.tenant_product_info,
            available_batches: moFG?.tenant_product_info?.product_batches || [],
            product_batches: moFG?.tenant_product_info?.product_batches,
            tax_info: moFG?.tax_info,
            uom_info: moFG?.uom_info,
            uom_list: moFG?.tenant_product_info?.uom_list,
          });
        }
      });
    });

    qualityChecksArr?.map((qualityCheck) => {
      const finishedGoodsAdjustment = finishedGoodsAdjustments?.find((item) => item?.batch_id === qualityCheck?.batch_id);
      const sfgAdjustment = sfgAdjustments?.find((item) => item?.batch_id === qualityCheck?.batch_id);
      const adjustment = finishedGoodsAdjustment || sfgAdjustment;
      if (adjustment) {
        requiredData.push({
          ...adjustment,
          ...qualityCheck,
        });
      }
    });

    return requiredData;
  };

  handleDrawerState(type, fgTenantProductId) {
    if (type === 'RAW_MATERIAL') {
      this.setState({ isShowDrawerForUpdateRawMaterial: true, fgTenantProductId });
    }
    if (type === 'BY_PRODUCT') {
      this.setState({ isShowDrawerForUpdateByProduct: true, fgTenantProductId });
    }
    if (type === 'FINISHED_PRODUCT') {
      this.setState({ isShowDrawerForUpdateFinishedProduct: true, fgTenantProductId });
    }
    if (type === 'OTHER_CHARGES') {
      this.setState({ isShowDrawerForOtherCharges: true, fgTenantProductId });
    }
    if (type === 'PRODUCTION_ROUTES') {
      this.setState({ isShowDrawerForOtherCharges: true, fgTenantProductId });
    }
  }

  handleFileChange(fileListData) {
    const { updateAttachment, selectedMO, getAttachmentById } = this.props;
    const fileList = fileListData?.fileList?.map((item) => ({
      ...item,
      url: item?.response?.response?.location || item?.url,
    }));

    const attachments = fileList?.map((attachment) => ({
      url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
    }));

    if (fileListData?.file.status === 'done') {
      const payload = {
        entity_id: selectedMO?.mo_id,
        entity_name: 'manufacturing_order',
        attachments,
      };
      updateAttachment(payload, () => {
        getAttachmentById(selectedMO?.mo_id, 'manufacturing_order', () => {
          this.setState({
            isGetAttachment: true,
          });
        });
      });
    }
    if (fileListData?.file.status === 'removed') {
      const payload = {
        entity_id: selectedMO?.mo_id,
        entity_name: 'manufacturing_order',
        attachments,
      };
      updateAttachment(payload, () => {
        getAttachmentById(selectedMO?.mo_id, 'manufacturing_order', () => {
          this.setState({
            isGetAttachment: true,
          });
        });
      });
    }

    this.setState({
      fileList: attachments.length > 0 ? attachments : [],
    });
  }

  productWithLowAvailableQuantity() {
    const { selectedMO } = this.props;
    return selectedMO?.mo_lines?.filter((item) => item?.tenant_product_info?.total_available_batches_quantity <= 0 && !item?.child_mo_lines?.length)?.length || 0;
  }

  rawMaterialsNotConsumedCompletely() {
    const { selectedMO } = this.props;
    return selectedMO?.mo_lines?.filter((item) => Math.abs(item?.issued_quantity) < (item?.quantity + (item?.wastage_quantity || 0)))?.length || 0;
  }

  issueContractWorkOrder(fgTenantProductId) {
    const { selectedMO } = this.props;
    if (['DRAFT', 'CONFIRMED', 'VOID'].includes(selectedMO?.status)) {
      notification.open({
        type: 'warning',
        message: 'Please start the production process before issuing work order to subcontractor',
        duration: 4,
        placement: 'top',
      });
    } else {
      const fgLines = fgTenantProductId ? selectedMO?.mo_finished_goods?.filter((moFg) => moFg?.tenant_product_id === fgTenantProductId) : selectedMO?.mo_finished_goods;
      const lines = [];
      for (let i = 0; i < fgLines?.length; i++) {
        lines.push({
          key: uuidv4(),
          mo_fg_id: fgLines[i]?.mo_fg_id,
          mo_id: fgLines[i]?.mo_id,
          lastUpdatedPrice: fgLines[i]?.sku_offers?.[0]?.offer_price,
          price: fgLines[i]?.sku_offers?.[0]?.offer_price,
          product_sku_info: fgLines[i]?.tenant_product_info,
          product_sku_name: fgLines[i]?.tenant_product_info?.product_sku_name,
          quantity: fgLines[i]?.quantity,
          sku: fgLines[i]?.tenant_product_info?.product_sku_id,
          taxId: fgLines[i]?.tenant_product_info?.tax_id,
          taxInfo: fgLines[i]?.tenant_product_info?.tax_info,
          child_taxes: Helpers.computeTaxation((fgLines[i].quantity * fgLines[i]?.sku_offers?.[0]?.offer_price), fgLines[i]?.tenant_product_info?.tax_info, selectedMO?.billing_address?.state, selectedMO?.seller_address?.state)?.tax_info?.child_taxes,
          tax_id: fgLines[i]?.tenant_product_info?.tax_id,
          tenant_product_id: fgLines[i]?.tenant_product_info?.tenant_product_id,
          uomId: fgLines[i]?.tenant_product_info?.uom_info?.uom_id,
          uomInfo: fgLines[i]?.tenant_product_info?.uom_info,
          uom_info: fgLines[i]?.tenant_product_info?.uom_info,
          uom_list: fgLines[i]?.tenant_product_info?.uom_list,
          vendorMoq: fgLines[i]?.sku_offers?.[0]?.moq,
          po_line_custom_fields: fgLines[i]?.custom_fields,
        });
      }
      this.setState({
        showCreatePoDrawer: true,
        poLines: lines,
        moInfo: {
          mo_id: selectedMO?.mo_id,
          vendorAddress: selectedMO?.seller_address,
          gstNumber: selectedMO?.seller_gst,
          toRecipients: selectedMO?.tenant_seller_info?.email_id_1 ? [selectedMO?.tenant_seller_info?.email_id_1] : [],
          paymentTerms: selectedMO?.tenant_seller_info?.default_payment_terms?.due_days,
          seller: selectedMO?.tenant_seller_info,
          fgTenantDepartmentId: selectedMO?.fg_tenant_department_id,
          currentSelectedSeller: selectedMO?.tenant_seller_info?.tenant_seller_id?.toString(),
          tenantSellerInfo: {
            tenant_seller_id: selectedMO?.tenant_seller_info?.tenant_seller_id,
            seller_id: selectedMO?.tenant_seller_info?.seller_id,
            seller_name: selectedMO?.tenant_seller_info?.seller_name,
            email_id: selectedMO?.tenant_seller_info?.email_id_1,
            mobile: selectedMO?.tenant_seller_info?.mobile_1,
          },
          tenantInfo: selectedMO?.tenant_info,
        },
      });
    }
  }

  issueContractRm() {
    const { selectedMO } = this.props;
    if (['DRAFT', 'CONFIRMED', 'VOID'].includes(selectedMO?.status)) {
      notification.open({
        type: 'warning',
        message: 'Please start the production process before issuing raw material to subcontractor',
        duration: 4,
        placement: 'top',
      });
    } else {
      const rm = selectedMO?.mo_lines_last_level_view;
      const lines = [];
      for (let i = 0; i < rm?.length; i++) {
        lines.push({
          key: uuidv4(),
          mo_line_id: rm[i]?.mo_line_id,
          mo_id: rm[i]?.mo_id,
          product_sku_info: rm[i]?.tenant_product_info,
          tenant_product_info: rm[i]?.tenant_product_info,
          product_sku_name: rm[i]?.tenant_product_info?.product_sku_name,
          quantity: Math.min(rm[i]?.quantity, Helpers.getValueTotalInObject(rm[i]?.tenant_product_info?.product_batches?.filter((item) => !item?.is_rejected_batch), 'available_qty')),
          unitPrice: rm[i]?.tenant_product_info?.selling_price,
          product_batches: rm[i]?.tenant_product_info?.product_batches,
          taxId: rm[i]?.tenant_product_info?.tax_id,
          tax_id: rm[i]?.tenant_product_info?.tax_id,
          taxInfo: rm[i]?.tenant_product_info?.tax_info,
          discount: 0,
          fg_tenant_product_id: rm[i]?.fg_tenant_product_id,
          tenant_product_id: rm[i]?.tenant_product_info?.tenant_product_id,
          sku: rm[i]?.tenant_product_info?.product_sku_id,
          uomId: rm[i]?.tenant_product_info?.uom_info?.uom_id,
          uomInfo: rm[i]?.tenant_product_info?.uom_info,
          uom_info: rm[i]?.tenant_product_info?.uom_info,
          uom_list: rm[i]?.tenant_product_info?.uom_list,
          expiry_days: rm[i]?.tenant_product_info?.expiry_days,
          so_quantity: Math.max(rm[i]?.total_quantity_required),
        });
      }
      this.setState({
        showCreateInvoiceDrawer: true,
        rmLines: lines,
        moInfo: {
          mo_id: selectedMO?.mo_id,
          moTenantId: selectedMO?.tenant_id,
          tenantDepartmentId: selectedMO?.tenant_department_id,
          vendorAddress: selectedMO?.seller_address,
          gstNumber: selectedMO?.seller_gst,
          paymentTerms: selectedMO?.tenant_seller_info?.default_payment_terms?.due_days,
          seller: selectedMO?.tenant_seller_info,
          currentSelectedSeller: selectedMO?.tenant_seller_info?.tenant_seller_id?.toString(),
          tenantSellerInfo: {
            tenant_seller_id: selectedMO?.tenant_seller_info?.tenant_seller_id,
            seller_id: selectedMO?.tenant_seller_info?.seller_id,
            seller_name: selectedMO?.tenant_seller_info?.seller_name,
            email_id: selectedMO?.tenant_seller_info?.email_id_1,
            mobile: selectedMO?.tenant_seller_info?.mobile_1,
          },
          fgLines: selectedMO?.mo_finished_goods,
          tenantInfo: selectedMO?.tenant_info,
        },
      });
    }
  }

  createWorkOrders = (issueType) => {
    const { selectedRows } = this.state;

    const uniqueVendors = new Set();
    const uniqueTenants = new Set();
    let hasError = false;
    selectedRows?.map((row) => {
      if (row?.seller_info?.seller_id) {
        uniqueVendors.add(row?.seller_info?.seller_id);
      }
      if (row?.tenant_info?.tenant_id) {
        uniqueTenants.add(row?.tenant_info?.tenant_id);
      }
    });

    if (uniqueVendors.size > 1) {
      notification.open({
        type: 'error',
        message: 'Please select job cards of single subcontractor only in order to create work orders in bulk',
        placement: 'top',
        duration: 4,
      });
      hasError = true;
    }

    if (uniqueTenants.size > 1) {
      notification.open({
        type: 'error',
        message: 'Please select job cards of single location in order to issue raw materials in bulk.',
        placement: 'top',
        duration: 4,
      });
    }

    if (!hasError) {
      const jwPayload = getPoLinesFromJobWorks(selectedRows, issueType);
      this.setState({ jwPoPayload: jwPayload });
      this.setState({ showPoForm: true });
    }
  }

  issueRawMaterials = (issueType) => {
    const { selectedRows } = this.state;

    const uniqueVendors = new Set();
    const uniqueTenants = new Set();
    let hasError = false;
    selectedRows?.map((row) => {
      if (row?.seller_info?.seller_id) {
        uniqueVendors.add(row?.seller_info?.seller_id);
      }
      if (row?.tenant_info?.tenant_id) {
        uniqueTenants.add(row?.tenant_info?.tenant_id);
      }
    });

    if (uniqueVendors.size > 1) {
      notification.open({
        type: 'error',
        message: 'Please select job cards of single subcontractor only in order to create work orders in bulk',
        placement: 'top',
        duration: 4,
      });
      hasError = true;
    }

    if (uniqueTenants.size > 1) {
      notification.open({
        type: 'error',
        message: 'Please select job cards of single location in order to issue raw materials in bulk.',
        placement: 'top',
        duration: 4,
      });
    }

    if (!hasError) {
      const jwPayload = getPoLinesFromJobWorks(selectedRows, issueType);
      this.setState({ jwPoPayload: jwPayload });
      this.setState({ showPoForm: true });
    }
  }

  render() {
    const {
      updateMOStatusLoading, user, history, purchaseRequests, getPurchaseRequests,
      getMOByIdLoading, getMOById, selectedMO, updateMOStatus, match, quickUpdateEntityWise,
      deleteMOLoading, deleteMO, updateAttachmentLoading, updateMrpRunLoading, updateMrpRun, getActivityLog,
      archiveMoLoading, archiveMo, moJobWork, getJobWorkByMoId, downloadDocument, getDownloadQualityChecks, isQuickView,
    } = this.props;
    const {
      currentUpdate, cancellationReason, showCreatePrDrawer, isShowDrawerForUpdateByProduct,
      isShowDrawerForUpdateRawMaterial, isShowDrawerForOtherCharges, onlyLinesWithLowQuantity, fileList, currentTab,
      toggleVerificationModal, multipleMo, fgTenantProductId, showCreatePoDrawer, poLines, moInfo, showCreateInvoiceDrawer,
      rmLines, costingMethod, cancelReservation, rmViewType, updateScheduledDate, updateDeliveryDate, showReservationDrawer, showUpdateReservationDrawer, selectedOrderLine, cfFGLine, visibleColumnsFG, cfRMLine, visibleColumnsRM, isGenerateLabelModalOpen, isLabelAvailable, selectedRows, selectedRowKeys, showPoForm, jwPoPayload, documentUINames, showCreatePI,
    } = this.state;

    const accessRestriction = user?.tenant_info?.production_config?.addons?.access_restriction?.is_active;

    const quickUpdateDisable = !(['VOID', 'COMPLETED'].includes(selectedMO?.status)) && Helpers.getPermission(Helpers.permissionEntities.MO, Helpers.permissionTypes.UPDATE, user);

    const enableLabelGeneration = user?.tenant_info?.global_config?.sub_modules?.label_generation?.is_active;
    // eslint-disable-next-line no-unused-vars

    function checkIfMoIsRestriction(LineData) {
      // Check if mo_users_info is either undefined, null, or its length is 0
      const isMoUsersInfoEmpty = !Array.isArray(selectedMO?.mo_users_info) || selectedMO.mo_users_info.length === 0;

      // Return true if accessRestriction is false, or mo_users_info is empty, or user_id is found in mo_users
      if (!accessRestriction
        || isMoUsersInfoEmpty
        || selectedMO?.mo_users?.includes(user?.user_id)) {
        return true;
      }

      // Default return if none of the conditions are met
      return false;
    }

    function convertMinutesToTime(minutes) {
      if (Number.isNaN(minutes) || minutes < 0) {
        return 'Invalid input';
      }

      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      const seconds = Math.round((minutes % 1) * 60);

      const formattedHours = hours > 0 ? (hours < 10 ? `0${hours}` : hours) : '00';
      const formattedMinutes = remainingMinutes > 0 ? (remainingMinutes < 10 ? `0${remainingMinutes}` : remainingMinutes) : '00';
      const formattedSeconds = seconds > 0 ? (seconds < 10 ? `0${seconds}` : seconds) : '00';

      return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
    }

    function checkEachFgMinimumOneRm() {
      const map = new Map();
      selectedMO?.mo_finished_goods?.forEach((fg) => {
        map.set(fg.tenant_product_id, 0);
      });

      selectedMO?.mo_lines?.forEach((rm) => {
        if (map.has(rm.fg_tenant_product_id)) {
          map.set(rm.fg_tenant_product_id, (map.get(rm.fg_tenant_product_id) || 0) + 1);
        }
      });

      return Array.from(map.values()).includes(0);
    }

    const fixedMenuBar = user?.side_menu_bar_type === 'FIXED';

    return (
      <div className={`view-document__wrapper ${!isQuickView ? 'view-document__wrapper-page' : ''}`}>
        <DownloadLabels
          documentType="manufacturing_order"
          documentId={selectedMO?.mo_id}
          isModalOpen={isGenerateLabelModalOpen}
          toggleModal={() => this.setState({ isGenerateLabelModalOpen: !isGenerateLabelModalOpen })}
          setIsLabelAvailable={(val) => this.setState({ isLabelAvailable: val })}
          selectedMO={selectedMO}
        />
        <Drawer
          onClose={() => this.setState({ isShowDrawerForUpdateByProduct: false })}
          open={isShowDrawerForUpdateByProduct}
          width="1045px"
          destroyOnClose
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header" style={{ width: '1000px' }}>
              <H3Text text="Create New Batch of By Product" className="custom-drawer__title" />
              <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ isShowDrawerForUpdateByProduct: false })} />
            </div>
          </div>
          <ByProduct
            callback={() => {
              this.setState({ isShowDrawerForUpdateByProduct: false });
              getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
              getJobWorkByMoId(user?.tenant_info?.org_id, selectedMO?.tenant_id, selectedMO?.mo_id, true);
              getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
            }}
            isFlexible={!!(selectedMO?.bom_info?.flexible_consumption === 'ALLOWED')}
            fgTenantProductId={fgTenantProductId}
          />
        </Drawer>
        <Drawer
          onClose={() => this.setState({ isShowDrawerForUpdateRawMaterial: false })}
          open={isShowDrawerForUpdateRawMaterial}
          width="1100px"
          destroyOnClose
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header" style={{ width: '1050px' }}>
              <H3Text text="Issue New Batch of Raw Materials" className="custom-drawer__title" />
              <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ isShowDrawerForUpdateRawMaterial: false })} />
            </div>
          </div>
          <UpdateRawMaterial
            selectedMO={selectedMO}
            callback={() => {
              this.setState({ isShowDrawerForUpdateRawMaterial: false });
              getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
              getJobWorkByMoId(user?.tenant_info?.org_id, selectedMO?.tenant_id, selectedMO?.mo_id, true);
              getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
            }}
            isFlexible={!!(selectedMO?.bom_info?.flexible_consumption === 'ALLOWED')}
            fgTenantProductId={fgTenantProductId}
          />
        </Drawer>
        <Drawer
          onClose={() => {
            this.setState({ showCreatePrDrawer: false }, () => {
              sessionStorage.setItem('removed_line_keys_mo_to_pr', JSON.stringify([]));
            });
          }}
          open={showCreatePrDrawer}
          width="1150px"
          destroyOnClose
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header" style={{ width: '1100px' }}>
              <H3Text text="Create New Material Request" className="custom-drawer__title" />
              <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ showCreatePrDrawer: false })} />
            </div>
          </div>
          <CreatePurchaseRequestFromMo
            selectedMO={selectedMO}
            onlyLinesWithLowQuantity={onlyLinesWithLowQuantity}
            callback={() => {
              this.setState({ showCreatePrDrawer: false, onlyLinesWithLowQuantity: false });
              getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
              getPurchaseRequests(selectedMO?.tenant_id, '', '', '', '', '', '', 1, 30, '', '', '', selectedMO?.mo_id);
              getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
              sessionStorage.setItem('removed_line_keys_mo_to_pr', JSON.stringify([]));
            }}
          />
        </Drawer>
        <Drawer
          onClose={() => {
            this.setState({ showCreatePI: false }, () => {
              sessionStorage.setItem('removed_line_keys_mo_to_pi', JSON.stringify([]));
            });
          }}
          open={showCreatePI}
          width="1150px"
          destroyOnClose
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header" style={{ width: '1100px' }}>
              <H3Text text="Create New Purchase Indent" className="custom-drawer__title" />
              <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ showCreatePI: false })} />
            </div>
          </div>
          <PurchaseIndentForm
            selectedMO={selectedMO}
            onlyLinesWithLowQuantity={onlyLinesWithLowQuantity}
            callback={() => {
              this.setState({ showCreatePI: false, onlyLinesWithLowQuantity: false });
              getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
              getPurchaseRequests(selectedMO?.tenant_id, '', '', '', '', '', '', 1, 30, '', '', '', selectedMO?.mo_id);
              getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
              sessionStorage.setItem('removed_line_keys_mo_to_pi', JSON.stringify([]));
            }}
          />
        </Drawer>
        <Drawer
          onClose={() => this.setState({ isShowDrawerForOtherCharges: false })}
          open={isShowDrawerForOtherCharges}
          width="661px"
          destroyOnClose
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header" style={{ width: '605px' }}>
              <H3Text text="Update Other Charges" className="custom-drawer__title" />
              <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ isShowDrawerForOtherCharges: false })} />
            </div>
          </div>
          <OtherCharges
            selectedMO={selectedMO}
            callback={() => {
              this.setState({ isShowDrawerForOtherCharges: false });
              getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
              getJobWorkByMoId(user?.tenant_info?.org_id, selectedMO?.tenant_id, selectedMO?.mo_id, true);
              getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
            }}
            fgTenantProductId={fgTenantProductId}
          />
        </Drawer>
        <div className="ant-row">
          <div className={`${fixedMenuBar ? 'ant-col-md-16' : 'ant-col-md-17'} ant-col-xs-24`}>
            {(!getMOByIdLoading || selectedMO)
              ? (
                <div className={`view-left__wrapper view-mo__wrapper ${!isQuickView ? 'is-page-view' : ''}`}>
                  <div className="view-mo__alert">
                    {window.screen.width > 425 && !(purchaseRequests?.purchase_requisition?.length > 0) && this.productWithLowAvailableQuantity() > 0 && this.rawMaterialsNotConsumedCompletely() > 0 && ['confirmed', 'in_progress'].includes(selectedMO?.status?.toLowerCase()) && (
                      <Alert
                        message={`You do not have sufficient stock for ${this.productWithLowAvailableQuantity()} product(s) to complete this manufacturing order. `}
                        showIcon
                        icon={<WarningFilled />}
                        type="error"
                        action={(
                          <div
                            className="view-mo__alert-action"
                            onClick={
                              () => this.setState({
                                showCreatePrDrawer: true,
                                onlyLinesWithLowQuantity: true,
                              })
                            }
                          >
                            Create Material Request
                          </div>
                        )}
                      />
                    )}
                  </div>

                  <div className="view-document__title">
                    <div className="view-document__title-number-wrapper">
                      <H3Text text={selectedMO?.mo_number} className="view-document__title-number" />
                      <div className="view-document__title-status" style={{ backgroundColor: Helpers.getStatusColor(selectedMO?.status).color }}>
                        {selectedMO?.status?.replaceAll('_', ' ')}
                      </div>
                    </div>
                    <div className="view-document__title-actions">
                      <Tooltip title={(selectedMO?.is_restricted && accessRestriction && (selectedMO?.mo_users_info?.length > 0)) ? 'This order has restricted access.' : 'This order is visible to your entire team.'}>
                        {(selectedMO?.is_restricted && (selectedMO?.mo_users_info?.length > 0)) && accessRestriction ? (
                          <div className="view-mo__lock">
                            <FontAwesomeIcon icon={faLock} style={{ fontSize: '16px', width: '35px', textAlign: 'center' }} />
                          </div>
                        ) : (
                          <div className="view-mo__unlock">
                            <FontAwesomeIcon icon={faUnlock} style={{ fontSize: '16px', width: '35px', textAlign: 'center' }} />
                          </div>
                        )}
                      </Tooltip>
                      <div className="view-mo__lock-users" style={{ marginLeft: (selectedMO?.is_restricted && accessRestriction && (selectedMO?.mo_users_info?.length > 0)) ? '-15px' : '0px' }}>
                        {selectedMO?.is_restricted && (selectedMO?.mo_users_info?.length > 0) && accessRestriction && (
                          <Avatar.Group maxCount={3}>
                            {selectedMO?.mo_users_info?.map((item) => (
                              <Tooltip title={item?.first_name}>
                                <Avatar
                                  style={{
                                    backgroundColor: Helpers.getAvatarColors(item?.first_name?.[0])?.color1,
                                  }}
                                >
                                  {item?.first_name.substring(0, 2).toUpperCase()}
                                </Avatar>
                              </Tooltip>
                            ))}
                          </Avatar.Group>
                        )}
                      </div>
                      {checkIfMoIsRestriction() && (
                        <Fragment>
                          <div className="action-buttons">
                            {Helpers.getPermission(Helpers.permissionEntities.MO, Helpers.permissionTypes.UPDATE, user) && (selectedMO?.status === 'CONFIRMED' || selectedMO?.status === 'DRAFT') && (
                              <H3Text
                                text={(
                                  <div
                                    onClick={() => {
                                      history.push(`/production/manufacturing-orders/update/${selectedMO?.mo_id}`);
                                    }}
                                  >
                                    <EditOutlined />
                                  </div>
                                )}
                                className="action-button"
                              />
                            )}
                            <div className="action-button">
                              <Dropdown
                                getPopupContainer={() => document.getElementsByClassName('view-mo__header-right')[0]}
                                overlay={(
                                  <Menu>
                                    {Helpers.getPermission(Helpers.permissionEntities.MO,
                                      Helpers.permissionTypes.COSTING, user) && (
                                        <Menu.Item>
                                          <H3Text
                                            text="Order Overview with cost"
                                            onClick={() => downloadDocument({
                                              url: `${Constants.MO_V2}/download?mo_id=${selectedMO?.mo_id}&tenant_id=${selectedMO?.tenant_id}&is_mo_line_cost_weighted_average=${costingMethod === 'FIFO'}`,
                                              document_type: 'MANUFACTURING_ORDER',
                                              document_number: selectedMO?.mo_number,
                                              key: uuidv4(),
                                            })}
                                          />
                                        </Menu.Item>
                                      )}
                                    {Helpers.getPermission(Helpers.permissionEntities.MO,
                                      Helpers.permissionTypes.COSTING, user) && (
                                        <Menu.Item>
                                          <H3Text
                                            text="Production Report with cost"
                                            onClick={() => downloadDocument({
                                              url: `${Constants.MO_V2}/download?mo_id=${selectedMO?.mo_id}&tenant_id=${selectedMO?.tenant_id}&with_production_report=${true}&is_mo_line_cost_weighted_average=${costingMethod === 'FIFO'}`,
                                              document_type: 'MANUFACTURING_ORDER',
                                              document_number: selectedMO?.mo_number,
                                              key: uuidv4(),
                                            })}
                                          />
                                        </Menu.Item>
                                      )}
                                    <Menu.Item>
                                      <H3Text
                                        text="Order Overview without cost"
                                        onClick={() => downloadDocument({
                                          url: `${Constants.MO_V2}/download_mo?mo_id=${selectedMO?.mo_id}&tenant_id=${selectedMO?.tenant_id}&is_mo_line_cost_weighted_average=${costingMethod === 'FIFO'}`,
                                          document_type: 'MANUFACTURING_ORDER',
                                          document_number: selectedMO?.mo_number,
                                          key: uuidv4(),
                                        })}
                                      />
                                    </Menu.Item>
                                    <Menu.Item>
                                      <H3Text
                                        text="Production Report without cost"
                                        onClick={() => downloadDocument({
                                          url: `${Constants.MO_V2}/download_mo?mo_id=${selectedMO?.mo_id}&tenant_id=${selectedMO?.tenant_id}&with_production_report=${true}&is_mo_line_cost_weighted_average=${costingMethod === 'FIFO'}`,
                                          document_type: 'MANUFACTURING_ORDER',
                                          document_number: selectedMO?.mo_number,
                                          key: uuidv4(),
                                        })}
                                      />
                                    </Menu.Item>
                                    <Menu.Item>
                                      <div style={{ display: `${!enableLabelGeneration ? 'flex' : 'block'}`, alignItems: 'center' }}>
                                        <H3Text
                                          text="Generate Labels"
                                          onClick={() => {
                                            if (enableLabelGeneration) {
                                              if (selectedMO?.mo_lines_first_level_exploded_view?.some((item) => item.raw_material_adjustments && item.raw_material_adjustments.length > 0 || selectedMO?.mo_finished_goods?.some((item) => item.finished_good_adjustments && item.finished_good_adjustments.length > 0)) && isLabelAvailable) {
                                                this.setState({ isGenerateLabelModalOpen: true });
                                              } else if (!(selectedMO?.mo_lines_first_level_exploded_view?.some((item) => item.raw_material_adjustments && item.raw_material_adjustments.length > 0 || selectedMO?.mo_finished_goods?.some((item) => item.finished_good_adjustments && item.finished_good_adjustments.length > 0)))) {
                                                notification.open({
                                                  type: 'error',
                                                  message: 'No Batches are currently available for printing labels',
                                                  duration: 4,
                                                  placement: 'top',
                                                });
                                              } else if (!isLabelAvailable) {
                                                notification.open({
                                                  type: 'error',
                                                  message: 'No Label format is available for Manufacturing Order to download',
                                                  duration: 4,
                                                  placement: 'top',
                                                });
                                              }
                                            }
                                          }}
                                        />
                                        {!enableLabelGeneration
                                          && (
                                            <Popconfirm
                                              placement="topRight"
                                              title="This feature is not accessible within your current plan to use this feature contact us."
                                              onConfirm={() => window.Intercom('showNewMessage')}
                                              okText="Contact Us"
                                              cancelText="Cancel"
                                            >
                                              <img
                                                className="barcode-restrict"
                                                src={Crown}
                                                alt="premium"
                                                style={{
                                                  marginLeft: '8px',
                                                }}
                                              />
                                            </Popconfirm>
                                          )}
                                      </div>
                                    </Menu.Item>
                                  </Menu>
                                )}
                                trigger={['click']}
                                placement="bottomRight"
                              >

                                <H3Text
                                  text={(
                                    <FontAwesomeIcon icon={faPrint} style={{ fontSize: '16px', width: '35px', textAlign: 'center' }} />
                                  )}
                                  className="action-button"
                                  style={{
                                    borderRight: '1px solid rgba(45, 124, 247, 0.5)',
                                  }}
                                />
                              </Dropdown>
                            </div>
                            {Helpers.getPermission(Helpers.permissionEntities.MO, Helpers.permissionTypes.CREATE, user) && (
                              <Tooltip
                                placement="topLeft"
                                title="Make a Copy"
                              >
                                <CopyOutlined
                                  className="action-button"
                                  style={{
                                    justifyContent: 'center',
                                    fontSize: '16px',
                                  }}
                                  onClick={() => {
                                    history.push({
                                      pathname: '/production/manufacturing-orders/create',
                                      state: { cloneMo: selectedMO?.mo_id },
                                    });
                                  }}
                                />
                              </Tooltip>
                            )}
                          </div>
                        </Fragment>
                      )}
                      {((Helpers.getPermission(Helpers.permissionEntities.MO, Helpers.permissionTypes.UPDATE, user) || Helpers.getPermission(Helpers.permissionEntities.MO, Helpers.permissionTypes.UPDATE, user))) && (
                        <div className="action-buttons">
                          {Helpers.getPermission(Helpers.permissionEntities.MO,
                            Helpers.permissionTypes.UPDATE, user) && selectedMO?.status === 'DRAFT' && (
                              <Popconfirm
                                placement="topRight"
                                title="Are you sure you want to confirm the Manufacturing Order?"
                                onConfirm={() => {
                                  updateMOStatus({
                                    tenant_id: selectedMO?.tenant_id,
                                    mo_id: selectedMO.mo_id,
                                    status: 'CONFIRMED',
                                  }, () => {
                                    getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
                                    getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
                                  });
                                }}
                                okText="Yes"
                                cancelText="No"
                              >
                                <H3Text
                                  text={(
                                    <div>
                                      {updateMOStatusLoading ? <LoadingOutlined /> : (
                                        <span>Confirm Order</span>
                                      )}
                                    </div>
                                  )}
                                  className="action-button action-button-big"
                                />
                              </Popconfirm>
                            )}

                          {Helpers.getPermission(Helpers.permissionEntities.MO,
                            Helpers.permissionTypes.UPDATE, user) && selectedMO?.status === 'CONFIRMED' && (
                              <Popconfirm
                                placement="topRight"
                                title="Confirm status change to In Progress?"
                                onConfirm={() => {
                                  if (!checkEachFgMinimumOneRm()) {
                                    updateMOStatus({
                                      tenant_id: selectedMO?.tenant_id,
                                      mo_id: selectedMO.mo_id,
                                      status: 'IN_PROGRESS',
                                    }, () => {
                                      getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
                                      getJobWorkByMoId(user?.tenant_info?.org_id, selectedMO?.tenant_id, match?.params?.moId, true);
                                      getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
                                    });
                                  }
                                }}
                                okText="Yes"
                                cancelText="No"
                                disabled={checkEachFgMinimumOneRm()}
                              >
                                <H3Text
                                  text={(
                                    <div>
                                      {updateMOStatusLoading ? <LoadingOutlined /> : (
                                        <span style={{ whiteSpace: 'nowrap' }}>Start Production</span>
                                      )}
                                    </div>
                                  )}
                                  className={`action-button action-button-big ${checkEachFgMinimumOneRm() ? 'start-production-disabled' : ''}`}
                                  infoMessage={checkEachFgMinimumOneRm() ? 'At least one of the FG (Finished Goods) does not have the required Raw Material.' : ''}
                                />
                              </Popconfirm>
                            )}
                          {Helpers.getPermission(Helpers.permissionEntities.MO,
                            Helpers.permissionTypes.DELETE, user) && selectedMO?.status === 'VOID' && (
                              <Popconfirm
                                placement="topRight"
                                title="Are you sure you want to delete the Manufacturing Order ?"
                                onConfirm={() => {
                                  deleteMO({
                                    tenant_id: selectedMO?.tenant_id,
                                    mo_id: selectedMO.mo_id,
                                  }, () => {
                                    history.push('/production/manufacturing-orders');
                                  });
                                }}
                                okText="Yes"
                                cancelText="No"
                              >
                                <H3Text
                                  text={(
                                    <div>
                                      {deleteMOLoading ? <LoadingOutlined /> : (
                                        <span>Delete</span>
                                      )}
                                    </div>
                                  )}
                                  className="action-button action-button-big"
                                />
                              </Popconfirm>
                            )}

                          {Helpers.getPermission(Helpers.permissionEntities.MO,
                            Helpers.permissionTypes.UPDATE, user) && ['IN_PROGRESS', 'COMPLETED']?.includes(selectedMO?.status) && !selectedMO?.is_archived && (
                              <Popconfirm
                                placement="topRight"
                                title={`Are you sure you want to ${selectedMO?.status === 'IN_PROGRESS' ? 'close' : 'open'} the Manufacturing Order?`}
                                onConfirm={() => {
                                  if (selectedMO?.status === 'IN_PROGRESS') {
                                    if (moJobWork?.job_works?.filter((item) => ['PENDING', 'DRAFT', 'IN_PROGRESS', 'PAUSED'].includes(item?.status))?.length) {
                                      const jwNumbers = moJobWork?.job_works?.filter((item) => ['PENDING', 'DRAFT', 'IN_PROGRESS', 'PAUSED'].includes(item?.status))?.map((item) => (item?.jw_number))?.join(',');
                                      notification.open({
                                        message: 'This order cannot be closed as there are open job cards linked to this order.',
                                        description: jwNumbers,
                                        duration: '4',
                                        placement: 'top',
                                        type: 'error',
                                      });
                                    } else {
                                      this.setState({ toggleVerificationModal: true });
                                    }
                                  } else {
                                    updateMOStatus({
                                      tenant_id: selectedMO?.tenant_id,
                                      mo_id: selectedMO.mo_id,
                                      status: 'IN_PROGRESS',
                                    }, () => {
                                      getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
                                      getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
                                    });
                                  }
                                }}
                                okText="Yes"
                                cancelText="No"

                              >
                                <H3Text
                                  text={(
                                    <div>
                                      {updateMOStatusLoading ? <LoadingOutlined /> : (
                                        <span style={{ whiteSpace: 'nowrap' }}>{selectedMO?.status === 'IN_PROGRESS' ? 'Close Order' : 'Open Order'}</span>
                                      )}
                                    </div>
                                  )}
                                  className="action-button action-button-big"
                                />
                              </Popconfirm>
                            )}

                          {Helpers.getPermission(Helpers.permissionEntities.MO,
                            Helpers.permissionTypes.UPDATE, user) && ['VOID', 'COMPLETED']?.includes(selectedMO?.status) && (
                              <Popconfirm
                                placement="topRight"
                                title={`Are you sure you want to ${selectedMO?.is_archived ? 'recover' : 'archive'} this Manufacturing Order?`}
                                onConfirm={() => {
                                  archiveMo({
                                    mo_id: selectedMO.mo_id,
                                    is_archived: !selectedMO?.is_archived,
                                  }, () => {
                                    getMOById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.MO, Helpers.permissionTypes.READ).join(','), selectedMO?.mo_id, costingMethod);
                                    getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
                                  });
                                }}
                                okText="Yes"
                                cancelText="No"
                              >
                                <H3Text
                                  text={(
                                    <div>
                                      {archiveMoLoading ? <LoadingOutlined /> : (
                                        <span style={{ whiteSpace: 'nowrap' }}>
                                          <FontAwesomeIcon icon={selectedMO?.is_archived ? faRotateLeft : faBoxArchive} />
                                          &nbsp;
                                          {selectedMO?.is_archived ? 'Restore' : 'Archive'}
                                        </span>
                                      )}
                                    </div>
                                  )}
                                  className="action-button action-button-big"
                                />
                              </Popconfirm>
                            )}

                          {selectedMO?.status !== 'VOID' && selectedMO?.status !== 'COMPLETED' && Helpers.getPermission(Helpers.permissionEntities.MO,
                            Helpers.permissionTypes.UPDATE, user) && (
                              <Popover
                                placement="bottom"
                                trigger="click"
                                title="Are you sure you want to cancel?"
                                content={(
                                  <React.Fragment>
                                    <H3FormInput
                                      name="field name"
                                      type="text"
                                      containerClassName="orgInputContainer"
                                      labelClassName="orgFormLabel"
                                      inputClassName="orgFormInput"
                                      placeholder=""
                                      onChange={(event) => this.setState({ cancellationReason: event.target.value })}
                                      value={cancellationReason}
                                      label="Reason for cancellation"
                                      maxlength="100"
                                      required
                                      showError={currentUpdate === 'VOID' && !cancellationReason}
                                    />
                                    <div className="view-mo__cancellation-buttons">
                                      <Button
                                        onClick={() => {
                                          this.setState({ currentUpdate: 'VOID' });
                                          if (cancellationReason) {
                                            updateMOStatus({
                                              status: 'VOID',
                                              mo_id: selectedMO.mo_id,
                                              cancellation_reason: cancellationReason,
                                              tenant_id: selectedMO?.tenant_id,
                                            }, () => {
                                              this.setState({ currentUpdate: '', cancellationPop: false });
                                              getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
                                              getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
                                            });
                                          }
                                        }}
                                        type="primary"
                                        size="small"
                                        loading={updateMOStatusLoading}
                                      >
                                        Yes
                                      </Button>
                                    </div>
                                  </React.Fragment>
                                )}
                              >
                                <H3Text
                                  text={(
                                    <div style={{ width: '50px', textAlign: 'center' }}>
                                      {(updateMOStatusLoading && currentUpdate === 'VOID')
                                        ? <LoadingOutlined /> : (
                                          <span>Cancel</span>
                                        )}
                                    </div>
                                  )}
                                  onClick={() => {
                                    this.setState({ cancellationPop: true });
                                  }}
                                  className="action-button action-button-big"
                                />
                              </Popover>
                            )}
                          {!['VOID', 'COMPLETED'].includes(selectedMO?.status) && (
                            <div className="hide__in-mobile">
                              <Dropdown
                                overlay={(
                                  <Menu>
                                    {Helpers.getPermission(Helpers.permissionEntities.PURCHASE_REQUEST,
                                      Helpers.permissionTypes.CREATE, user) && window.screen.width > 425 &&
                                      user?.tenant_info?.production_config?.sub_modules?.manufacturing_order?.settings?.enable_mr_creation_from_mo &&
                                      (
                                        <Menu.Item>
                                          <H3Text
                                            text={(
                                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                                <div>
                                                  New Material Request
                                                </div>
                                                {!user?.tenant_info?.purchase_config?.sub_modules?.purchase_request?.is_active && (
                                                  <Popconfirm
                                                    placement="topRight"
                                                    title="This feature is not accessible within your current plan to use this feature contact us."
                                                    onConfirm={() => window.Intercom('showNewMessage')}
                                                    okText="Contact Us"
                                                    cancelText="Cancel"
                                                  >
                                                    <img className="barcode-restrict" style={{ marginLeft: '4px' }} src={Crown} alt="premium" />
                                                  </Popconfirm>
                                                )}
                                              </div>
                                            )}
                                            onClick={
                                              () => this.setState({
                                                showCreatePrDrawer: true,
                                                onlyLinesWithLowQuantity: false,
                                              })
                                            }
                                            className="hide__in-mobile"
                                          />
                                        </Menu.Item>
                                      )}
                                    {Helpers.getPermission(Helpers.permissionEntities.PURCHASE_INDENT, Helpers.permissionTypes.CREATE, user) &&
                                      window.screen.width > 425 &&
                                      user?.tenant_info?.purchase_config?.sub_modules?.purchase_indent?.is_active &&
                                      user?.tenant_info?.production_config?.sub_modules?.manufacturing_order?.settings?.enable_pi_creation_from_mo &&
                                      (
                                        <Menu.Item>
                                          <H3Text
                                            text={(
                                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                                <div>
                                                  New Purchase Indent
                                                </div>
                                                {!user?.tenant_info?.purchase_config?.sub_modules?.purchase_indent?.is_active && (
                                                  <Popconfirm
                                                    placement="topRight"
                                                    title="This feature is not accessible within your current plan to use this feature contact us."
                                                    onConfirm={() => window.Intercom('showNewMessage')}
                                                    okText="Contact Us"
                                                    cancelText="Cancel"
                                                  >
                                                    <img className="barcode-restrict" style={{ marginLeft: '4px' }} src={Crown} alt="premium" />
                                                  </Popconfirm>
                                                )}
                                              </div>
                                            )}
                                            onClick={
                                              () => this.setState({
                                                showCreatePI: true,
                                                onlyLinesWithLowQuantity: false,
                                              })
                                            }
                                            className="hide__in-mobile"
                                          />
                                        </Menu.Item>
                                      )}
                                    {!['CONFIRMED'].includes(selectedMO?.status) && this.getQcData()?.filter((item) => item?.status !== 'COMPLETED')?.length === 0 && (selectedMO?.mo_quality_checks) && (
                                      <Menu.Item>
                                        <H3Text
                                          text="Download QC Report"
                                          onClick={() => {
                                            getDownloadQualityChecks(selectedMO?.tenant_id, '', 'MANUFACTURING_ORDER', selectedMO?.mo_id);
                                          }}
                                          className="hide__in-mobile"
                                        />
                                      </Menu.Item>
                                    )}

                                    {Helpers.getPermission(Helpers.permissionEntities.PURCHASE_ORDER,
                                      Helpers.permissionTypes.CREATE, user) && window.screen.width > 425 && !['DRAFT', 'CONFIRMED', 'VOID'].includes(selectedMO?.status) && (
                                        <Menu.Item>
                                          <H3Text
                                            text={(
                                              <div style={{ display: 'flex', alignItems: 'center' }}>
                                                <div>
                                                  New Work Order
                                                </div>
                                                {!user?.tenant_info?.purchase_config?.sub_modules?.purchase_order?.is_active && (
                                                  <Popconfirm
                                                    placement="topRight"
                                                    title="This feature is not accessible within your current plan to use this feature contact us."
                                                    onConfirm={() => window.Intercom('showNewMessage')}
                                                    okText="Contact Us"
                                                    cancelText="Cancel"
                                                  >
                                                    <img className="barcode-restrict" style={{ marginLeft: '4px' }} src={Crown} alt="premium" />
                                                  </Popconfirm>
                                                )}
                                              </div>
                                            )}
                                            onClick={() => this.issueContractWorkOrder()}
                                            className="hide__in-mobile"
                                          />
                                        </Menu.Item>
                                      )}
                                    {Helpers.getPermission(Helpers.permissionEntities.INVOICE,
                                      Helpers.permissionTypes.CREATE, user) && window.screen.width > 425 && !['DRAFT', 'CONFIRMED', 'VOID'].includes(selectedMO?.status) && selectedMO?.subcontractor_pos?.length > 0 && (
                                        <Menu.Item>
                                          <H3Text
                                            text="Issue Raw Material"
                                            onClick={() => {
                                              if (!!selectedMO?.subcontractor_pos && selectedMO?.subcontractor_pos?.length > 0) {
                                                this.issueContractRm();
                                              } else {
                                                notification.open({
                                                  message: 'You need to create atleast one work order to issue Raw Material',
                                                  duration: 4,
                                                  placement: 'top',
                                                  type: 'warning',
                                                });
                                              }
                                            }}
                                            className="hide__in-mobile"
                                          />
                                        </Menu.Item>
                                      )}
                                    <Menu.Item>
                                      <H3Text
                                        text={(
                                          <div style={{ display: 'flex', alignItems: 'center' }}>
                                            <div>
                                              Reserve Stock
                                            </div>
                                          </div>
                                        )}
                                        onClick={
                                          () => this.setState({
                                            showReservationDrawer: true,
                                          })
                                        }
                                        className="hide__in-mobile"
                                      />
                                    </Menu.Item>
                                  </Menu>
                                )}
                                trigger={['click']}
                                placement="bottomRight"
                              >

                                <div style={{ width: '75px' }}>
                                  <H3Text
                                    text={(
                                      <div
                                        style={{ display: 'flex', alignItems: 'center' }}
                                        onClick={(e) => e.preventDefault()}
                                      >
                                        More
                                        <CaretDownOutlined />
                                      </div>
                                    )}
                                    className="action-button action-button-big"
                                    style={{
                                      borderRight: '1px solid rgba(45, 124, 247, 0.5)',
                                    }}
                                  />
                                </div>
                              </Dropdown>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="document-header">
                    <div className="ant-row">
                      <div className="ant-col-md-12">
                        <div className="document-header__field document-header__date">
                          <H3Text text="Scheduled Date" className="document-header__field-name" />
                          {quickUpdateDisable ? (
                            <div className="document-header__field-input">
                              <DatePicker
                                value={updateScheduledDate}
                                onChange={(value) => {
                                  this.setState({ updateScheduledDate: value });
                                  quickUpdateEntityWise({
                                    scheduled_date: dayjs(value).format('YYYY/MM/DD'),
                                    mo_id: selectedMO.mo_id,
                                    entity_name: 'MANUFACTURING_ORDER',
                                  }, () => {
                                    getMOById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.MO, Helpers.permissionTypes.READ).join(','), selectedMO?.mo_id, costingMethod),
                                      getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
                                  },
                                    () => getMOById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.MO, Helpers.permissionTypes.READ).join(','), selectedMO?.mo_id, costingMethod));
                                }}
                                format="DD-MMM-YYYY"
                                allowClear={false}
                              />
                            </div>
                          ) : (
                            <H3Text text={selectedMO?.scheduled_date ? (toISTDate(selectedMO?.scheduled_date).format('DD/MM/YYYY')) : '-'} className="document-header__field-value" />
                          )}
                        </div>
                      </div>
                      <div className="ant-col-md-12">
                        <div className="document-header__field document-header__date">
                          <H3Text text="Delivery Date" className="document-header__field-name" />
                          {quickUpdateDisable ? (
                            <div className="document-header__field-input">
                              <DatePicker
                                value={updateDeliveryDate}
                                onChange={(value) => {
                                  this.setState({ updateDeliveryDate: value });
                                  quickUpdateEntityWise({
                                    delivery_date: dayjs(value).format('YYYY/MM/DD'),
                                    mo_id: selectedMO.mo_id,
                                    entity_name: 'MANUFACTURING_ORDER',
                                  }, () => {
                                    getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
                                    this.setState({ isUserReadyForPrefillData: true });
                                    // }, () => {
                                    //   this.setState({ isUserReadyForPrefillData: false });
                                  });
                                }}
                                format="DD-MMM-YYYY"
                                allowClear={false}
                              />
                            </div>
                          ) : (
                            <H3Text text={selectedMO?.delivery_date ? (toISTDate(selectedMO?.delivery_date).format('DD/MM/YYYY')) : '-'} className="document-header__field-value" />
                          )}
                        </div>
                      </div>
                      <div className="ant-col-md-12">
                        <Tooltip title={selectedMO?.tenant_info?.tenant_name}>
                          <div className="document-header__field">
                            <H3Text text="Location" className="document-header__field-name" />
                            <H3Text text={selectedMO?.tenant_info?.tenant_name?.length > 25 ? `${selectedMO?.tenant_info?.tenant_name?.substring(0, 25)}..` : selectedMO?.tenant_info?.tenant_name} className="document-header__field-value" />
                          </div>
                        </Tooltip>
                      </div>
                      <div className="ant-col-md-12">
                        <div className="document-header__field">
                          <H3Text text="Department" className="document-header__field-name" />
                          <H3Text text={selectedMO?.tenant_department_info?.department_name} className="document-header__field-value" />
                        </div>
                      </div>
                      <div className="ant-col-md-12">
                        <div className="document-header__field">
                          <H3Text text="FG Department" className="document-header__field-name" />
                          <H3Text text={selectedMO?.fg_tenant_department_info?.department_name} className="document-header__field-value" />
                        </div>
                      </div>
                      <div className="ant-col-md-12">
                        <div className="document-header__field">
                          <H3Text text="RM Department" className="document-header__field-name" />
                          <H3Text text={selectedMO?.rm_tenant_department_info?.department_name} className="document-header__field-value" />
                        </div>
                      </div>
                      <div className="ant-col-md-12">
                        <div className="document-header__field">
                          <H3Text text="BP Department" className="document-header__field-name" />
                          <H3Text text={selectedMO?.bp_tenant_department_info?.department_name} className="document-header__field-value" />
                        </div>
                      </div>
                      <div className="ant-col-md-12">
                        <div className="document-header__field">
                          <H3Text text="Overage Tracking" className="document-header__field-name" />
                          <H3Text
                            text={selectedMO?.enable_wastage_tracking ? 'Enabled' : 'Disabled'}
                            className="document-header__field-value"
                          />
                        </div>
                      </div>
                      <div className="ant-col-md-12">
                        <div className="document-header__field">
                          <H3Text text="Costing Method" className="document-header__field-name" />
                          <H3Text
                            text={(
                              <div className="document-header__field-input">
                                <PRZSelect
                                  labelInValue
                                  value={costingMethod}
                                  onChange={(val) => {
                                    this.setState({ costingMethod: val?.value });
                                    getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, val?.value);
                                  }}
                                  bordered
                                  options={[
                                    { value: 'cost_price', label: 'Cost Price' },
                                    { value: 'FIFO', label: 'FIFO' },
                                  ]}
                                  disabled={!Helpers.getPermission(Helpers.permissionEntities.MO,
                                    Helpers.permissionTypes.COSTING, user)}
                                />
                              </div>
                            )}
                            className="document-header__field-value"
                          />
                        </div>
                      </div>
                      <div className="ant-col-md-12">
                        <div className="document-header__field">
                          <H3Text text="View Type" className="document-header__field-name" />
                          <H3Text
                            text={(
                              <div className="document-header__field-input" style={{ display: 'flex', alignItems: 'center' }}>
                                <PRZSelect
                                  labelInValue
                                  defaultValue="FIRST_LEVEL"
                                  onChange={(val) => {
                                    this.setState({ rmViewType: val?.value });
                                  }}
                                  bordered
                                  options={[
                                    { value: 'FIRST_LEVEL', label: 'First Level' },
                                    { value: 'LAST_LEVEL', label: 'Last Level' },
                                  ]}
                                  disabled={!accessRestriction}
                                />
                                {!accessRestriction && (
                                  <Popconfirm
                                    placement="topRight"
                                    title="This feature is not accessible within your current plan to use this feature contact us."
                                    onConfirm={() => window.Intercom('showNewMessage')}
                                    okText="Contact Us"
                                    cancelText="Cancel"
                                  >
                                    <img className="barcode-restrict" style={{ marginLeft: '5px' }} src={Crown} alt="premium" />
                                  </Popconfirm>
                                )}
                              </div>
                            )}
                            className="document-header__field-value"
                          />
                        </div>
                      </div>
                      <div className="ant-col-md-12">
                        <div className="document-header__field">
                          <H3Text text="Created By" className="document-header__field-name" />
                          <H3Text
                            text={selectedMO?.created_at ? `${selectedMO?.created_by_info?.first_name} ${selectedMO?.created_by_info?.last_name} (${toISTDate(selectedMO?.created_at).format('DD/MM/YYYY')})` : '-'}
                            className="document-header__field-value"
                          />
                        </div>
                      </div>
                      <div className="ant-col-md-12">
                        <div className="document-header__field">
                          <H3Text text="SFG Tracking" className="document-header__field-name" />
                          <H3Text
                            text={selectedMO?.is_issue_sfg_enabled ? 'Enabled' : 'Disabled'}
                            className="document-header__field-value"
                          />
                        </div>
                      </div>
                      {selectedMO?.lead_time_in_min ? (
                        <div className="ant-col-md-12">
                          <div className="document-header__field">
                            <H3Text text="Est. Lead Time" className="document-header__field-name" />
                            <H3Text text={convertMinutesToTime(selectedMO?.lead_time_in_min)} className="document-header__field-value" />
                          </div>
                        </div>
                      ) : ''}
                      {selectedMO?.remarks && (
                        <div className="ant-col-md-12">
                          <div className="document-header__field">
                            <H3Text text="Remarks" className="document-header__field-name" />
                            <H3Text
                              text={selectedMO?.remarks}
                              className="document-header__field-value"
                            />
                          </div>
                        </div>
                      )}
                      {selectedMO?.custom_fields?.length > 0 ? (
                        <ViewCustomFields
                          customFields={selectedMO?.custom_fields?.filter((cf) => cf.is_active)}
                          wrapperClass="document-header__field"
                          labelClass="document-header__field-name"
                          valueClass="document-header__field-value"
                          isHorizontal
                        />
                      ) : ''}
                    </div>
                  </div>
                  {selectedMO?.tenant_seller_id && (
                    <div className="mo-contractor__wrapper">
                      <H3Text
                        text="This is a contract manufacturing order. Please complete the below steps."
                        className="mo-contractor__title"
                      />
                      <div className="mo-contractor__details-wrapper">
                        <div className="mo-contractor__timeline">
                          <div className="mo-contractor__timeline-step__wrapper">
                            <div
                              className={Number(selectedMO?.pos_issued_percentage || '0') > 0 ? 'mo-contractor__timeline-step mo-contractor__timeline-step-completed' : 'mo-contractor__timeline-step'}
                            >
                              {Number(selectedMO?.pos_issued_percentage || '0') > 0
                                ? <FontAwesomeIcon icon={faCheck} /> : '1'}
                            </div>
                            <H3Text text="Send Work Order" className="mo-contractor__timeline-step__text" />
                            {Number(selectedMO?.pos_issued_percentage || '0') === 0 && ['IN_PROGRESS'].includes(selectedMO.status) && (
                              <H3Text
                                text="click here"
                                className="mo-contractor__timeline-step__text-action"
                                onClick={() => this.issueContractWorkOrder()}
                              />
                            )}
                            {Number(selectedMO?.pos_issued_percentage || '0') !== 0 && <H3Text text={`${Number(selectedMO?.pos_issued_percentage || '0')}% Sent`} className="mo-contractor__timeline-step__text-success" />}
                          </div>
                          <H3Text className="mo-contractor__timeline-step-line" />
                          <div className="mo-contractor__timeline-step__wrapper">
                            <div
                              className={Math.abs(Number(selectedMO?.invoices_issued_percentage || '0')) > 0 ? 'mo-contractor__timeline-step mo-contractor__timeline-step-completed' : 'mo-contractor__timeline-step'}
                            >
                              {Number(selectedMO?.invoices_issued_percentage || '0') > 0
                                ? <FontAwesomeIcon icon={faCheck} /> : '2'}
                            </div>
                            <H3Text text="Issue Raw Material" className="mo-contractor__timeline-step__text" />
                            {Number(selectedMO?.invoices_issued_percentage || '0') === 0 && ['IN_PROGRESS'].includes(selectedMO.status) && (
                              <H3Text
                                text="click here"
                                className="mo-contractor__timeline-step__text-action"
                                onClick={() => {
                                  if (!!selectedMO?.subcontractor_pos && selectedMO?.subcontractor_pos?.length > 0) {
                                    this.issueContractRm();
                                  } else {
                                    notification.open({
                                      message: 'You need to create atleast one work order to issue Raw Material',
                                      duration: 4,
                                      placement: 'top',
                                      type: 'warning',
                                    });
                                  }
                                }}
                              />
                            )}
                            {Number(selectedMO?.invoices_issued_percentage || '0') !== 0 && <H3Text text={`${Math.abs(Number(selectedMO?.invoices_issued_percentage || '0'))}% Issued`} className="mo-contractor__timeline-step__text-success" />}
                          </div>
                          <H3Text className="mo-contractor__timeline-step-line" />
                          <div className="mo-contractor__timeline-step__wrapper">
                            <div
                              className={Number(selectedMO?.grns_issued_percentage || '0') > 0 ? 'mo-contractor__timeline-step mo-contractor__timeline-step-completed' : 'mo-contractor__timeline-step'}
                            >
                              {Number(selectedMO?.grns_issued_percentage || '0') > 0
                                ? <FontAwesomeIcon icon={faCheck} /> : '3'}
                            </div>
                            <H3Text text="Receive Goods" className="mo-contractor__timeline-step__text" />
                            {Number(selectedMO?.grns_issued_percentage || '0') > 0 && <H3Text text={`${Number(selectedMO?.grns_issued_percentage || '0')}% Received`} className="mo-contractor__timeline-step__text-success" />}
                          </div>
                        </div>
                        <div style={{ width: '400px' }}>
                          <div className="mo-contractor__details">
                            <div className="mo-contractor__info-icon">
                              {selectedMO?.seller_name?.trim()?.split(' ')?.length > 1 ? `${selectedMO?.seller_name?.trim()?.split(' ')?.[0]?.[0]?.toUpperCase()}${selectedMO?.seller_name?.trim()?.split(' ')?.[1]?.[0]?.toUpperCase()}` : selectedMO?.seller_name?.trim()?.substring(0, 2).toUpperCase()}
                            </div>
                            <div className="mo-contractor__info">
                              <Link to={`/vendors/view/${selectedMO?.tenant_seller_info?.seller_id}`} target="_blank">
                                <H3Text text={selectedMO?.seller_name} className="mo-contractor__info-name" />
                              </Link>
                              {selectedMO?.seller_address && (
                                <H3Text
                                  text={`${selectedMO?.seller_address?.city}, ${selectedMO?.seller_address?.state}`}
                                  className="mo-contractor__info-address"
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  <br />
                  <div className="view-mo-tab__wrapper">
                    <Tabs
                      activeKey={currentTab}
                      onChange={(key) => {
                        this.setState({ currentTab: key });
                        history.push(`?tab=${key}`);
                      }}
                      mode="horizontal"
                      type="card"
                    >
                      <TabPane tab="Order Information" key="/order-information" />
                      {selectedMO?.is_issue_sfg_enabled && (<TabPane tab="Production Steps" key="/production-steps" />)}
                      {selectedMO?.subcontractor_pos?.length > 0 && (
                        <TabPane
                          tab={(
                            <div>
                              Work Orders &nbsp;
                              <Badge count={selectedMO?.subcontractor_pos?.length} />
                            </div>
                          )}
                          key="/work-orders"
                        />
                      )}
                      {selectedMO?.subcontractor_invoices?.length > 0 && (
                        <TabPane
                          tab={(
                            <div>
                              Raw Material Invoices &nbsp;
                              <Badge count={selectedMO?.subcontractor_invoices?.length} />
                            </div>
                          )}
                          key="/raw-material-invoices"
                        />
                      )}
                      {this.getQcData()?.length > 0 && Helpers.getPermission(Helpers.permissionEntities.QUALITY_CHECKS, Helpers.permissionTypes.READ, user) && (
                        <TabPane
                          tab={(
                            <div>
                              Quality Checks &nbsp;
                              {this.getQcData()?.filter((item) => item?.status !== 'COMPLETED')?.length ? <Badge count={this.getQcData()?.filter((item) => item?.status !== 'COMPLETED')?.length} /> : ''}
                            </div>
                          )}
                          key="/quality-checks"
                        />
                      )}
                      {user?.tenant_info?.production_config?.addons?.factory_setup?.is_active && moJobWork?.count > 0 && (
                        <TabPane
                          tab={(
                            <div>
                              Job Cards &nbsp;
                              {!moJobWork?.job_works?.filter((item) => ['PENDING', 'DRAFT', 'IN_PROGRESS', 'PAUSED'].includes(item?.status))?.length ? <FontAwesomeIcon icon={faCircleCheck} />
                                : <Badge count={moJobWork?.job_works?.filter((item) => ['PENDING', 'DRAFT', 'IN_PROGRESS', 'PAUSED'].includes(item?.status))?.length} />}
                            </div>
                          )}
                          key="/mo-job-works"
                        />
                      )}
                    </Tabs>

                    {!!selectedRows?.length && (
                      <Dropdown
                        overlay={(
                          <Menu>
                            <Menu.Item>
                              <div onClick={() => this.createWorkOrders('INPUT')}>
                                + Work Order
                              </div>
                            </Menu.Item>
                            <Menu.Item>
                              <div onClick={() => this.issueRawMaterials('INPUT')}>
                                + Issue Raw Materials
                              </div>
                            </Menu.Item>
                            <Menu.Item>
                              <div onClick={() => this.createWorkOrders('PROCESSING')}>
                                + Processing Work Order
                              </div>
                            </Menu.Item>
                            <Menu.Item>
                              <div onClick={() => this.issueRawMaterials('PROCESSING')}>
                                + Issue Processing Materials
                              </div>
                            </Menu.Item>
                          </Menu>
                        )}
                        placement="bottomLeft"
                        trigger={['click']}
                      // getPopupContainer={(triggerNode) => triggerNode.parentNode}
                      >
                        <div className="action-buttons">
                          <div className="action-button" style={{ padding: '0px 7px', fontWeight: '500', color: 'black' }}>
                            Actions
                            &nbsp;
                            <FontAwesomeIcon icon={faCaretDown} />
                          </div>
                        </div>
                      </Dropdown>

                    )}
                  </div>
                  {currentTab === '/order-information' && (
                    <div className="mo-tab-section">
                      <FinishedProductsLines
                        handleDrawerState={(tpId) => {
                          this.handleDrawerState('FINISHED_PRODUCT', tpId);
                        }}
                        issueContractWorkOrder={(tpId) => this.issueContractWorkOrder(tpId)}
                        selectedMO={selectedMO}
                        multipleMo={!!multipleMo}
                        callback={() => {
                          getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
                          getJobWorkByMoId(user?.tenant_info?.org_id, selectedMO?.tenant_id, selectedMO?.mo_id, true);
                          getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
                        }}
                        isIssueSfgEnabled={selectedMO?.is_issue_sfg_enabled}
                        cfFGLine={cfFGLine}
                        visibleColumns={visibleColumnsFG}
                      />
                      <hr className="view-mo__line" />
                      <RawMaterialsLines
                        handleDrawerState={(tpId) => {
                          this.handleDrawerState('RAW_MATERIAL', tpId);
                        }}
                        costingMethod={costingMethod}
                        rmViewType={rmViewType}
                        isIssueSfgEnabled={selectedMO?.is_issue_sfg_enabled}
                        callback={() => {
                          getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
                          getJobWorkByMoId(user?.tenant_info?.org_id, selectedMO?.tenant_id, selectedMO?.mo_id, true);
                          getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
                        }}
                        selectedMO={selectedMO}
                        selectedOrderLine={(lineData) => this.setState({ selectedOrderLine: lineData })}
                        setUpdateReserveDrawerOpen={() => this.setState({ showUpdateReservationDrawer: true })}
                        cfRMLine={cfRMLine}
                        visibleColumns={visibleColumnsRM}
                      />
                      <hr className="view-mo__line" />
                      {/* {selectedMO?.by_product_lines?.length ? ( */}
                      <div className="ant-col-md-24 ant-col-xs-24">
                        <ByProductsLines
                          data={selectedMO?.by_product_lines || []}
                          selectedMO={selectedMO}
                          handleDrawerState={(tpId) => {
                            this.handleDrawerState('BY_PRODUCT', tpId);
                          }}
                          callback={() => {
                            getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
                            getJobWorkByMoId(user?.tenant_info?.org_id, selectedMO?.tenant_id, selectedMO?.mo_id, true);
                            getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
                          }}
                        />
                        <hr className="view-mo__line" />
                      </div>
                      {/* ) : ''} */}
                      {selectedMO?.extra_charges?.length ? (
                        <OtherChargesLines
                          data={selectedMO?.extra_charges || []}
                          selectedMO={selectedMO}
                          handleDrawerState={(tpId) => {
                            this.handleDrawerState('OTHER_CHARGES', tpId);
                          }}
                        />
                      ) : ''}
                      <div className="view__mo-footer">
                        <div className="view__mo-attachment">
                          <Attachment
                            fileList={fileList}
                            disableCase={getMOByIdLoading || updateAttachmentLoading || updateMrpRunLoading}
                            labelClassName="orgFormLabel"
                            inputClassName=""
                            // containerClassName="text-area__po-container"
                            handleFileChange={(value) => this.handleFileChange(value)}
                            updateEnable={Helpers.getPermission(Helpers.permissionEntities.MO, Helpers.permissionTypes.UPDATE, user)}
                          />
                        </div>
                      </div>
                    </div>
                  )}
                  {currentTab === '/production-steps' && (
                    <div className="mo-tab-section">
                      <FinishedProductsLinesV2
                        handleDrawerState={(tpId) => {
                          this.handleDrawerState('FINISHED_PRODUCT', tpId);
                        }}
                        selectedMO={selectedMO}
                        multipleMo={!!multipleMo}
                        callback={() => {
                          getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
                          getJobWorkByMoId(user?.tenant_info?.org_id, selectedMO?.tenant_id, selectedMO?.mo_id, true);
                          getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
                        }}
                      />
                    </div>
                  )}
                  {currentTab === '/work-orders' && (
                    <div className="mo-tab-section">
                      <WorkOrdersList
                        purchaseOrders={selectedMO?.subcontractor_pos}
                        moId={selectedMO?.mo_id}
                        callback={() => {
                          getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
                          getJobWorkByMoId(user?.tenant_info?.org_id, selectedMO?.tenant_id, selectedMO?.mo_id, true);
                          getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
                        }}
                        selectedMO={selectedMO}
                      />
                    </div>
                  )}
                  {currentTab === '/raw-material-invoices' && (
                    <div className="mo-tab-section">
                      <RmInvoicesList
                        invoices={selectedMO?.subcontractor_invoices}
                        selectedMO={selectedMO}
                        callback={() => {
                          getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
                          getJobWorkByMoId(user?.tenant_info?.org_id, selectedMO?.tenant_id, selectedMO?.mo_id, true);
                          getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
                        }}
                      />
                    </div>
                  )}
                  {currentTab === '/quality-checks' && (
                    <div className="mo-tab-section">
                      <QualityCheckMO
                        callback={() => {
                          getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
                          getJobWorkByMoId(user?.tenant_info?.org_id, selectedMO?.tenant_id, selectedMO?.mo_id, true);
                          getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
                        }}
                        selectedMOFinishedGoods={this.getQcData()}
                      />
                    </div>
                  )}
                  {currentTab === '/mo-job-works' && (
                    <div className="mo-tab-section">
                      <ProductionRoutesLines
                        selectedMO={selectedMO}
                        handleDrawerState={(tpId) => {
                          this.handleDrawerState('PRODUCTION_ROUTES', tpId);
                        }}
                        callback={() => {
                          getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
                          getJobWorkByMoId(user?.tenant_info?.org_id, selectedMO?.tenant_id, selectedMO?.mo_id, true);
                          getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
                        }}
                        selectedRows={selectedRows}
                        selectedRowKeys={selectedRowKeys}
                        setSelectedRows={(val) => { this.setState({ selectedRows: val }); }}
                        setSelectedRowKeys={(val) => { this.setState({ selectedRowKeys: val }); }}
                      />
                    </div>
                  )}
                </div>
              )
              : (
                //  Loader
                <ViewLoadingSkull isQuickView={isQuickView} fixedMenuBar={fixedMenuBar} />
              )}
          </div>
          <div className={`${fixedMenuBar ? 'ant-col-md-8' : 'ant-col-md-7'} ant-col-xs-24`}>
            <div className={`view-right__wrapper ${!isQuickView ? 'is-page-view' : ''}`}>
              <ListPurchaseRequests
                selectedMO={selectedMO}
                callback={() => {
                  this.setState({ showPRList: false });
                  getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
                  getJobWorkByMoId(user?.tenant_info?.org_id, selectedMO?.tenant_id, selectedMO?.mo_id, true);
                  getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
                }}
              />
              {selectedMO?.linked_pis?.length && Helpers.getPermission(Helpers.permissionEntities.PURCHASE_INDENT, Helpers.permissionTypes.READ, user) && (
                <div className="linked-doc__wrapper">
                  <H3Text text='PURCHASE INDENTS' className="linked-doc__title" />
                  {selectedMO?.linked_pis?.map((item) => (
                    <div className="linked-doc__item-header">
                      <CaretRightOutlined />
                      <div className="linked-doc__item-header-id-status__wrapper">
                        <Link to={`/purchase/purchase-indent/view/${item?.pi_id}`} target="_blank">
                          <H3Text text={`#${item?.pi_number}`} className="linked-doc__item-header-id" />
                          <div
                            className="linked-doc__item-header-status"
                            style={{ backgroundColor: Helpers.getStatusColor(item?.status)?.color }}
                          >
                            {Helpers.getStatusColor(item?.status)?.text}
                          </div>
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              {selectedMO?.linked_sales_orders?.length && (
                <div className="linked-doc__wrapper">
                  <H3Text text={`${documentUINames?.salesOrderUIName?.toUpperCase() || 'SALES ORDER'}S`} className="linked-doc__title" />
                  {selectedMO?.linked_sales_orders?.map((item) => (
                    <div className="linked-doc__item-header">
                      <CaretRightOutlined />
                      <div className="linked-doc__item-header-id-status__wrapper">
                        <Link to={`/sales/sales-order/view/${item?.order_id}`} target="_blank">
                          <H3Text text={`#${item?.sales_order_number}`} className="linked-doc__item-header-id" />
                          <div
                            className="linked-doc__item-header-status"
                            style={{ backgroundColor: Helpers.getStatusColor(item?.status)?.color }}
                          >
                            {Helpers.getStatusColor(item?.status)?.text}
                          </div>
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              {selectedMO?.mrp_run_id && (
                <div className="linked-doc__wrapper">
                  <H3Text text="MATERIALS REQUIREMENT PLAN" className="linked-doc__title" />
                  <div className="linked-doc__item-header">
                    <CaretRightOutlined />
                    <div className="linked-doc__item-header-id-status__wrapper">
                      <Link to={`/production/planning/view/${selectedMO?.mrp_run_id}`} target="_blank">
                        <H3Text text={`MATERIALS PLAN# ${selectedMO?.mrp_run_number}`} className="linked-doc__item-header-status" />
                      </Link>
                    </div>
                    <Popover
                      placement="topLeft"
                      title={(
                        <div style={{ width: '300px', marginBottom: '10px' }}>
                          Are you sure you want to remove this order from the Materials Plan?
                        </div>
                      )}
                      content={(
                        <React.Fragment>
                          {selectedMO?.is_reservation_enabled && (
                            <Fragment>
                              <div style={{
                                width: '300px',
                                fontSize: '13px',
                                marginBottom: '10px',
                                color: '#2d7cf7',
                                fontWeight: '500',
                              }}
                              >
                                You have stock reserved for the manufacturing order in this MRP. Do you want to cancel the stock reservation?
                              </div>
                              <Radio.Group
                                onChange={(event) => this.setState({ cancelReservation: event?.target?.value })}
                                value={cancelReservation}
                              >
                                <Space direction="horizontal">
                                  <Radio key="yes" value="yes">Yes</Radio>
                                  <Radio key="no" value="no">No</Radio>
                                </Space>
                              </Radio.Group>
                            </Fragment>
                          )}
                          <div style={{ marginTop: '10px' }}>
                            <Button
                              onClick={() => updateMrpRun({
                                mrp_run_id: selectedMO?.mrp_run_id,
                                mo_ids: [selectedMO?.mo_id],
                                remove_batch_reservation: cancelReservation === 'yes',
                              },
                                () => {
                                  getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
                                  getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
                                })}
                              type="primary"
                              size="small"
                              loading={updateMrpRunLoading || updateMrpRunLoading}
                            >
                              Continue
                            </Button>
                          </div>
                        </React.Fragment>
                      )}
                      trigger="click"
                    >
                      <H3Text text="Remove from Plan" className="remove-mrp-run-button" />
                    </Popover>
                  </div>
                </div>
              )}
              {selectedMO && (
                <ActivityLog
                  entityId={selectedMO?.mo_id}
                  entityType="manufacturing_order"
                  entityName="Manufacturing Order"
                />
              )}
            </div>
          </div>
        </div>
        <OrderSummary
          showVerificationModal={toggleVerificationModal}
          toggleVerificationModal={() => this.setState({ toggleVerificationModal: !toggleVerificationModal })}
        />
        <Drawer
          onClose={() => this.setState({ showCreatePoDrawer: false })}
          open={showCreatePoDrawer}
          width="1100px"
          destroyOnClose
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header" style={{ width: '1050px' }}>
              <H3Text text="Send Work Order" className="custom-drawer__title" />
              <H3Image
                src={closeIcon}
                className="custom-drawer__close-icon"
                onClick={() => this.setState({ showCreatePoDrawer: false })}
              />
            </div>
          </div>
          <POForm
            tenantId={selectedMO?.tenant_id}
            moFgLines={poLines}
            moInfo={moInfo}
            moCallback={() => {
              getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
              this.setState({ currentTab: '/work-orders', showCreatePoDrawer: false });
            }}
          />
        </Drawer>
        <Drawer
          onClose={() => this.setState({ showCreateInvoiceDrawer: false })}
          open={showCreateInvoiceDrawer}
          width="1020px"
          destroyOnClose
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header" style={{ width: '975px' }}>
              <H3Text text="Issue Raw Materials" className="custom-drawer__title" />
              <H3Image
                src={closeIcon}
                className="custom-drawer__close-icon"
                onClick={() => this.setState({ showCreateInvoiceDrawer: false })}
              />
            </div>
          </div>
          <InvoiceForm
            workOrder={selectedMO?.subcontractor_pos}
            moRmLines={rmLines}
            moInfo={moInfo}
            tenantId={selectedMO?.tenant_id}
            rmTenantDepartmentId={selectedMO?.rm_tenant_department_id}
            moCallback={() => {
              getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
              getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
              this.setState({ currentTab: '/raw-material-invoices', showCreateInvoiceDrawer: false });
            }}
            isCarryForward
          />
        </Drawer>
        <Drawer
          onClose={() => this.setState({ showReservationDrawer: false })}
          open={showReservationDrawer}
          width="970px"
          destroyOnClose
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header" style={{ width: '920px' }}>
              <H3Text text="Reservation Stock" className="custom-drawer__title" />
              <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ showReservationDrawer: false })} />
            </div>
          </div>
          <ReservationMO
            selectedMO={selectedMO}
            callback={() => {
              this.setState({ showReservationDrawer: false });
              getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
            }}
          />
        </Drawer>
        <Drawer
          onClose={() => {
            this.setState({ showUpdateReservationDrawer: false });
          }}
          open={showUpdateReservationDrawer}
          width="970px"
          destroyOnClose
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header" style={{ width: '920px' }}>
              <H3Text text="Update Reservation Stock" className="custom-drawer__title" />
              <H3Image
                src={closeIcon}
                className="custom-drawer__close-icon"
                onClick={() => {
                  this.setState({ showUpdateReservationDrawer: false });
                }}
              />
            </div>
          </div>
          <UpdateReservation
            order={selectedOrderLine}
            callback={() => {
              getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod);
            }}
            mrpId={selectedMO?.mo_id}
            entityId={selectedOrderLine?.mo_id}
            entityType="MANUFACTURING_ORDER"
            entityLineId={selectedOrderLine?.mo_line_id}
            entityLineType="RAW_MATERIAL"
            productName={selectedOrderLine?.tenant_product_info?.product_sku_name}
            productSkuId={selectedOrderLine?.tenant_product_info?.internal_sku_code}
            productInternalSkuCode={selectedOrderLine?.tenant_product_info?.internal_sku_code}
          />
        </Drawer>
        <Drawer
          onClose={() => this.setState({ showPoForm: false })}
          open={showPoForm}
          width="1100px"
          destroyOnClose
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header" style={{ width: '1050px' }}>
              <H3Text text="Send Work Order" className="custom-drawer__title" />
              <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ showPoForm: false })} />
            </div>
          </div>
          <POForm
            tenantId={selectedMO?.tenant_id}
            jwInfo={jwPoPayload}
            moCallback={() => {
              getMOById(selectedMO?.tenant_id, selectedMO?.mo_id, costingMethod),
                this.setState({
                  showPoForm: false,
                  selectedRowKeys: [],
                  selectedRows: [],
                });
            }}
          />
        </Drawer>
      </div>
    );
  }
}

const mapStateToProps = ({
  UserReducers, GRNReducers, MOReducers, AttachmentReducers, PurchaseRequestReducers, JobWorkReducers,
}) => ({
  user: UserReducers.user,
  getMOByIdLoading: MOReducers.getMOByIdLoading,
  selectedMO: MOReducers.selectedMO,
  updateMOStatusLoading: MOReducers.updateMOStatusLoading,
  grnData: GRNReducers.grnData,
  deleteMOLoading: MOReducers.deleteMOLoading,
  updateAttachmentLoading: AttachmentReducers.updateAttachmentLoading,
  purchaseRequests: PurchaseRequestReducers.purchaseRequests,
  updateMrpRunLoading: MOReducers.updateMrpRunLoading,
  archiveMoLoading: MOReducers.archiveMoLoading,
  moJobWork: JobWorkReducers.moJobWork,
});

const mapDispatchToProps = (dispatch) => ({
  getActivityLog: (entityId, entityName, page, limit, callback) => dispatch(ActivityLogActions.getActivityLog(entityId, entityName, page, limit, callback)),
  updateMOStatus: (payload, callback) => dispatch(MOActions.updateMOStatus(payload, callback)),
  deleteMO: (payload, callback) => dispatch(MOActions.deleteMO(payload, callback)),
  archiveMo: (payload, callback) => dispatch(MOActions.archiveMo(payload, callback)),
  getMOById: (tenantId, moId, costingMethod) => dispatch(MOActions.getMOById(tenantId, moId, costingMethod)),
  getMOByIdSuccess: (selectedMO) => dispatch(MOActions.getMOByIdSuccess(selectedMO)),
  getPurchaseRequests: (tenantId, status, fromDate, toDate, deliveryFromDate, deliveryToDate, tenantRoleId, page, limit, callback, searchKeyword, tenantDepartmentId, moId) => dispatch(PurchaseRequestActions.getPurchaseRequests(tenantId, status, fromDate, toDate, deliveryFromDate, deliveryToDate, tenantRoleId, page, limit, callback, searchKeyword, tenantDepartmentId, moId)),
  getAttachmentById: (entityId, entityName, callback) => dispatch(AttachmentActions.getAttachmentById(entityId, entityName, callback)),
  updateAttachment: (payload, callback) => dispatch(AttachmentActions.updateAttachment(payload, callback)),
  updateMrpRun: (payload, callback) => dispatch(MOActions.updateMrpRun(payload, callback)),
  getActivityLogSuccess: (activityLog) => dispatch(ActivityLogActions.getActivityLogSuccess(activityLog)),
  getJobWorkByMoId: (orgId, tenantId, moId, isListScreen) => dispatch(JobWorkActions.getJobWorkByMoId(orgId, tenantId, moId, isListScreen)),
  downloadDocument: (payload, document) => dispatch(AnalyticsActions.downloadDocument(payload, document)),
  getDownloadQualityChecks: (tenantId, qualityCheckId, entityName, entityId) => dispatch(
    qualityCheckActions.getDownloadQualityChecks(tenantId, qualityCheckId, entityName, entityId),
  ),
  quickUpdateEntityWise: (payload, Callback, rollback) => dispatch(quickUpdateActions.quickUpdateEntityWise(payload, Callback, rollback)),
  getDocCFV2: (payload, callback) => dispatch(CFV2Actions.getDocCFV2(payload, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(ViewManufacturingOrder));
