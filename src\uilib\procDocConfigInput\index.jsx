import React, { Fragment, useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Checkbox, Select, Upload, DatePicker, notification,
} from 'antd';
import {
  PlusOutlined,
} from '@ant-design/icons';
import ImgCrop from 'antd-img-crop';
import Constants, { toISTDate } from '@Apis/constants';
import RichTextEditor from '@Components/Common/RichTextEditor';
import H3Text from '@Uilib/h3Text';
import PRZSelect from '../../components/Common/UI/PRZSelect';
import './style.scss';
import TextArea from 'antd/es/input/TextArea';

const { Option } = Select;
const dateFormat = 'DD/MM/YYYY';

const uploadButton = (
  <div>
    <PlusOutlined />
    <div style={{ marginTop: 8 }}>Upload</div>
  </div>
);

const ProcDocConfigInput = ({
  label, labelClassName, required, containerClassName, fieldType, placeholder, value, maxlength, onChange, inputClassName, disabled, onBlur,
  dropDownOptions, showOnly, showSignatureUpload, showLetterHead, setSignatoryName, signatoryName, showLabelInput, showSignatureTakenByUpload, disableTnc,
}) => {
  const [fileList, setFileList] = useState([]);
  const [emailIds, setEmailIds] = useState([]);

  useEffect(() => {
    if (fieldType === 'image' || fieldType === 'taken_by_sign_base_64') {
      const response = { thumbUrl: value };
      setFileList(value ? [response] : []);
    } else if (fieldType === 'email') {
      const ids = value ? value?.split(' ') : [];
      setEmailIds(ids);
    }
  }, []);

  const colorConfig = [
    {
      color: '#E1AE00',
      name: 'Amber',
    },
    {
      color: '#2D7DF7',
      name: 'Blue',
    },
    {
      color: '#27A52C',
      name: 'Green',
    },
    {
      color: '#2942D1',
      name: 'Indigo',
    },
    {
      color: '#EC6800',
      name: 'Orange',
    },
    {
      color: '#00947C',
      name: 'Teal',
    },
    {
      color: '#7A7A7A',
      name: 'Grey',
    },
    {
      color: '#E41D0F',
      name: 'Red',
    }];
  const colorOptions = colorConfig.map((item) => ({
    value: item.color,
    label: (
      <div className="profile-color-option">
        <div
          style={{ backgroundColor: item.color }}
          className="profile-color-option__swatch"
        />
        <H3Text text={item.name} className="profile-color-option__color" />
      </div>
    ),
  }));

  const countLines = (html) => {
    // Count the number of <p> tags
    const paragraphCount = (html.match(/<p>/g) || []).length;

    // Count the number of <li> tags
    const listItemCount = (html.match(/<li>/g) || []).length;

    // Total number of lines is the sum of <p> tags and <li> tags
    const totalLines = paragraphCount + listItemCount;

    return totalLines;
  };
  const inputLabel = label && (
    <label className={labelClassName || 'default-label'}>
      {label}
      {' '}
      <span style={{ color: 'red' }}>{required ? '*' : ''}</span>
    </label>
  );

  return (
    <Fragment>
      {fieldType === 'letter_head_padding' && showLetterHead && showOnly === 'Template2' && (
        <div className={containerClassName}>
          <div>
            {inputLabel}
          </div>
          <input
            placeholder={placeholder}
            value={value}
            maxLength={maxlength}
            onChange={(e) => onChange(Number(e.target.value))}
            className={fieldType !== 'checkbox' ? inputClassName || 'default-input' : ''}
            disabled={disabled}
            onBlur={onBlur}
            min="0"
          />
        </div>
      )}
      {fieldType === 'footer_padding' && showLetterHead && showOnly === 'Template2' && (
        <div className={containerClassName}>
          <div>
            {inputLabel}
          </div>
          <input
            placeholder={placeholder}
            value={value}
            maxLength={maxlength}
            onChange={(e) => onChange(Number(e.target.value))}
            className={fieldType !== 'checkbox' ? inputClassName || 'default-input' : ''}
            disabled={disabled}
            onBlur={onBlur}
            min="0"
          />
        </div>
      )}
      {fieldType === 'text' && (
        <div className={containerClassName}>
          <div>
            {inputLabel}
          </div>
          <input
            placeholder={placeholder}
            value={value}
            maxLength={maxlength}
            onChange={(e) => onChange(e.target.value)}
            className={fieldType !== 'checkbox' ? inputClassName || 'default-input' : ''}
            disabled={disabled}
            onBlur={onBlur}
            min="0"
          />
        </div>
      )}
      {fieldType === 'color' && showOnly === 'Template1' && (
        <div className={containerClassName}>
          <div>
            {inputLabel}
          </div>
          <PRZSelect
            className="input proc-doc__select-input"
            value={value}
            onChange={(val) => onChange(val)}
            options={colorOptions}
          />
        </div>
      )}
      {fieldType === 'dropdown' && (
        <div className={containerClassName}>
          <div>
            {inputLabel}
          </div>
          <PRZSelect
            className="input proc-doc__select-input"
            value={value}
            onChange={(val) => onChange(val)}
          >
            {dropDownOptions?.map((item) => (
              <Option key={item?.key} value={item?.value} />
            ))}
          </PRZSelect>
        </div>
      )}
      {fieldType === 'placement_of_t_and_c' && (
        <div className={containerClassName}>
          <div>
            {inputLabel}
          </div>
          <PRZSelect
            className="input proc-doc__select-input"
            value={value}
            onChange={(val) => {
              setSignatoryName(val);
              onChange(val);
            }}
          >
            {dropDownOptions?.map((item) => (
              <Option key={item?.key} value={item?.value}>
                {item?.label}
              </Option>
            ))}
          </PRZSelect>
        </div>
      )}
      {fieldType === 'rich_text' && (
        <div className={containerClassName}>
          <div>
            {inputLabel}
          </div>
          <RichTextEditor
            value={value}
            disabled={disableTnc}
            placeholder="Start typing here.."
            onChange={(val) => {
              const lengthOfText = countLines(val);
              if (signatoryName === 'Full Page') {
                onChange(val);
              } else if (lengthOfText <= 8 && signatoryName === 'Compact') {
                onChange(val);
              } else {
                notification.error({
                  message: 'In the case of compact Terms & Conditions, the maximum number of bullet points allowed is 8.',
                  placement: 'top',
                  duration: 4,
                });
              }
            }}
          />
        </div>
      )}
      {fieldType === 'checkbox' && (
        <div className={containerClassName}>
          <Checkbox
            checked={value}
            onChange={() => onChange(!value)}
            disabled={disabled}
            onBlur={onBlur}
            style={{ marginTop: '3px' }}
          >
            {label}
          </Checkbox>
        </div>
      )}
      {fieldType === 'print_bundled_products' && (showOnly !== 'Template3') && (
        <div className={containerClassName}>
          <Checkbox
            checked={value}
            onChange={() => onChange(!value)}
            disabled={disabled}
            onBlur={onBlur}
            style={{ marginTop: '3px' }}
          >
            {label}
          </Checkbox>
        </div>
      )}
      {fieldType === 'print_product_images' && (showOnly !== 'Template3') && (
        <div className={containerClassName}>
          <Checkbox
            checked={value}
            onChange={() => onChange(!value)}
            disabled={disabled}
            onBlur={onBlur}
            style={{ marginTop: '3px' }}
          >
            {label}
          </Checkbox>
        </div>
      )}
      {fieldType === 'print_batch_information' && (showOnly !== 'Template3') && (
        <div className={containerClassName}>
          <Checkbox
            checked={value}
            onChange={() => onChange(!value)}
            disabled={disabled}
            onBlur={onBlur}
            style={{ marginTop: '3px' }}
          >
            {label}
          </Checkbox>
        </div>
      )}
      {fieldType === 'print_discount' && (showOnly !== 'Template3') && (
        <div className={containerClassName}>
          <Checkbox
            checked={value}
            onChange={() => onChange(!value)}
            disabled={disabled}
            onBlur={onBlur}
            style={{ marginTop: '3px' }}
          >
            {label}
          </Checkbox>
        </div>
      )}
      {fieldType === 'print_tax' && (showOnly !== 'Template3') && (
        <div className={containerClassName}>
          <Checkbox
            checked={value}
            onChange={() => onChange(!value)}
            disabled={disabled}
            onBlur={onBlur}
            style={{ marginTop: '3px' }}
          >
            {label}
          </Checkbox>
        </div>
      )}
      {fieldType === 'print_item_code' && (showOnly !== 'Template3') && (
        <div className={containerClassName}>
          <Checkbox
            checked={value}
            onChange={() => onChange(!value)}
            disabled={disabled}
            onBlur={onBlur}
            style={{ marginTop: '3px' }}
          >
            {label}
          </Checkbox>
        </div>
      )}
      {fieldType === 'print_prepared_by' && (showOnly !== 'Template3') && (
        <div className={containerClassName}>
          <Checkbox
            checked={value}
            onChange={() => onChange(!value)}
            disabled={disabled}
            onBlur={onBlur}
            style={{ marginTop: '3px' }}
          >
            {label}
          </Checkbox>
        </div>
      )}
      {fieldType === 'print_prepared_at' && (showOnly !== 'Template3') && (
        <div className={containerClassName}>
          <Checkbox
            checked={value}
            onChange={() => onChange(!value)}
            disabled={disabled}
            onBlur={onBlur}
            style={{ marginTop: '3px' }}
          >
            {label}
          </Checkbox>
        </div>
      )}
      {fieldType === 'print_letter_head' && showOnly === 'Template2' && (
        <div className={containerClassName}>
          <Checkbox
            checked={value}
            onChange={() => onChange(!value)}
            disabled={disabled}
            onBlur={onBlur}
            style={{ marginTop: '3px' }}
          >
            {label}
          </Checkbox>
        </div>
      )}
      <div className="ant-md-col-24" />
      {fieldType === 'print_signature_input' && (
        <div className={containerClassName}>
          <Checkbox
            checked={value}
            onChange={(e) => {
              onChange(!value);
            }}
            disabled={disabled}
            onBlur={onBlur}
            style={{ marginTop: '3px' }}
          >
            {label}
          </Checkbox>

        </div>
      )}
      {fieldType === 'print_taken_by_signature_input' && (
        <div className={containerClassName}>
          <Checkbox
            checked={value}
            onChange={(e) => {
              onChange(!value);
            }}
            disabled={disabled}
            onBlur={onBlur}
            style={{ marginTop: '3px' }}
          >
            {label}
          </Checkbox>

        </div>
      )}
      {fieldType === 'image' && showSignatureUpload && (
        <div className={containerClassName}>
          <div>
            {inputLabel}
          </div>
          <div className="signature__wrapper-img">
            <ImgCrop
              aspect={4 / 1}
              crop={{
                unit: 'px',
                width: 200,
                height: 50,
              }}
              cropperProps={{
                crop: {
                  unit: 'px',
                  width: 200,
                  height: 50,
                },
                restrictPosition: false,
              }}
              minZoom={0.1}
            >
              <Upload
                action={Constants.UPLOAD_FILE}
                listType="picture-card"
                fileList={fileList}
                onChange={(fList) => {
                  setFileList(fList?.fileList);
                  onChange(fList?.fileList);
                }}
                showUploadList={{ showPreviewIcon: false }}
              >
                {value?.length >= 1 ? null
                  : uploadButton}
              </Upload>
            </ImgCrop>
            <div className="signature__wrapper-text">
              This e-signature will be printed in the authorised signatory column in the document
              <ul>
                <li>200px x 50px image size is recommended</li>
                <li>1MB Maximum file size allowed</li>
              </ul>
            </div>
          </div>
        </div>
      )}
      {fieldType === 'taken_by_sign_base_64' && showSignatureTakenByUpload && (
        <div className={containerClassName}>
          <div>
            {inputLabel}
          </div>
          <div className="signature__wrapper-img">
            <ImgCrop
              aspect={4 / 1}
              crop={{
                unit: 'px',
                width: 200,
                height: 50,
              }}
              cropperProps={{
                crop: {
                  unit: 'px',
                  width: 200,
                  height: 50,
                },
                restrictPosition: false,
              }}
              minZoom={0.1}
            >
              <Upload
                action={Constants.UPLOAD_FILE}
                listType="picture-card"
                fileList={fileList}
                onChange={(fList) => {
                  setFileList(fList?.fileList);
                  onChange(fList?.fileList);
                }}
                showUploadList={{ showPreviewIcon: false }}
              >
                {value?.length >= 1 ? null
                  : uploadButton}
              </Upload>
            </ImgCrop>
            <div className="signature__wrapper-text">
              This e-signature will be printed in the taken by signatory column in the document
              <ul>
                <li>200px x 50px image size is recommended</li>
                <li>1MB Maximum file size allowed</li>
              </ul>
            </div>
          </div>
        </div>
      )}
      {(fieldType === 'signature_name' && showSignatureUpload) && (
        <div className={containerClassName}>
          <div>
            {inputLabel}
          </div>
          <input
            placeholder={placeholder}
            value={value}
            maxLength={maxlength}
            onChange={(e) => onChange(e.target.value)}
            className={fieldType !== 'checkbox' ? inputClassName || 'default-input' : ''}
            disabled={disabled}
            onBlur={onBlur}
            min="0"
          />
        </div>
      )}
      {(fieldType === 'taken_by_name' && showSignatureTakenByUpload) && (
        <div className={containerClassName}>
          <div>
            {inputLabel}
          </div>
          <input
            placeholder={placeholder}
            value={value}
            maxLength={maxlength}
            onChange={(e) => onChange(e.target.value)}
            className={fieldType !== 'checkbox' ? inputClassName || 'default-input' : ''}
            disabled={disabled}
            onBlur={onBlur}
            min="0"
          />
        </div>
      )}
      {fieldType === 'email' && (
        <div className={containerClassName}>
          <div>
            {inputLabel}
          </div>
          <PRZSelect
            mode="tags"
            maxTagCount="responsive"
            filterOption={false}
            className="input proc-doc__select-input"
            value={emailIds}
            onChange={(val) => {
              setEmailIds(val);
              onChange(val);
            }}
          >
            {emailIds?.map((item) => (
              <Option key={item} value={item}>
                {item}
              </Option>
            ))}
          </PRZSelect>
        </div>
      )}
      {(fieldType === 'number') && (
        <div className={containerClassName}>
          <div>
            {inputLabel}
          </div>
          <input
            type="number"
            placeholder={placeholder}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            className={inputClassName}
            disabled={disabled}
            onBlur={onBlur}
            min="50"
            max="100"
          />
        </div>
      )}
      {fieldType === 'logo_height' && (showOnly !== 'Template3') && (
        <div className={containerClassName}>
          <div>
            {inputLabel}
          </div>
          <input
            type="number"
            placeholder={placeholder}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            className={inputClassName}
            disabled={disabled}
            onBlur={onBlur}
            min="50"
            max="100"
          />
        </div>
      )}
      {fieldType === 'text_box' && (
        <div className="ant-col-md-24 ant-col-xs-24" style={{ marginTop: '5px', marginBottom: '10px' }}>
          <div className="orgInputContainer">
            <label className="orgFormLabel">
              {inputLabel}
            </label>
            <div className="ant-col-md-12 ant-col-xs-24">
              <TextArea
                rows={5}
                onChange={(event) => {
                  onChange(event.target.value);
                }}
                value={value}
                inputClassName="orgFormInput"
                style={{
                  width: '100%',
                }}
              />
            </div>
          </div>
        </div>
      )}
      {fieldType === 'print_labels' && (
        <div className={containerClassName}>
          <Checkbox
            checked={value}
            onChange={() => onChange(!value)}
            disabled={disabled}
            onBlur={onBlur}
            style={{ marginTop: '3px' }}
          >
            {label}
          </Checkbox>
        </div>
      )}
      {(fieldType === 'label_name_input' && showLabelInput) && (
        <div className={containerClassName}>
          <div>
            {inputLabel}
          </div>
          <input
            placeholder={placeholder}
            value={value}
            maxLength={maxlength}
            onChange={(e) => onChange(e.target.value)}
            className={inputClassName}
            disabled={disabled}
            onBlur={onBlur}
            min="0"
          />
        </div>
      )}
    </Fragment>
  );
};

ProcDocConfigInput.propTypes = {
  containerClassName: PropTypes.string,
  labelClassName: PropTypes.string,
  label: PropTypes.string,
  inputClassName: PropTypes.string,
  placeholder: PropTypes.string,
  maxlength: PropTypes.string,
  fieldType: PropTypes.string,
  value: PropTypes.string,
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  required: PropTypes.bool,
  disabled: PropTypes.bool,
  dropDownOptions: PropTypes.array,
  showOnly: PropTypes.string,
  showSignatureUpload: PropTypes.bool,
  showSignatureTakenByUpload: PropTypes.bool,

};

export default ProcDocConfigInput;
