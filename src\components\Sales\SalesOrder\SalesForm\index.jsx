import React, { Component, Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import Constants, { DEFAULT_CUR_ROUND_OFF } from '@Apis/constants';
import H3Text from '@Uilib/h3Text';
import RichTextEditor from '@Components/Common/RichTextEditor';
import {
  DatePicker,
  Drawer,
  notification,
  Upload,
  Checkbox,
  Radio,
  Popconfirm,
  Tooltip,
} from 'antd';
import H3Image from '@Uilib/h3Image';
import CustomerActions from '@Actions/customerActions';
import CustomStatusActions from '@Actions/configurations/customStatusActions';
import DocConfigActions from '@Actions/docConfigActions';
import closeIcon from '@Images/icons/icon-close-blue.png';
import { EditFilled, PlusCircleFilled, PlusOutlined } from '@ant-design/icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCircleXmark, faInfo, faCircleInfo, } from '@fortawesome/free-solid-svg-icons';
import SOActions from '@Actions/sales/soActions';
import H3FormInput from '@Uilib/h3FormInput';
import { v4 as uuidv4 } from 'uuid';
import AddressSelector from '@Components/Common/FormUtils/AddressSelecter';
import dayjs from 'dayjs';
import TenantSelector from '@Components/Common/Selector/TenantSelector';
import OfferActions from '@Actions/offerActions';
import PriceListActions from '@Actions/inventory/priceListActions';
import CFV2Actions from '@Actions/configurations/cfV2Actions';
import SelectDepartment from '@Components/Common/SelectDepartment';
import SelectAppUser from '@Components/Common/SelectAppUser';
import ProductActions from '@Actions/productActions';
import SelectPaymentTerm from '@Components/Common/SelectPaymentTerm';
import Helpers from '@Apis/helpers';
import BarcodeReader from '@Components/Common/BarcodeReader';
import CurrenciesActions from '@Actions/configurations/currenciesAction';
import Crown from '@Images/crown2.png';
import CustomFieldV3 from '@Components/Common/CustomFieldV3';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import CustomDocumentInputs from '@Components/Common/CustomDocumentInputs';
import CustomDocumentColumns from '@Components/Common/CustomDocumentColumns';
import TagSelector from '@Components/Common/Selector/TagSelector';
import TagActions from '@Actions/tagActions';
import ExtraChargesActions from '@Actions/configurations/extraChargesAction';
import TransporterForm from '@Components/Common/TransporterForm';
import FormHelpers from '@Helpers/FormHelpers';
import FreightTaxInput from '@Components/Common/FreightTaxInput';
import FormLoadingSkull from '@Components/Common/formLoadingSkull';
import SelectExtraCharge from '../../../Admin/Common/SelectExtraCharge';
import SettingsInfo from '../../../Common/SettingsInfo';
import BulkUploadSo from './BulkUploadSo';
import CustomerForm from '../../Customer/CustomerHome/CreateCustomer/CustomerForm';
import SelectCustomer from '../../../Common/SelectCustomer';
import SoLines from './SoLines';
import SelectPriceList from '../../../Common/SelectPriceList';
import './style.scss';
import documentUINamesConstant from '../../../../apis/documentUINamesConstant';
import CurrencyConversionV2 from '../../../Common/CurrencyConversionV2';
import salesErrors from './salesErrors';
import ErrorHandle from '../../../Common/ErrorHandle';
import RestrictedAccessMessage from '../../../Common/RestrictedAccess/RestrictedAccessMessage';
import PRZButton from '../../../Common/UI/PRZButton';
import PRZConfirmationPopover from '../../../Common/UI/PRZConfirmationPopover';
import PRZInput from '../../../Common/UI/PRZInput';
import PRZText from '../../../Common/UI/PRZText';
import ChargesTaxInput from '../../../Common/ChargesTaxInput';
import PRZSelect from '../../../Common/UI/PRZSelect';
import DocumentNumberSeqInput from '../../../Admin/Common/DocumentNumberSeqInput';
import TnCSelector from '@Components/Admin/Common/TnCSelector';

const uploadButton = (
  <div>
    <PlusOutlined />
    <div style={{ marginTop: 8 }}>Upload</div>
  </div>
);

class SalesForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [
        {
          key: uuidv4(),
          asset1: '',
          product_sku_name: '',
          quantity: '',
          unitPrice: '',
          lot: 0,
          taxId: '',
          discount: 0,
          child_taxes: [
            {
              tax_amount: 0,
              tax_type_name: '',
            },
          ],
          lineCustomFields: [],
        },
      ],
      chargeData: [],
      paymentTerms: 0,
      orderDate: dayjs(),
      estimateExpiryDate: '',
      deliveryDate: dayjs(),
      attachments: [],
      textAreaHeight: 2,
      isLineWiseDiscount: true,
      showAddCustomer: true,
      showNewCustomerModal: false,
      pricesToApply: [],
      termsAndConditions: '',
      selectedCurrencyName: '',
      selectedCurrencyID: '',
      sendWhatsappNotification: false,
      accountManager: '',
      charge1Name: 'Freight',
      charge1Value: 0,
      customVoucherNumber: '',
      discountType: 'Percent',
      visibleColumns: {
        PRODUCT: {
          label: 'Product',
          visible: true,
          disabled: true,
        },
        QUANTITY: {
          label: 'Quantity',
          visible: true,
          disabled: true,
        },
        UNIT: {
          label: 'Unit',
          visible: true,
          disabled: true,
        },
        UNIT_PRICE: {
          label: 'Unit Price',
          visible: true,
          disabled: true,
        },
        TAX: {
          label: 'Tax',
          visible: true,
          disabled: true,
        },
        DISCOUNT: {
          label: 'Discount',
          visible: true,
          disabled: true,
        },
        LINE_TOTAL: {
          label: 'Line Total',
          visible: true,
          disabled: true,
        },
      },
      freightTaxId: 'Not Applicable',
      freightTax: null,
      openFreightTax: false,
      isAutomaticConversionRate: null,
      currencyConversionRate: '',
      documentUINames: {},
      docLevelErrors: [],
      lineLevelErrors: [],
      updateDocumentReason: '',
      freightTaxData: {
        child_taxes: [
          {
            tax_amount: 0,
            tax_type_name: '',
          },
        ],
      },
      productSkuIds: [],
    };
  }

  componentDidMount() {
    const {
      getSOById,
      user,
      match,
      getDocCFV2,
      getOrgStatus,
      getDocConfig,
      getCurrencies,
      location,
      isSalesEstimate,
      estimateTenantId,
      getApplyPriceLists,
      getCharges,
    } = this.props;

    const tempDocumentUINames = documentUINamesConstant(user);

    getCharges(user?.tenant_info?.org_id, 'SALES', (charges) => {
      const freight = charges?.find((i) => (i?.ledger_name === 'Freight Charge' && i?.is_system_charge));
      if (freight) {
        this.setState({ freightSacCode: freight?.charge_sac_code });
      }
    });

    this.setState({
      isEstimate: !!(
        !isSalesEstimate && window.location.href.includes('/estimate')
      ),
      salesOrderType:
        !isSalesEstimate && window.location.href.includes('/estimate')
          ? 'SALES_ESTIMATE'
          : 'SALES_ORDER',
      documentUINames: {
        uiName:
          !isSalesEstimate && window.location.href.includes('/estimate')
            ? tempDocumentUINames.estimateUIName
            : tempDocumentUINames.salesOrderUIName,
        shortUIName:
          !isSalesEstimate && window.location.href.includes('/estimate')
            ? tempDocumentUINames.estimateShortUIName
            : tempDocumentUINames.salesOrderShortUIName,
        salesOrderUIName: tempDocumentUINames.salesOrderUIName,
        salesOrderShortUIName: tempDocumentUINames.salesOrderShortUIName,
        estimateUIName: tempDocumentUINames.estimateUIName,
        estimateShortUIName: tempDocumentUINames.estimateShortUIName,
      },
    });

    let entityName = 'SALES_ORDER';
    let permissionEntity = Helpers.permissionEntities.SALES_ORDER;
    let isWhatsappNotificationEnabled =
      user?.tenant_info?.is_so_automatic_notification_enabled;
    if (!isSalesEstimate && window.location.href.includes('/estimate')) {
      entityName = 'SALES_ESTIMATE';
      isWhatsappNotificationEnabled =
        user?.tenant_info?.is_se_automatic_notification_enabled;
      permissionEntity = Helpers.permissionEntities.SALES_ESTIMATE;
    }
    const { orderId } = match.params;
    const SoIdClone = location?.state?.cloneSo;
    const selectedTenantIdSo = isSalesEstimate
      ? estimateTenantId
      : location?.state?.selectedTenantIdSo;
    const soId = SoIdClone || orderId;
    getCurrencies();
    const payload = {
      orgId: user?.tenant_info?.org_id,
      entityName,
    };
    getDocCFV2(payload);
    getDocCFV2({
      orgId: user?.tenant_info?.org_id,
      entityName: 'ANNUAL_MAINTENANCE_CONTRACT',
    });
    getOrgStatus(
      {
        orgId: user?.tenant_info?.org_id,
        entityName: 'SALES_ORDER',
        orderType: entityName,
      },
      () => { }
    );
    if (soId || isSalesEstimate) {
      getSOById({
        orgId: user?.tenant_info?.org_id,
        tenantId:
          Helpers.getTenantEntityPermission(
            user?.user_tenants,
            permissionEntity,
            Helpers.permissionTypes.UPDATE
          ).join(',') || user?.tenant_info?.tenant_id,
        soId,
        orderType: entityName,
      });
    } else {
      getDocConfig(
        selectedTenantIdSo || user?.tenant_info?.tenant_id,
        entityName,
        (doc) => {
          const termsData = doc
            ?.find((item) => item?.document_type === `${entityName}_DOWNLOAD`)
            ?.allowed_fields?.find(
              (field) => field?.field_name === 'default_t_and_c'
            )?.field_value;
          this.setState({
            termsAndConditions: termsData,
            sendWhatsappNotification: isWhatsappNotificationEnabled,
          });
        }
      );
    }
    if (Helpers.getPermission(Helpers.permissionEntities.SALES_ORDER, Helpers.permissionTypes.CREATE, user) && user?.tenant_info?.sales_config?.sub_modules?.sales_order?.is_active) {
      if (!isSalesEstimate && window.location.href.includes('/estimate')) {
        this.setState({
          salesOrderType: 'SALES_ESTIMATE',
        });
      } else {
        this.setState({
          salesOrderType: 'SALES_ORDER',
        });
      }
    } else if (Helpers.getPermission(Helpers.permissionEntities.ANNUAL_MAINTENANCE_CONTRACT, Helpers.permissionTypes.CREATE, user) && user?.tenant_info?.sales_config?.sub_modules?.ANNANNUAL_MAINTENANCE_CONTRACT?.is_active) {
      if (!isSalesEstimate && window.location.href.includes('/estimate')) {
        this.setState({
          salesOrderType: 'SALES_ESTIMATE',
        });
      } else {
        this.setState({
          salesOrderType: 'ANNUAL_MAINTENANCE_CONTRACT',
        }, () => {
          getDocConfig(selectedTenantIdSo || user?.tenant_info?.tenant_id, 'ANNUAL_MAINTENANCE_CONTRACT', (doc) => {
            const termsData = doc?.find((item) => item?.document_type === 'ANNUAL_MAINTENANCE_CONTRACT_DOWNLOAD')?.allowed_fields?.find((field) => field?.field_name === 'default_t_and_c')?.field_value;
            this.setState({
              termsAndConditions: termsData,
            });
          });
        });
      }
    }
  }

  getDraftStatusType() {
    const { orgStatusSalesOrder } = this.props;
    const orgStatusData = orgStatusSalesOrder?.statuses;

    return orgStatusData?.filter((status) => status?.type === 'DRAFT');
  }

  componentWillUnmount() {
    const { getSOByIdSuccess, getDocCFV2Success } = this.props;
    getDocCFV2Success(null);
    if (!window?.location?.pathname?.includes('/sales/estimate/view/')) {
      getSOByIdSuccess(null);
    }
  }

  static getDerivedStateFromProps(props, state) {
    const { user, CurrenciesResults, isSalesEstimate, location, getApplyPriceLists, } = props;
    const {
      selectedTenant,
      tenantDepartmentId,
      documentUINames,
      orderDate,
      isEstimate,
      deliveryDate,
      selectedCustomer,
      cfSalesOrderDoc,
      shippingAddress,
      billingAddress,
      data,
      chargeData,
      gstNumber,
      formSubmitted,
      docLevelErrors,
      lineLevelErrors,
      salesOrderType,
    } = state;
    const dataFromOcrToSo = location?.state?.dataFromOcrToSo;
    if (
      CurrenciesResults &&
      CurrenciesResults?.length > 0 &&
      !props.selectedSO?.status &&
      !props.selectedSO
    ) {
      const defaultCurrency = CurrenciesResults?.find(
        (currency) => currency.is_default === true
      );
      if (defaultCurrency && !state.selectedCurrencyID) {
        return {
          ...state,
          selectedCurrencyID: defaultCurrency?.org_currency_id,
          selectedCurrencyName: defaultCurrency,
          isAutomaticConversionRate:
            user?.tenant_info?.global_config?.settings
              ?.automatic_conversion_rate,
          currencyConversionRate: user?.tenant_info?.global_config?.settings
            ?.automatic_conversion_rate
            ? defaultCurrency?.automatic_conversion_rate
            : defaultCurrency?.conversion_rate,
        };
      }
    }

    if (dataFromOcrToSo && props?.cfV2DocSalesOrder && !state?.isOcrDataReady) {
      const selectedOrder = dataFromOcrToSo;
      const inventoryConfig = user?.tenant_info?.inventory_config;
      const departmentLevelStock =
        inventoryConfig?.settings?.department_level_stock;
      const lineCFs = props.cfV2DocSalesOrder?.data?.document_line_custom_fields
        ?.length
        ? CustomFieldHelpers.getCfStructure(
          props.cfV2DocSalesOrder?.data?.document_line_custom_fields?.filter(
            (item) => item?.is_active
          ) || [],
          false
        )
        : [];
      const salesOrderLines = selectedOrder?.linesData?.map((orderLine) => ({
        key: uuidv4(),
        product_sku_name: orderLine?.product_info?.product_sku_name,
        product_category_info: orderLine?.product_info?.product_category_info,
        tenant_product_id: orderLine?.tenant_product_id,
        product_sku_id: orderLine?.product_info?.product_sku_id,
        product_sku_info: orderLine?.product_info,
        available_qty: orderLine?.department_quantity,
        asset1: orderLine?.product_info?.assets?.[0]?.url || '',
        quantity: Number(orderLine?.quantity) || 1,
        unitPrice: Number(orderLine?.offer_price) || 1,
        uomId: Number(orderLine?.uom_info?.uom_id),
        uomGroup: Number(orderLine?.group_id),
        taxId: Number(orderLine?.tax_info?.tax_id),
        taxInfo: orderLine?.tax_info,
        child_taxes: Helpers.computeTaxation(
          orderLine.quantity *
          orderLine.offer_price *
          (orderLine.line_discount_percentage
            ? Number(100 - orderLine.line_discount_percentage) / 100
            : 1),
          orderLine?.tax_info,
          user?.tenant_info?.state,
          selectedOrder?.billing_address_info?.state
        )?.tax_info?.child_taxes,
        remarks: orderLine?.product_info?.remarks,
        hsn_code: orderLine?.product_info?.hsn_code,
        remarkRequired: !!orderLine?.product_info?.remarks?.replace(
          /<[^>]+>/g,
          ''
        ),
        discount: orderLine?.line_discount_percentage,
        product_type: orderLine?.product_info?.product_type,
        showFreebie:
          Number(orderLine?.free_quantity) > 0
            ? Number(orderLine?.free_quantity)
            : null,
        free_quantity: orderLine?.free_quantity || 0,
        lineCustomFields: FormHelpers.lineSystemFieldValue(
          lineCFs,
          orderLine?.custom_fields
        )?.map((k) => ({
          ...k,
          fieldValue:
            k?.fieldName === 'Rate'
              ? Number(orderLine.offer_price)
              : k?.fieldName === 'Quantity'
                ? Number(orderLine.quantity)
                : k?.fieldValue,
        })),
        bundle_products: orderLine?.bundle_products?.map((item) => ({
          ...item,
          product_info: item?.product_info,
          product_sku_info: orderLine?.product_info?.product_sku_id,
          available_qty: orderLine?.department_quantity,
          uom_info: item?.uom_info?.[0],
          bundle_quantity: item?.quantity / orderLine?.quantity,
          tax_info: item?.tax_group_info,
        })),
        uom_info: orderLine?.uom_info,
        uom_list: orderLine?.uom_list,
      }));
      return {
        ...state,
        isOcrDataReady: true,
        tenantDepartmentId: departmentLevelStock
          ? user?.tenant_info?.default_department_for_sales
          : user?.tenant_info?.default_store_id,
        selectedTenant: user?.tenant_info?.tenant_id,
        productSkuIds:
          selectedOrder?.linesData?.map(
            (salesOrderLine) => salesOrderLine?.product_info?.product_sku_id
          ) || [],
        selectedCustomer: Number(selectedOrder?.selectedCustomer?.customer_id),
        selectedCustomerInfo: selectedOrder?.selectedCustomer,
        accountManager:
          selectedOrder?.selectedCustomer?.customer_info?.account_manager_info
            ?.account_manager_id,
        gstNumber: selectedOrder?.selectedCustomer?.customer_info?.gst_number,
        orderDate: dayjs(selectedOrder?.order_date, 'DD-MM-YYYY'),
        estimateExpiryDate: selectedOrder?.estimate_expiry_date
          ? dayjs(selectedOrder?.estimate_expiry_date, 'DD-MM-YYYY')
          : '',
        deliveryDate: dayjs(selectedOrder?.delivery_date, 'DD-MM-YYYY'),
        billingAddress: selectedOrder?.billingAddress,
        shippingAddress: selectedOrder?.shippingAddress,
        data: salesOrderLines,
        fileList: selectedOrder?.ocrDocData,
        selectedOrder,
        discountPercentage: selectedOrder?.discount_percentage,
        isLineWiseDiscount: selectedOrder?.is_line_wise_discount,
        chargeData:
          selectedOrder?.other_charges?.map((item) => ({
            ...item,
            chargeKey: uuidv4(),
            chargesTaxId: item?.tax_info?.tax_id,
            chargesSacCode: item?.charge_sac_code,
            chargesTaxInfo: item?.tax_info,
            chargesTax: item?.tax_info?.tax_value,
            chargesTaxData: {
              ...item?.tax_info,
              child_taxes: Helpers.computeTaxation(item?.charge_amount, item?.tax_info, user?.tenant_info?.state, selectedOrder?.billing_address_info?.state)?.tax_info?.child_taxes
            },
          })) || [],
        cfSalesOrderDoc: props.cfV2DocSalesOrder?.data?.document_custom_fields
          ?.length
          ? CustomFieldHelpers.getCfStructure(
            props.cfV2DocSalesOrder?.data?.document_custom_fields || [],
            true
          )
          : [],
        cfSalesOrderLine: lineCFs,
        visibleColumns: props.cfV2DocSalesOrder?.data
          ?.document_line_custom_fields
          ? CustomFieldHelpers.updateVisibleColumns(
            props.cfV2DocSalesOrder?.data?.document_line_custom_fields || [],
            state.visibleColumns
          )
          : state.visibleColumns,
      };
    }

    if (!state.isUserReadyForCF && !dataFromOcrToSo) {
      const inventoryConfig = user?.tenant_info?.inventory_config;
      const departmentLevelStock =
        inventoryConfig?.settings?.department_level_stock;
      if (
        (isSalesEstimate
          ? true
          : !window.location.href.includes('/estimate')) &&
        props?.cfV2DocSalesOrder?.data?.success && props?.cfV2DocAnnualMaintenanceContract?.data?.success
      ) {
        const lineCFs = props.cfV2DocSalesOrder?.data
          ?.document_line_custom_fields?.length
          ? CustomFieldHelpers.getCfStructure(
            props.cfV2DocSalesOrder?.data?.document_line_custom_fields?.filter(
              (item) => item?.is_active
            ) || [],
            false
          )
          : [];

        const linesCFsAMC = props.cfV2DocAnnualMaintenanceContract?.data
          ?.document_line_custom_fields?.length
          ? CustomFieldHelpers.getCfStructure(
            props.cfV2DocAnnualMaintenanceContract?.data?.document_line_custom_fields?.filter(
              (item) => item?.is_active
            ) || [],
            false
          )
          : [];

        return {
          ...state,
          cfSalesOrderDoc: salesOrderType === 'ANNUAL_MAINTENANCE_CONTRACT' ?
            props.cfV2DocAnnualMaintenanceContract?.data?.document_custom_fields
              ?.length
              ? CustomFieldHelpers.getCfStructure(
                props.cfV2DocAnnualMaintenanceContract?.data?.document_custom_fields || [],
                true
              )
              : []
            : props.cfV2DocSalesOrder?.data?.document_custom_fields
              ?.length
              ? CustomFieldHelpers.getCfStructure(
                props.cfV2DocSalesOrder?.data?.document_custom_fields || [],
                true
              )
              : [],
          cfSalesOrderDocSO: props.cfV2DocSalesOrder?.data?.document_custom_fields
            ?.length
            ? CustomFieldHelpers.getCfStructure(
              props.cfV2DocSalesOrder?.data?.document_custom_fields || [],
              true
            )
            : [],
          cfSalesOrderDocAMC: props.cfV2DocAnnualMaintenanceContract?.data?.document_custom_fields
            ?.length
            ? CustomFieldHelpers.getCfStructure(
              props.cfV2DocAnnualMaintenanceContract?.data?.document_custom_fields || [],
              true
            )
            : [],
          cfSalesOrderLine: salesOrderType === 'ANNUAL_MAINTENANCE_CONTRACT' ? linesCFsAMC : lineCFs,
          cfSalesOrderLineSO: lineCFs,
          cfSalesOrderLineAMC: linesCFsAMC,
          visibleColumns: props.cfV2DocSalesOrder?.data
            ?.document_line_custom_fields
            ? CustomFieldHelpers.updateVisibleColumns(
              props.cfV2DocSalesOrder?.data?.document_line_custom_fields ||
              [],
              state.visibleColumns
            )
            : state.visibleColumns,
          visibleColumnsSO: props.cfV2DocSalesOrder?.data
            ?.document_line_custom_fields
            ? CustomFieldHelpers.updateVisibleColumns(
              props.cfV2DocSalesOrder?.data?.document_line_custom_fields ||
              [],
              state.visibleColumns
            )
            : state.visibleColumns,
          visibleColumnsAMC: props.cfV2DocAnnualMaintenanceContract?.data
            ?.document_line_custom_fields
            ? CustomFieldHelpers.updateVisibleColumns(
              props.cfV2DocAnnualMaintenanceContract?.data?.document_line_custom_fields ||
              [],
              state.visibleColumns
            )
            : state.visibleColumns,
          // data: state.data.map((item) => ({
          //   ...item,
          //   lineCustomFields: lineCFs,
          // })),
          data: state.data.map((item) => ({
            ...item,
            lineCustomFields: salesOrderType === 'ANNUAL_MAINTENANCE_CONTRACT' ? linesCFsAMC : lineCFs,
          })),
          selectedTenant: user?.tenant_info?.tenant_id,
          tenantDepartmentId: departmentLevelStock
            ? user?.tenant_info?.default_department_for_sales
            : user?.tenant_info?.default_store_id,
          isUserReadyForCF: true,
        };
      }
      if (props?.cfV2DocSalesEstimate) {
        const lineCFs =
          CustomFieldHelpers.getCfStructure(
            props.cfV2DocSalesEstimate?.data?.document_line_custom_fields,
            false
          ) || [];

        return {
          ...state,

          cfSalesOrderDoc:
            CustomFieldHelpers.getCfStructure(
              props.cfV2DocSalesEstimate?.data?.document_custom_fields,
              true
            ) || [],
          cfSalesOrderLine: lineCFs,
          visibleColumns: CustomFieldHelpers.updateVisibleColumns(
            props.cfV2DocSalesEstimate?.data?.document_line_custom_fields,
            state.visibleColumns
          ),
          data: state.data.map((item) => ({
            ...item,
            lineCustomFields: lineCFs,
          })),
          selectedTenant: user?.tenant_info?.tenant_id,
          tenantDepartmentId: departmentLevelStock
            ? user?.tenant_info?.default_department_for_sales
            : user?.tenant_info?.default_store_id,
          isUserReadyForCF: true,
        };
      }
    }

    if (
      (props?.selectedSO || isSalesEstimate) &&
      (props?.match?.params?.orderId || props?.location?.state?.cloneSo) &&
      // (props?.cfV2DocSalesOrder && props.cfV2DocSalesEstimate) &&
      ((!isSalesEstimate && window.location.href.includes('/estimate')) ? props.cfV2DocSalesEstimate?.data?.success : props.cfV2DocSalesOrder?.data?.success && props.cfV2DocAnnualMaintenanceContract?.data?.success) &&
      !state?.isUserReady &&
      !dataFromOcrToSo
    ) {
      const selectedOrder = props.selectedSO;
      const isAnnualMaintenanceContract = selectedOrder?.order_type === 'ANNUAL_MAINTENANCE_CONTRACT';
      const docCf =
        isAnnualMaintenanceContract ? props.cfV2DocAnnualMaintenanceContract?.data?.document_custom_fields
          : (!isSalesEstimate && window.location.href.includes('/estimate')
            ? props.cfV2DocSalesEstimate?.data?.document_custom_fields
            : props.cfV2DocSalesOrder?.data?.document_custom_fields);
      // const docLineCf =
      //   !isSalesEstimate && window.location.href.includes("/estimate")
      //     ? props.cfV2DocSalesEstimate?.data?.document_line_custom_fields
      //     : props.cfV2DocSalesOrder?.data?.document_line_custom_fields;
      const docLineCf =
        isAnnualMaintenanceContract ? props?.cfV2DocAnnualMaintenanceContract?.data?.document_line_custom_fields
          : (!isSalesEstimate && window.location.href.includes('/estimate')
            ? props.cfV2DocSalesEstimate?.data?.document_line_custom_fields
            : props.cfV2DocSalesOrder?.data?.document_line_custom_fields);
      const salesOrderLines = selectedOrder?.sales_order_lines?.map(
        (orderLine) => {
          const newLineCf =
            isSalesEstimate || !window.location.href.includes('/estimate')
              ? (selectedOrder?.order_type === 'ANNUAL_MAINTENANCE_CONTRACT' ?
                props.cfV2DocAnnualMaintenanceContract?.data?.document_line_custom_fields
                : props.cfV2DocSalesOrder?.data?.document_line_custom_fields)
              : props.cfV2DocSalesEstimate?.data?.document_line_custom_fields;

          const discountValue = orderLine?.is_discount_in_percent ? Number.parseFloat(Number(orderLine?.line_discount_percentage)?.toFixed(DEFAULT_CUR_ROUND_OFF)) : Number.parseFloat(Number(orderLine?.line_discount_amount)?.toFixed(DEFAULT_CUR_ROUND_OFF));
          const taxableValue = orderLine?.is_discount_in_percent ? (orderLine.quantity * orderLine.unit_price) * (1 - discountValue / 100) : Math.max(orderLine.quantity * orderLine.unit_price - discountValue, 0);
          const unitDiscountValue = Number(discountValue / Number(orderLine?.quantity))?.toPrecision(DEFAULT_CUR_ROUND_OFF);
          return {
            key: uuidv4(),
            product_sku_name: orderLine?.product_sku_info?.product_sku_name,
            product_category_info:
              orderLine?.product_sku_info?.product_category_info,
            product_sku_id: orderLine?.product_sku_info?.product_sku_id,
            product_sku_info: orderLine?.product_sku_info,
            available_qty: orderLine?.product_sku_info?.department_quantity,
            asset1: orderLine?.product_sku_info?.assets?.[0]?.url || '',
            sku: orderLine?.product_sku_info?.sku,
            quantity: Number(orderLine?.quantity),
            unitPrice: Number(orderLine?.unit_price),
            uomId: Number(orderLine?.uom_info?.[0]?.uom_id),
            uomGroup: Number(orderLine?.group_id),
            taxId: Number(orderLine?.tax_id),
            taxInfo: orderLine?.tax_group_info,
            child_taxes: Helpers.computeTaxation(taxableValue, orderLine?.tax_group_info, user?.tenant_info?.state, selectedOrder?.billing_address_info?.state)?.tax_info?.child_taxes,
            remarks: orderLine?.remarks,
            hsn_code: orderLine?.hsn_code,
            remarkRequired: !!orderLine?.remarks?.replace(/<[^>]+>/g, ''),
            discount: discountValue,
            unitDiscount: unitDiscountValue,
            lineDiscountType: orderLine?.is_discount_in_percent ? 'Percent' : 'Amount',
            showUnitDiscount: !orderLine?.is_discount_in_percent && unitDiscountValue > 0,
            product_type: orderLine?.product_sku_info?.product_type,
            showFreebie:
              Number(orderLine?.free_quantity) > 0
                ? Number(orderLine?.free_quantity)
                : null,
            free_quantity: orderLine?.free_quantity || 0,
            lineCustomFields: newLineCf?.length
              ? (props?.match?.url?.includes('/update')) ? CustomFieldHelpers.mergeCustomFields(
                newLineCf || [],
                orderLine?.so_line_custom_fields || []
                , true)?.map((k) => ({
                  ...k,
                  fieldValue:
                    k?.fieldName === 'Rate'
                      ? Number(orderLine.unit_price)
                      : k?.fieldName === 'Quantity'
                        ? Number(orderLine?.quantity)
                        : k?.fieldValue,
                }))
                : CustomFieldHelpers.mergeCustomFields(
                  newLineCf || [],
                  orderLine?.so_line_custom_fields || [])?.map((k) => ({
                    ...k,
                    fieldValue:
                      k?.fieldName === 'Rate'
                        ? Number(orderLine.unit_price)
                        : k?.fieldName === 'Quantity'
                          ? Number(orderLine?.quantity)
                          : k?.fieldValue,
                  })) : [],
            bundle_products: orderLine?.bundle_products?.map((item) => ({
              ...item,
              product_info: item?.product_sku_info,
              product_sku_info: orderLine?.product_sku_info?.product_sku_id,
              available_qty: orderLine?.product_sku_info?.department_quantity,
              uom_info: item?.uom_info?.[0],
              bundle_quantity: item?.quantity / orderLine?.quantity,
              tax_info: item?.tax_group_info,
            })),
            order_line_id: orderLine?.order_line_id,
            tenant_product_id: orderLine?.tenant_product_id,
            uom_info: orderLine?.uom_info?.[0],
            uom_list: orderLine?.product_sku_info?.uom_list,

          };
        }
      );
      // Already Used Custom  Fields
      const oldCustomField = selectedOrder?.custom_fields || [];
      const oldLineCustomField = selectedOrder?.sales_order_lines[0]?.so_line_custom_fields || [];
      const mergedLineCustomFields = (props?.match?.url?.includes('/update')) ? CustomFieldHelpers.mergeCustomFields(docLineCf, oldLineCustomField, true) : CustomFieldHelpers.mergeCustomFields(docLineCf, oldLineCustomField);
      return {
        ...state,
        isUserReady: true,
        productSkuIds:
          selectedOrder?.sales_order_lines?.map(
            (salesOrderLine) => salesOrderLine?.product_sku_info?.product_sku_id
          ) || [],
        charge1Name: selectedOrder?.charge_1_name || 'Freight',
        charge1Value: selectedOrder?.charge_1_value,
        createExpenseCheckbox:
          selectedOrder?.transporter_id || selectedOrder?.vehicle_number,
        transporterId: selectedOrder?.transporter_id,
        vehicleNumber: selectedOrder?.vehicle_number,
        transporterBillNumber: selectedOrder?.transporter_bill_number,
        freightTaxId: selectedOrder?.freight_tax_id || 'Not Applicable',
        freightTax: selectedOrder?.freight_tax_info?.tax_value,
        freightTaxInfo: selectedOrder?.freight_tax_info,
        freightTaxData: {
          ...selectedOrder?.freight_tax_info,
          child_taxes: Helpers.computeTaxation(selectedOrder?.charge_1_value, selectedOrder?.freight_tax_info, user?.tenant_info?.state, selectedOrder?.billing_address_info?.state)?.tax_info?.child_taxes
        },
        selectedPriceList: selectedOrder?.price_list_id,
        selectedCustomer: Number(selectedOrder?.customer_id),
        selectedCustomerInfo: selectedOrder?.customer_info,
        accountManager: selectedOrder?.account_manager_id,
        gstNumber: selectedOrder?.customer_info?.gst_number,
        orderDate: props?.location?.state?.cloneSo
          ? dayjs()
          : dayjs(selectedOrder?.order_date),
        estimateExpiryDate: props?.location?.state?.cloneSo
          ? dayjs()
          : selectedOrder?.estimate_expiry_date
            ? dayjs(selectedOrder?.estimate_expiry_date)
            : '',
        deliveryDate: props?.location?.state?.cloneSo
          ? dayjs()
          : selectedOrder?.delivery_date
            ? dayjs(selectedOrder?.delivery_date)
            : '',
        billingAddress: selectedOrder?.billing_address_info,
        shippingAddress: selectedOrder?.shipping_address_info,
        data: salesOrderLines,
        paymentTerms: Number(selectedOrder?.payment_terms?.[0]?.due_days),
        termsAndConditions: selectedOrder?.terms_and_conditions,
        tncId: selectedOrder?.tc_id,
        soNumber: selectedOrder?.sales_order_number,
        fileList: selectedOrder?.attachments,
        selectedOrder,
        tenantDepartmentId: selectedOrder?.tenant_department_id,
        selectedTenant: selectedOrder?.tenant_id,
        remark: selectedOrder?.payment_terms?.[0]?.remark || '',
        discountPercentage: selectedOrder?.is_discount_in_percent ? selectedOrder?.discount_percentage?.toFixed(DEFAULT_CUR_ROUND_OFF) : selectedOrder?.discount_amount?.toFixed(DEFAULT_CUR_ROUND_OFF),
        discountType: selectedOrder?.is_discount_in_percent ? 'Percent' : 'Amount',
        isLineWiseDiscount: selectedOrder?.is_line_wise_discount,
        chargeData:
          selectedOrder?.other_charges?.map((item) => ({
            ...item,
            chargeKey: uuidv4(),
            chargesTaxId: item?.tax_info?.tax_id,
            chargesSacCode: item?.charge_sac_code,
            chargesTaxInfo: item?.tax_info,
            chargesTax: item?.tax_info?.tax_value,
            chargesTaxData: {
              ...item?.tax_info,
              child_taxes: Helpers.computeTaxation(item?.charge_amount, item?.tax_info, user?.tenant_info?.state, selectedOrder?.billing_address_info?.state)?.tax_info?.child_taxes
            },
          })) || [],
        selectedCurrencyID: selectedOrder?.org_currency_info?.org_currency_id,
        selectedCurrencyName: selectedOrder?.org_currency_info,
        sendWhatsappNotification: selectedOrder?.is_so_automatic_notification_enabled,
        cfSalesOrderDoc: docCf?.length ? (props?.match?.url?.includes('/update')) ? CustomFieldHelpers.mergeCustomFields(docCf, oldCustomField, true) : CustomFieldHelpers.mergeCustomFields(docCf, oldCustomField) : [],
        cfSalesOrderLine: docLineCf?.length ? mergedLineCustomFields : [],
        visibleColumns: oldLineCustomField?.length ? CustomFieldHelpers.updateVisibleColumns(CustomFieldHelpers.postCfStructure(mergedLineCustomFields), state.visibleColumns) : state.visibleColumns,
        selectedTags: selectedOrder?.tags,
        freightSacCode: selectedOrder?.freight_sac_code,
        isAutomaticConversionRate: false,
        currencyConversionRate: selectedOrder?.conversion_rate,
        salesOrderType: isSalesEstimate ? 'SALES_ORDER' : selectedOrder?.order_type,
      };
    }
    if (
      !isSalesEstimate && window.location.href.includes('/estimate')
        ? user?.tenant_info?.sales_config?.sub_modules?.sales_estimate?.settings
          ?.restrict_estimate_of_other_acc_manager
        : user?.tenant_info?.sales_config?.sub_modules?.sales_order?.settings
          ?.restrict_orders_of_other_acc_manager
    ) {
      return {
        ...state,
        accountManager: user?.user_id,
      };
    }

    return {
      ...state,
    };
  }

  handleChangeTextArea = (html) => {
    if (this.state.termsAndConditions !== html) {
      this.setState({ termsAndConditions: html });
    }
  };

  handleDelete = (key) => {
    const { data, cfSalesOrderLine } = this.state;
    if (data?.length === 1) {
      this.setState({
        data: [
          {
            key: uuidv4(),
            asset1: '',
            product_name: '',
            quantity: '',
            unitPrice: '',
            lot: 0,
            taxId: '',
            discount: 0,
            child_taxes: [
              {
                tax_amount: 0,
                tax_type_name: '',
              },
            ],
            lineCustomFields: cfSalesOrderLine,
          },
        ],
      });
    } else {
      const copyData = data.filter((item) => item.key !== key);
      this.setState({ data: copyData }, () => {
        if (!this.state.isLineWiseDiscount) this.handleDiscountPercentageChange(this.state.discountPercentage);
      });
    }
  };

  handleDeleteCharge = (chargeKey) => {
    const { chargeData } = this.state;
    const copyChargeData = chargeData.filter(
      (item) => item.chargeKey !== chargeKey
    );
    this.setState({ chargeData: copyChargeData });
  };

  getLineTotals() {
    const { data, chargeData, charge1Value, freightTax } = this.state;
    let totalAmount = 0;
    let totalDiscount = 0;
    let totalBase = 0;
    let totalTax = 0;
    let totalOtherCharge = 0;
    let orderTotal = 0;
    let freightCharge = Number(charge1Value);
    let totalOtherCharges = 0;

    // Taxes Bifurcation
    const totalTaxValue = Helpers.groupAndSumByTaxName(
      data?.map((item) => item?.child_taxes)?.flat()
    )?.reduce((acc, curr) => acc + curr?.tax_amount, 0);

    for (let i = 0; i < chargeData?.length; i++) {
      let currentOtherCharge = 0;
      let chargesTaxAmountLinesWise = 0;
      if (chargeData[i]?.charge_amount) {
        chargesTaxAmountLinesWise = Helpers.groupAndSumByTaxName([...chargeData[i]?.chargesTaxData?.child_taxes?.flat()])?.reduce((acc, curr) => acc + curr?.tax_amount, 0);
        currentOtherCharge = chargeData?.[i]?.chargesTax ? chargeData?.[i]?.charge_amount + (chargesTaxAmountLinesWise) : chargeData?.[i]?.charge_amount;
        if (chargeData?.[i]?.chargesTax) {
          totalOtherCharges += Number(chargeData?.[i]?.charge_amount);
        }
      }
      totalOtherCharge += currentOtherCharge;
    }

    for (let i = 0; i < data?.length; i++) {
      let currentAmount = 0;
      let currentDiscount = 0;
      let currentBase = 0;
      let currentTax = 0;
      let currentOrder = 0;
      if (data[i].quantity) {
        const discountValue = Number(data[i].discount) || 0;

        currentAmount += data[i].quantity * data[i].unitPrice;
        currentDiscount += data[i].discount
          ? (data[i]?.lineDiscountType === 'Percent' ? (data[i].quantity * data[i].unitPrice * (data[i].discount / 100)) : discountValue)
          : 0;
        if (data[i].discount) {
          if (data[i]?.lineDiscountType === 'Percent') {
            currentBase
              += data[i].quantity
              * data[i].unitPrice
              * (data[i].discount ? (Number(100 - data[i].discount) / 100) : 1);
          } else if (data[i]?.lineDiscountType === 'Amount') {
            currentBase += (data[i].quantity
              * data[i].unitPrice) - discountValue;
          }
        } else {
          currentBase
            += data[i].quantity
            * data[i].unitPrice;
        }
      }

      if (data[i].taxId && data[i].taxInfo?.tax_value) {
        currentTax +=
          currentBase *
          (data?.[i]?.taxId ? data[i].taxInfo?.tax_value / 100 : 0);
      }
      if (currentBase) currentOrder = currentBase;

      totalAmount += currentAmount;
      totalDiscount += currentDiscount;
      totalBase += currentBase;
      totalTax += currentTax;
      orderTotal += currentOrder;
    }

    if (freightTax) {
      totalBase += freightCharge;
      freightCharge *= 1 + freightTax / 100;
    }

    orderTotal += totalOtherCharge;
    orderTotal += freightCharge;
    orderTotal += totalTaxValue;
    totalBase += totalOtherCharges;
    return {
      totalAmount,
      totalDiscount,
      totalBase,
      totalTax,
      orderTotal,
    };
  }

  showNewCustomerInSelect = (value) => {
    this.setState({
      selectedCustomer: value?.customer_id,
      billingAddress: value?.customer_info?.billing_address_details,
      shippingAddress: value?.customer_info?.shipping_address_details,
      gstNumber: value?.customer_info?.gst_number,
      selectedCustomerInfo: value?.customer_info,
    });
  };

  handleProductChange = (tenantSku, key, productData, isMultiMode, callback) => {
    const {
      data,
      tenantDepartmentId,
      isLineWiseDiscount,
      discountPercentage,
      selectedCustomer,
      shippingAddress,
      selectedPriceList,
      pricesToApply,
      billingAddress,
      cfSalesOrderLine,
      selectedTenant,
    } = this.state;
    const {
      getProductById, user, getApplyPriceLists, selectedSO, location,
    } = this.props;
    const autoPrintDescription = user?.tenant_info?.global_config?.settings?.print_description_automatically;
    // const selectedTenantIdSo = location?.state?.selectedTenantIdSo;
    if (isMultiMode && Array.isArray(tenantSku)) {
      const productSkuIds = tenantSku?.map((item) => item?.product_info?.product_sku_id)?.join(',');
      getProductById(
        selectedTenant,
        '',
        tenantDepartmentId,
        (products) => {
          if (selectedPriceList) {
            const newProductSkuIds = products?.map((item) => item?.product_info?.product_sku_id)?.join(',');
            getApplyPriceLists(
              selectedTenant || selectedSO?.tenant_id,
              selectedPriceList,
              selectedCustomer,
              shippingAddress?.state,
              newProductSkuIds,
              (toApplyPriceList) => {
                const copyData = [];
                const copyToApplyPriceList = [];
                products.forEach((product) => {
                  if (
                    product?.product_type === 'BUNDLE' && product?.bundle_products?.length === 0
                  ) {
                    notification.open({
                      message:
                        `${product?.internal_sku_code} Product is a bundle but it does not have any child components.`,
                      duration: 4,
                      type: 'error',
                      placement: 'top',
                    });
                  } else {
                    const tenantSkuData = tenantSku?.find((item) => item?.product_info?.product_sku_id === product?.product_info?.product_sku_id);
                    const productSkuData = productData?.find((item) => item?.product_sku_id === product?.product_info?.product_sku_id);
                    const currentToApplyPriceList = toApplyPriceList?.find((item) => item?.product_sku_id === product?.product_info?.product_sku_id);

                    const newDataLine = {
                      key: uuidv4(),
                      child_taxes: [
                        {
                          tax_amount: 0,
                          tax_type_name: '',
                        },
                      ],
                      lineCustomFields: cfSalesOrderLine,
                    };
                    const copyDataItem = this.createDefaultRow({
                      dataLine: newDataLine,
                      product,
                      tenantSku: tenantSkuData,
                      productData: productSkuData,
                      toApplyPriceList: currentToApplyPriceList,
                      isLineWiseDiscount,
                      discountPercentage,
                      autoPrintDescription,
                      cfSalesOrderLine,
                      user,
                      billingAddress,
                    });

                    copyData.push(copyDataItem);
                    copyToApplyPriceList.push(currentToApplyPriceList);
                  }
                });

                if (callback) {
                  callback();
                }

                this.setState((prevState) => ({
                  data: [...prevState.data, ...copyData],
                  productSkuIds: [...prevState.productSkuIds, ...copyData?.map((productSkuId) => productSkuId?.product_sku_id)],
                  pricesToApply: [...prevState.pricesToApply, ...copyToApplyPriceList],
                }));
              },
              true,
            );
          } else {
            const copyData = [];
            products.forEach((product) => {
              if (
                product?.product_type === 'BUNDLE' && product?.bundle_products?.length === 0
              ) {
                notification.open({
                  message:
                    `${product?.internal_sku_code} Product is a bundle but it does not have any child components.`,
                  duration: 4,
                  type: 'error',
                  placement: 'top',
                });
              } else {
                const tenantSkuData = tenantSku?.find((item) => item?.product_info?.product_sku_id === product?.product_info?.product_sku_id);
                const productSkuData = productData?.find((item) => item?.product_sku_id === product?.product_info?.product_sku_id);
                const newDataLine = {
                  key: uuidv4(),
                  child_taxes: [
                    {
                      tax_amount: 0,
                      tax_type_name: '',
                    },
                  ],
                  lineCustomFields: cfSalesOrderLine,
                };
                const copyDataItem = this.createDefaultRow({
                  dataLine: newDataLine,
                  product,
                  tenantSku: tenantSkuData,
                  productData: productSkuData,
                  // toApplyPriceList: '',
                  isLineWiseDiscount,
                  discountPercentage,
                  autoPrintDescription,
                  cfSalesOrderLine,
                  user,
                  billingAddress,
                });

                copyData.push(copyDataItem);
              }
            });

            if (callback) {
              callback();
            }

            this.setState((prevState) => ({
              data: [...prevState.data, ...copyData],
              productSkuIds: [...prevState.productSkuIds, ...copyData?.map((productSkuId) => productSkuId?.product_sku_id)],
            }));
          }
        },
        true,
        productSkuIds,
      );
    } else {
      getProductById(
        selectedTenant,
        tenantSku?.product_sku_id,
        tenantDepartmentId,
        (product) => {
          if (
            product?.product_type === 'BUNDLE' && product?.bundle_products?.length === 0
          ) {
            notification.open({
              message:
                `${product?.internal_sku_code} Product is a bundle but it does not have any child components.`,
              duration: 4,
              type: 'error',
              placement: 'top',
            });
          } else if (selectedPriceList) {
            getApplyPriceLists(
              selectedTenant || selectedSO?.tenant_id,
              selectedPriceList,
              selectedCustomer,
              shippingAddress?.state,
              tenantSku?.product_sku_id,
              (toApplyPriceList) => {
                const copyData = JSON.parse(JSON.stringify(data));
                for (let i = 0; i < copyData?.length; i++) {
                  if (copyData[i].key === key) {
                    const copyDataItem = this.createDefaultRow({
                      dataLine: copyData?.[i],
                      product,
                      tenantSku,
                      productData,
                      toApplyPriceList: toApplyPriceList[0],
                      isLineWiseDiscount,
                      discountPercentage,
                      autoPrintDescription,
                      cfSalesOrderLine,
                      user,
                      billingAddress,
                    });
                    copyData[i] = copyDataItem;
                  }
                }
                this.setState({
                  data: copyData,
                  productSkuIds: copyData?.map((productSkuId) => productSkuId?.product_sku_id),
                  pricesToApply: [...pricesToApply, ...toApplyPriceList],
                }, () => {
                  if (!isLineWiseDiscount) this.handleDiscountPercentageChange(discountPercentage);
                });
              },
            );
          } else {
            const copyData = JSON.parse(JSON.stringify(data));
            for (let i = 0; i < copyData?.length; i++) {
              if (copyData[i].key === key) {
                const copyDataItem = this.createDefaultRow({
                  dataLine: copyData[i],
                  product,
                  tenantSku,
                  productData,
                  isLineWiseDiscount,
                  discountPercentage,
                  autoPrintDescription,
                  cfSalesOrderLine,
                  user,
                  billingAddress,
                });
                copyData[i] = copyDataItem;
              }
            }
            this.setState({
              data: copyData,
              productSkuIds: copyData?.map((productSkuId) => productSkuId?.product_sku_id),
            }, () => {
              if (!isLineWiseDiscount) this.handleDiscountPercentageChange(discountPercentage);
            });
          }
        },
        true,
      );
    }
  };

  createDefaultRow = ({
    dataLine, product, tenantSku, productData, toApplyPriceList, isLineWiseDiscount, discountPercentage, autoPrintDescription, cfSalesOrderLine, user, billingAddress,
  }) => {
    const copyDataItem = dataLine;
    copyDataItem.tenant_product_id = tenantSku?.tenant_product_id;
    copyDataItem.product_category_info = tenantSku?.product_category_info;
    copyDataItem.po_line_id = tenantSku?.po_line_id;
    copyDataItem.product_type = tenantSku?.product_type;
    copyDataItem.product_sku_id = tenantSku?.product_info?.product_sku_id;
    copyDataItem.available_qty = tenantSku?.available_qty;
    copyDataItem.product_sku_info = tenantSku?.product_info;
    copyDataItem.product_sku_name = tenantSku?.product_info?.product_sku_name;
    copyDataItem.asset1 = tenantSku?.product_info?.assets?.[0]?.url || '';
    copyDataItem.quantity = '1';
    copyDataItem.bundle_products = product?.bundle_products;
    copyDataItem.unitPrice = tenantSku?.selling_price || 0;
    copyDataItem.default_selling_price = tenantSku?.selling_price || 0;
    copyDataItem.sku = tenantSku?.product_info?.product_sku_id || '';
    copyDataItem.taxInfo = tenantSku?.tax_info;
    copyDataItem.child_taxes = Helpers.computeTaxation(
      1 * tenantSku?.selling_price,
      tenantSku?.tax_info,
      user?.tenant_info?.state,
      billingAddress?.state,
    )?.tax_info?.child_taxes;
    copyDataItem.taxId = tenantSku?.product_info?.tax_id;
    copyDataItem.lot = tenantSku?.total_price || 0;
    copyDataItem.uomId = tenantSku?.product_info?.uom_id || 0;
    copyDataItem.uom_info = tenantSku?.uom_info;
    copyDataItem.uom_list = tenantSku?.uom_list;
    copyDataItem.uomGroup = tenantSku?.group_id;
    copyDataItem.discount = !isLineWiseDiscount ? discountPercentage : 0;
    copyDataItem.hsn_code = tenantSku?.product_info?.hsn_code || '';
    copyDataItem.remarks = autoPrintDescription ? tenantSku?.product_info?.description || '' : '';
    copyDataItem.lineDiscountType = 'Percent';
    copyDataItem.remarkRequired = autoPrintDescription && tenantSku?.product_info?.description?.replace(/<[^>]+>/g, '');
    const cfToBeMapped = [
      ...(product?.custom_fields ?? []),
      ...(productData?.system_custom_fields ?? []),
    ];
    copyDataItem.lineCustomFields = FormHelpers.lineSystemFieldValue(
      cfSalesOrderLine,
      cfToBeMapped,
    )?.map((k) => ({
      ...k,
      fieldValue: k?.fieldName === 'Rate'
        ? Number(copyDataItem.unitPrice)
        : k?.fieldName === 'Quantity'
          ? 1
          : k?.fieldValue,
    }));

    if (
      toApplyPriceList && toApplyPriceList?.product_sku_id === tenantSku?.product_info.product_sku_id
      && copyDataItem !== undefined
    ) {
      copyDataItem.unitPrice = toApplyPriceList?.price_list_amount == 0
        ? tenantSku?.selling_price
        : toApplyPriceList?.is_inclusive_of_tax
          ? toApplyPriceList?.price_list_amount
          / ((100 + tenantSku?.product_info?.tax_value) / 100)
          : toApplyPriceList?.price_list_amount;
      copyDataItem.discount = toApplyPriceList?.discount_percentage;
    }
    return copyDataItem;
  };

  handleProductChangeValue = (value, key) => {
    const { data, isLineWiseDiscount, discountPercentage, } = this.state;
    const copyData = JSON.parse(JSON.stringify(data));
    for (let i = 0; i < copyData?.length; i++) {
      if (copyData[i].key === key) {
        const copyDataItem = copyData[i];
        copyDataItem.product_sku_name = value;
        copyData[i] = copyDataItem;
      }
    }
    this.setState({ data: copyData });
  };

  handleMultiProductChange = (tenantSku, key, productData, callback) => {
    this.handleProductChange(tenantSku, key, productData, true, callback);
  }

  addNewRow(callback) {
    const { data, cfSalesOrderLine } = this.state;
    const copyData = JSON.parse(JSON.stringify(data));
    const key = uuidv4();
    copyData.push({
      key,
      asset1: '',
      product_sku_name: '',
      quantity: '',
      unitPrice: '',
      taxId: '',
      lot: '',
      child_taxes: [
        {
          tax_amount: 0,
          tax_type_name: '',
        },
      ],
      lineCustomFields: cfSalesOrderLine,
    });
    this.setState({ data: copyData }, () => {
      if (callback) {
        callback(key);
      }
    });
  }

  addNewChargesRow() {
    const { chargeData } = this.state;
    const copychargeData = JSON.parse(JSON.stringify(chargeData));
    copychargeData.push({
      chargeKey: uuidv4(),
      charge_name: '',
      charge_amount: 0,
      chargesTaxData: {
        child_taxes: [
          {
            tax_amount: 0,
            tax_type_name: '',
          },
        ],
      },
    });
    this.setState({ chargeData: copychargeData });
  }

  isDataValid() {
    const { data } = this.state;
    let isDataValid = true;
    for (let i = 0; i < data?.length; i++) {
      if (
        Number(data[i].quantity) <= 0 ||
        !data[i].product_sku_name ||
        Number(data[i]?.unitPrice) <= 0 ||
        !JSON.stringify(data[i].taxId) ||
        !CustomFieldHelpers.isCfValid(data[i]?.lineCustomFields)
      ) {
        isDataValid = false;
      }
    }
    return isDataValid;
  }

  isDataValid2() {
    const { chargeData } = this.state;
    let isDataValid = true;
    if (chargeData?.length) {
      for (let i = 0; i < chargeData?.length; i++) {
        if (!chargeData[i].charge_name || !chargeData[i].charge_amount) {
          isDataValid = false;
        }
      }
    }
    return isDataValid;
  }

  getSOStatus(withApproval) {
    const { orgStatusSalesOrder } = this.props;
    const status = {};

    if (withApproval) {
      const requiredStatus = orgStatusSalesOrder?.statuses?.filter(
        (soStatus) =>
          soStatus?.type === 'CONFIRMED' && soStatus?.is_default === true
      )?.[0];
      status.custom_status_id = requiredStatus?.custom_status_id;
      status.secondary_status = requiredStatus?.status_name;
    } else {
      const requiredStatus = orgStatusSalesOrder?.statuses?.filter(
        (soStatus) =>
          soStatus?.type === 'DRAFT' && soStatus?.is_default === true
      )?.[0];
      status.custom_status_id = requiredStatus?.custom_status_id;
      status.secondary_status = requiredStatus?.status_name;
    }
    return status;
  }

  getSalesErrors() {
    const {
      selectedTenant,
      tenantDepartmentId,
      documentUINames,
      orderDate,
      isEstimate,
      deliveryDate,
      selectedCustomer,
      selectedPriceList,
      cfSalesOrderDoc,
      shippingAddress,
      billingAddress,
      data,
      chargeData,
      gstNumber,
    } = this.state;
    const { user } = this.props;

    let { docLevelError, lineLevelError } = salesErrors({
      selectedTenant,
      tenantDepartmentId,
      documentUINames,
      orderDate,
      isEstimate,
      deliveryDate,
      selectedCustomer,
      selectedPriceList,
      cfSalesOrderDoc,
      shippingAddress,
      billingAddress,
      user,
      data,
      chargeData,
      gstNumber,
    });

    return {
      docLevelError: docLevelError ?? [],
      lineLevelError: lineLevelError ?? [],
    };
  }

  createSalesOrder(withApproval) {
    this.setState({ withApproval });
    const {
      createSO,
      history,
      user,
      match,
      updateSO,
      location,
      createTag,
      estimateId,
      isSalesEstimate,
    } = this.props;
    const {
      deliveryDate,
      orderDate,
      data,
      shippingAddress,
      paymentTerms,
      billingAddress,
      selectedCustomer,
      selectedOrder,
      termsAndConditions,
      discountPercentage,
      isLineWiseDiscount,
      selectedCustomerInfo,
      gstNumber,
      fileList,
      cfSalesOrderDoc,
      tenantDepartmentId,
      remark,
      chargeData,
      selectedPriceList,
      accountManager,
      selectedCurrencyName,
      sendWhatsappNotification,
      charge1Name,
      charge1Value,
      soNumber,
      customVoucherNumber,
      selectedTags,
      vehicleNumber,
      transporterId,
      freightTaxId,
      freightTaxInfo,
      transporterBillNumber,
      freightSacCode,
      isEstimate,
      salesOrderType,
      estimateExpiryDate,
      selectedTenant,
      currentSelectedTenantInfo,
      currencyConversionRate,
      updateDocumentReason,
      discountType,
      initialSoNumber,
      docSeqId,
      tncId
    } = this.state;
    const selectedTenantIdSo = location?.state?.selectedTenantIdSo;
    const salesOrderLines = [];
    this.setState({
      formSubmitted: true,
      data: JSON.parse(JSON.stringify(data)),
    });

    let toPush = salesOrderType === 'ANNUAL_MAINTENANCE_CONTRACT' ? 'amc-sales-order' : 'sales-order';
    let permissionEntity = Helpers.permissionEntities.SALES_ORDER;
    if (isEstimate) {
      toPush = 'estimate';
      permissionEntity = Helpers.permissionEntities.SALES_ESTIMATE;
    }
    let hasShippingAddress = shippingAddress;
    if (
      isEstimate &&
      !user?.tenant_info?.sales_config?.sub_modules?.sales_estimate?.settings
        ?.is_shipping_address_mandatory
    ) {
      hasShippingAddress = true;
    }

    let hasBillingAddress = billingAddress;
    if (
      isEstimate &&
      !user?.tenant_info?.sales_config?.sub_modules?.sales_estimate?.settings
        ?.is_billing_address_mandatory
    ) {
      hasBillingAddress = true;
    }
    let isPriceListMandatory = user?.tenant_info?.sales_config?.sub_modules?.sales_order?.settings?.is_price_list_mandatory;


    const { docLevelError, lineLevelError } = this.getSalesErrors();
    if (!docLevelError?.length && !lineLevelError?.length) {
      if (
        this.isDataValid() &&
        this.isDataValid2() &&
        selectedCustomer &&
        orderDate &&
        hasShippingAddress &&
        CustomFieldHelpers.isCfValid(cfSalesOrderDoc) &&
        hasBillingAddress &&
        (gstNumber ? String(gstNumber)?.length === 15 : true) &&
        (isEstimate || !isPriceListMandatory || selectedPriceList)
      ) {
        if (match?.params?.orderId && !isSalesEstimate) {
          for (let i = 0; i < data?.length; i++) {
            let lineDiscount = data?.[i]?.lineDiscountType === 'Percent' ? Number(data?.[i]?.discount || 0) : ((Number(data?.[i]?.discount || 0) / (Number(data?.[i].quantity) * parseFloat(data?.[i].unitPrice))) * 100);
            const soLine = {
              order_line_id: data[i]?.order_line_id,
              tenant_product_id: data[i].tenant_product_id,
              product_sku_name: data[i].product_sku_name,
              quantity: Number(data[i].quantity),
              line_discount_amount: (data?.[i]?.lineDiscountType === 'Percent') ? (data?.[i].discount ? data?.[i].quantity * data?.[i].unitPrice * (data?.[i].discount / 100) : 0) : data?.[i]?.discount,
              remarks: data[i]?.remarks,
              hsn_code: data[i]?.hsn_code?.toString(),
              uom_id: data[i]?.uomId,
              unit_price: parseFloat(data[i].unitPrice),
              offer_price: parseFloat(data[i].unitPrice),
              tax_id: data[i]?.taxId,
              tax_group_info: data[i]?.taxInfo,
              uom_info: [data[i]?.uom_info],
              line_discount_percentage: lineDiscount,
              is_discount_in_percent: data?.[i]?.lineDiscountType === 'Percent',
              free_quantity: Number(data[i]?.free_quantity) || 0,
              custom_fields: CustomFieldHelpers.postCfStructure(
                data[i]?.lineCustomFields
              ),
              bundle_products: data[i]?.bundle_products?.map((item) => ({
                ...item,
                offer_price: 0,
                unit_price: 0,
                line_discount_percentage: 0,
                tax_id: item?.tax_info?.tax_id,
                tax_group_info: item?.tax_info,
                uom_id: item?.uom_info?.uom_id,
                uom_info: [item?.uom_info],
                product_sku_name: item.product_info?.product_sku_name,
                quantity:
                  parseFloat(item?.bundle_quantity || '0') *
                  (parseFloat(data[i]?.quantity || '0') +
                    parseFloat(data[i]?.free_quantity || '0')),
              })),
            };
            salesOrderLines.push(soLine);
          }
          const payload = [
            {
              order_id: selectedOrder?.order_id,
              customer_id: selectedCustomer,
              account_manager_id: accountManager || null,
              customer_info: { ...selectedCustomerInfo, gst_number: gstNumber },
              tenant_id: selectedOrder?.tenant_id,
              order_date: dayjs(orderDate).format('YYYY/MM/DD'),
              estimate_expiry_date: estimateExpiryDate
                ? dayjs(estimateExpiryDate).format('YYYY/MM/DD')
                : null,
              delivery_date: deliveryDate
                ? dayjs(deliveryDate).format('YYYY/MM/DD')
                : null,
              customer_po_date: deliveryDate
                ? dayjs(deliveryDate).format('YYYY/MM/DD')
                : null,
              // sales_order_number: soNumber,
              payment_terms: [
                {
                  advance_amount: 0,
                  due_days: paymentTerms,
                  remark,
                },
              ],
              price_list_id: selectedPriceList || null,
              shipping_address: shippingAddress?.address_id,
              shipping_address_info: shippingAddress,
              billing_address: billingAddress?.address_id,
              billing_address_info: billingAddress,
              sales_order_lines: salesOrderLines,
              tenant_info: {
                ...selectedOrder?.tenant_info,
                tenant_name: selectedOrder
                  ? selectedOrder?.tenant_info?.tenant_name
                  : user?.tenant_info?.tenant_name,
                legal_name: selectedOrder
                  ? selectedOrder?.tenant_info?.legal_name
                  : user?.tenant_info?.legal_name,
                gst_number: selectedOrder
                  ? selectedOrder?.tenant_info?.gst_number
                  : user?.tenant_info?.gst_number,
              },
              attachments:
                fileList?.map((attachment) => ({
                  url:
                    attachment?.response?.response?.location || attachment?.url,
                  type: attachment.type,
                  name: attachment.name,
                  uid: attachment.uid,
                })) || [],
              terms_and_conditions: global.isBlankString(termsAndConditions)
                ? ''
                : termsAndConditions,
              tc_id: tncId || null,
              status: withApproval ? 'CONFIRMED' : 'DRAFT',
              custom_fields:
                CustomFieldHelpers.postCfStructure(cfSalesOrderDoc),
              tenant_department_id: tenantDepartmentId,
              other_charges:
                chargeData?.map((charge) => ({
                  charge_name: charge?.charge_name,
                  charge_amount: charge?.charge_amount,
                  charge_type: '',
                  charge_sac_code: charge?.chargesSacCode || null,
                  tax_info: charge?.chargesTaxInfo || null,
                  ledger_name: charge?.ledger_name || null,
                })) || [],
              discount_percentage: discountType === 'Percent' ? discountPercentage : (discountPercentage / this.getLineTotals().totalAmount || 0) * 100,
              is_line_wise_discount: isLineWiseDiscount,
              secondary_status:
                this.getSOStatus(withApproval)?.secondary_status,
              custom_status_id:
                this.getSOStatus(withApproval)?.custom_status_id,
              conversion_rate:
                currencyConversionRate || selectedCurrencyName?.conversion_rate,
              org_currency_id: selectedCurrencyName?.org_currency_id,
              charge_1_name: charge1Name,
              charge_1_value: Number(charge1Value),
              transporter_id: transporterId,
              vehicle_number: vehicleNumber,
              freight_tax_id:
                freightTaxId === 'Not Applicable' ? null : freightTaxId,
              freight_tax_info: freightTaxInfo,
              transporter_bill_number: transporterBillNumber,
              is_so_automatic_notification_enabled: sendWhatsappNotification,
              freight_sac_code: freightSacCode,
              order_type: salesOrderType,
              update_document_reason: updateDocumentReason,
              is_discount_in_percent: discountType === 'Percent',
              sales_order_number: initialSoNumber?.toLowerCase()?.trim() === soNumber?.toLowerCase()?.trim() ? null : soNumber,
            },
          ];
          // return
          updateSO(payload, () => {
            if (selectedTags?.length) {
              const tagPayload = {
                entity_name: 'SALES_ORDER',
                entity_id: selectedOrder?.order_id,
                action: 'REPLACE',
                tag: selectedTags.join(','),
              };
              createTag(tagPayload, () => {
                history.push(
                  `/sales/${toPush}/view/${selectedOrder?.order_id}`
                );
              });
            } else {
              history.push(
                `/sales/${toPush}/view/${selectedOrder?.order_id
                }/${Helpers.getTenantEntityPermission(
                  user?.user_tenants,
                  permissionEntity,
                  Helpers.permissionTypes.UPDATE
                ).join(',')}`
              );
            }
            this.setState({ currentAction: '' });
          });
        } else {
          for (let i = 0; i < data?.length; i++) {
            let lineDiscount = data[i]?.lineDiscountType === 'Percent' ? Number(data?.[i]?.discount || 0) : ((Number(data?.[i]?.discount || 0) / (Number(data[i].quantity) * parseFloat(data[i].unitPrice))) * 100);
            const soLine = {
              tenant_product_id: data[i].tenant_product_id,
              product_sku_name: data[i].product_sku_name,
              quantity: Number(data[i].quantity),
              free_quantity: Number(data[i].free_quantity) || 0,
              uom_id: data[i]?.uomId,
              unit_price: parseFloat(data[i].unitPrice),
              remarks: data[i]?.remarks,
              hsn_code: data[i]?.hsn_code?.toString(),
              tax_id: data[i]?.taxId,
              tax_group_info: data[i]?.taxInfo,
              uom_info: [data[i]?.uom_info],
              offer_price: parseFloat(data[i].unitPrice),
              line_discount_percentage: lineDiscount,
              is_discount_in_percent: data?.[i]?.lineDiscountType === 'Percent',
              custom_fields: CustomFieldHelpers.postCfStructure(
                data[i]?.lineCustomFields
              ),
              bundle_products: data[i]?.bundle_products?.map((item) => {
                const { order_id, order_line_id, ...rest } = item;
                return {
                  ...rest,
                  offer_price: 0,
                  unit_price: 0,
                  line_discount_percentage: 0,
                  tax_id: item?.tax_info?.tax_id,
                  tax_group_info: item?.tax_info,
                  uom_id: item?.uom_info?.uom_id,
                  uom_info: [item?.uom_info],
                  product_sku_name: item.product_info?.product_sku_name,
                  quantity:
                    parseFloat(item?.bundle_quantity || '0') *
                    (Number(data[i]?.quantity || '0') +
                      Number(data[i].free_quantity || '0')),
                };
              }),
            };
            salesOrderLines.push(soLine);
          }
          const departmentLevelStock =
            user?.tenant_info?.inventory_config?.settings
              ?.department_level_stock;
          const payload = [
            {
              sales_estimate_id: isSalesEstimate ? estimateId : null,
              customer_id: selectedCustomer,
              account_manager_id: accountManager || null,
              customer_info: { ...selectedCustomerInfo, gst_number: gstNumber },
              tenant_id: selectedTenant || selectedTenantIdSo,
              order_date: dayjs(orderDate).format('YYYY/MM/DD'),
              estimate_expiry_date: estimateExpiryDate
                ? dayjs(estimateExpiryDate).format('YYYY/MM/DD')
                : null,
              delivery_date: deliveryDate
                ? dayjs(deliveryDate).format('YYYY/MM/DD')
                : null,
              customer_po_date: deliveryDate
                ? dayjs(deliveryDate).format('YYYY/MM/DD')
                : null,
              // custom_voucher_number:
              //   soNumber === customVoucherNumber ? "" : soNumber,
              payment_terms: [
                {
                  advance_amount: 0,
                  due_days: paymentTerms,
                  remark,
                },
              ],
              price_list_id: selectedPriceList || null,
              shipping_address: shippingAddress?.address_id,
              shipping_address_info: shippingAddress,
              billing_address: billingAddress?.address_id,
              billing_address_info: billingAddress,
              sales_order_lines: salesOrderLines,
              tenant_info: {
                tenant_name:
                  currentSelectedTenantInfo?.tenant_name ||
                  selectedOrder?.tenant_info?.tenant_name ||
                  user?.tenant_info?.tenant_name,
                legal_name:
                  currentSelectedTenantInfo?.legal_name ||
                  selectedOrder?.tenant_info?.legal_name ||
                  user?.tenant_info?.legal_name,
                gst_number:
                  currentSelectedTenantInfo?.gst_number ||
                  selectedOrder?.tenant_info?.gst_number ||
                  user?.tenant_info?.gst_number,
              },
              attachments:
                fileList?.map((attachment) => ({
                  url:
                    attachment?.response?.response?.location || attachment?.url,
                  type: attachment.type,
                  name: attachment.name,
                  uid: attachment.uid,
                })) || [],
              status: withApproval ? 'CONFIRMED' : 'DRAFT',
              terms_and_conditions: global.isBlankString(termsAndConditions)
                ? ''
                : termsAndConditions,
              tnc_id: tncId || null,
              custom_fields:
                CustomFieldHelpers.postCfStructure(cfSalesOrderDoc),
              tenant_department_id: this.props?.location?.state?.cloneSo
                ? tenantDepartmentId
                : !departmentLevelStock
                  ? user?.tenant_info?.default_store_id
                  : tenantDepartmentId,
              other_charges:
                chargeData?.map((charge) => ({
                  charge_name: charge?.charge_name,
                  charge_amount: charge?.charge_amount,
                  charge_type: '',
                  charge_sac_code: charge?.chargesSacCode || null,
                  tax_info: charge?.chargesTaxInfo || null,
                  ledger_name: charge?.ledger_name || null,
                })) || [],
              discount_percentage: discountType === 'Percent' ? discountPercentage : (discountPercentage / this.getLineTotals().totalAmount || 0) * 100,
              is_line_wise_discount: isLineWiseDiscount,
              secondary_status:
                this.getSOStatus(withApproval)?.secondary_status,
              custom_status_id:
                this.getSOStatus(withApproval)?.custom_status_id,
              conversion_rate:
                currencyConversionRate || selectedCurrencyName?.conversion_rate,
              org_currency_id: selectedCurrencyName?.org_currency_id,
              charge_1_name: charge1Name,
              charge_1_value: Number(charge1Value),
              transporter_id: transporterId,
              vehicle_number: vehicleNumber,
              freight_tax_id:
                freightTaxId === 'Not Applicable' ? null : freightTaxId,
              freight_tax_info: freightTaxInfo,
              transporter_bill_number: transporterBillNumber,
              is_so_automatic_notification_enabled: sendWhatsappNotification,
              freight_sac_code: freightSacCode,
              order_type: salesOrderType,
              is_discount_in_percent: discountType === 'Percent',
              sales_order_number: initialSoNumber?.toLowerCase()?.trim() === soNumber?.toLowerCase()?.trim() ? null : soNumber,
              seq_id: docSeqId || null,
            },
          ];
          createSO(payload, (so) => {
            if (selectedTags?.length) {
              const tagPayload = {
                entity_name: 'SALES_ORDER',
                entity_id: so?.order_id,
                action: 'ADD',
                tag: selectedTags.join(','),
              };
              createTag(tagPayload, () => {
                history.push(`/sales/${toPush}/view/${so?.order_id}`);
              });
            } else {
              history.push(`/sales/${toPush}/view/${so?.order_id}`);
            }
            this.setState({ currentAction: '' });
          });
        }
      } else if (gstNumber && String(gstNumber)?.length !== 15) {
        notification.open({
          message: 'Please enter a valid GST Number',
          duration: 4,
          type: 'error',
          placement: 'top',
        });
      } else {
        notification.open({
          message: 'Please fill all the required fields',
          duration: 4,
          type: 'error',
          placement: 'top',
        });
      }
    }
  }

  customInputChange(fieldValue, cfId) {
    const { cfSalesOrderDoc } = this.state;
    const newCustomField = cfSalesOrderDoc.map((customField) => {
      if (customField?.cfId === cfId) {
        if (customField?.fieldType === 'ATTACHMENT') {
          return {
            ...customField,
            fieldValue: fieldValue?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url,
              type: attachment.type,
              name: attachment.name,
              uid: attachment.uid,
            })),
          };
        }
        return {
          ...customField,
          fieldValue,
        };
      }
      return {
        ...customField,
      };
    });
    this.setState({
      cfSalesOrderDoc: newCustomField,
    });
  }

  customLineInputChange(fieldValue, key, rate) {
    const { data, billingAddress, pricesToApply, } = this.state;
    const { user } = this.props;
    const copyData = JSON.parse(JSON.stringify(data));
    for (let i = 0; i < copyData.length; i++) {
      if (copyData[i]?.key === key) {
        copyData[i].lineCustomFields = fieldValue;
        const rateCf = fieldValue?.find((j) => j?.fieldName === 'Rate');
        const qtyCf = fieldValue?.find((j) => j?.fieldName === 'Quantity');
        copyData[i].unitPrice =
          rateCf?.fieldValue > 0
            ? parseFloat(
              Number(rateCf?.fieldValue)?.toFixed(DEFAULT_CUR_ROUND_OFF)
            )
            : '';

        let discountValue = Number.parseFloat(copyData[i].discount) || 0;
        if (copyData[i]?.showUnitDiscount) {
          discountValue = Number.parseFloat(Number(Number(copyData[i].unitDiscount) * Number(qtyCf?.fieldValue)).toFixed(DEFAULT_CUR_ROUND_OFF));
        }

        const taxableValue = copyData[i]?.lineDiscountType === 'Percent' ?
          Number(qtyCf?.fieldValue) * Number(copyData[i].unitPrice) * (discountValue ? Number(100 - discountValue) / 100 : 1) : Math.max(qtyCf?.fieldValue * copyData[i].unitPrice - discountValue, 0);

        copyData[i].quantity =
          qtyCf?.fieldValue > 0
            ? parseFloat(
              Number(qtyCf?.fieldValue).toFixed(DEFAULT_CUR_ROUND_OFF)
            )
            : '';
        copyData[i].discount = copyData?.[i]?.showUnitDiscount ? discountValue : copyData?.[i]?.discount;
        copyData[i].child_taxes = Helpers.computeTaxation(
          taxableValue,
          copyData[i].taxInfo,
          user?.tenant_info?.state,
          billingAddress?.state
        )?.tax_info?.child_taxes;

        // applying price list
        for (let j = 0; j < pricesToApply?.length; j++) {
          const priceToApply = pricesToApply?.[j];
          if (copyData?.[i]?.product_sku_id === priceToApply?.product_sku_id) {

            if (rate !== undefined && rate !== null) {
              copyData[i].unitPrice = rate;
            } else if (Number(copyData?.[i]?.quantity) >= priceToApply?.start_quantity && Number(copyData?.[i]?.quantity) <= priceToApply?.end_quantity) {
              copyData[i].unitPrice = priceToApply?.is_inclusive_of_tax ? priceToApply?.price_list_amount / ((100 + copyData?.[i]?.taxInfo?.tax_value) / 100) : priceToApply?.price_list_amount;
              copyData[i].discount = priceToApply?.discount_percentage;
              break;
            } else if ((priceToApply?.start_quantity === undefined && priceToApply?.end_quantity === undefined) || (priceToApply?.start_quantity === null && priceToApply?.end_quantity === null)) {
              copyData[i].unitPrice = Number(priceToApply?.price_list_amount) ? Number(priceToApply?.price_list_amount) : (copyData?.[i]?.default_selling_price || 0);
              copyData[i].discount = priceToApply?.discount_percentage;
            } else {
              copyData[i].unitPrice = copyData?.[i]?.default_selling_price || 0;
            }
          }
        }
      }
    }
    this.setState({ data: copyData }, () => {
      if (!this.state?.isLineWiseDiscount) {
        this.handleDiscountPercentageChange(this.state.discountPercentage);
      }
    });
  }

  onBulkUpload(updatedData, importedData) {
    const { user } = this.props;
    const { billingAddress, cfSalesOrderLine } = this.state;

    const oldVisibleColumns = {
      PRODUCT: {
        label: 'Product',
        visible: true,
        disabled: true,
      },
      QUANTITY: {
        label: 'Quantity',
        visible: true,
        disabled: true,
      },
      UNIT: {
        label: 'Unit',
        visible: true,
        disabled: true,
      },
      UNIT_PRICE: {
        label: 'Unit Price',
        visible: true,
        disabled: true,
      },
      TAX: {
        label: 'Tax',
        visible: true,
        disabled: true,
      },
      DISCOUNT: {
        label: 'Discount',
        visible: true,
        disabled: true,
      },
      LINE_TOTAL: {
        label: 'Line Total',
        visible: true,
        disabled: true,
      },
    };

    const finalData = [];
    this.setState({ isBulkUpload: true });

    for (let i = 0; i < importedData?.length; i++) {
      const importedItem = importedData[i];
      for (let j = 0; j < updatedData?.length; j++) {
        const updatedItem = updatedData[j];
        const soLine = {};
        if (importedItem?.key === updatedItem?.key) {
          soLine.key = uuidv4();
          soLine.product_sku_name = updatedItem?.product_sku_name;
          soLine.product_sku_id = updatedItem?.product_sku_id;
          soLine.product_sku_info = {
            internal_sku_code: updatedItem?.internal_sku_code,
          };
          soLine.available_qty =
            updatedItem?.product_sku_info?.department_quantity;
          soLine.asset1 = updatedItem?.product_sku_info?.assets?.[0]?.url || '';
          soLine.sku = updatedItem?.product_sku_info?.sku;
          soLine.quantity = Number(importedItem?.quantity);
          soLine.unitPrice = Number(importedItem?.unit_price);
          soLine.uomId = Number(updatedItem?.uom_info?.uom_id);
          soLine.uomGroup = Number(updatedItem?.group_id);
          soLine.taxId = Number(updatedItem?.tax_id);
          soLine.taxInfo = updatedItem?.tax_info;
          soLine.child_taxes = Helpers.computeTaxation(
            importedItem.quantity *
            importedItem.unit_price *
            (importedItem.line_discount_percentage
              ? Number(100 - importedItem.line_discount_percentage) / 100
              : 1),
            updatedItem?.tax_info,
            user?.tenant_info?.state,
            billingAddress?.state
          )?.tax_info?.child_taxes;
          soLine.remarks = updatedItem?.remarks;
          soLine.hsn_code = updatedItem?.product_sku_info?.hsn_code;
          soLine.discount = importedItem?.line_discount_percentage;
          soLine.product_type = updatedItem?.product_sku_info?.product_type;
          soLine.showFreebie =
            Number(importedItem?.free_quantity) > 0
              ? Number(importedItem?.free_quantity)
              : null;
          soLine.free_quantity = importedItem?.free_quantity || 0;
          soLine.lineCustomFields = FormHelpers.lineSystemFieldValue(
            cfSalesOrderLine,
            []
          )
            ?.filter(
              (k) => k?.fieldName === 'Rate' || k?.fieldName === 'Quantity'
            )
            ?.map((k) => ({
              ...k,
              fieldValue:
                k?.fieldName === 'Rate'
                  ? Number(importedItem?.unit_price)
                  : Number(importedItem?.quantity),
            }));
          soLine.bundle_products = updatedItem?.bundle_products?.map(
            (item) => ({
              ...item,
              product_info: item?.product_sku_info,
              product_sku_info: updatedItem?.product_sku_info?.product_sku_id,
              available_qty: updatedItem?.product_sku_info?.department_quantity,
              uom_info: item?.uom_info?.[0],
              bundle_quantity: item?.quantity / updatedItem?.quantity,
              tax_info: item?.tax_group_info,
            })
          );
          soLine.tenant_product_id =
            updatedItem?.tenant_product?.[0]?.tenant_product_id;
          soLine.uom_info = updatedItem?.uom_info;
          soLine.uom_list = updatedItem?.uom_list;

          finalData.push(soLine);
        }
      }
    }
    this.setState({ data: finalData, visibleColumns: oldVisibleColumns });
  }

  getDataSource = (dataSource) => {
    const data = JSON.parse(JSON.stringify(dataSource));
    return [...data.filter((item) => item?.product_sku_id), ...data.filter((item) => !item?.product_sku_id)];
  }

  handleDiscountPercentageChange = (value) => {

    const { user } = this.props;
    const { discountType, data, billingAddress } = this.state;

    const copyData = JSON.parse(JSON.stringify(data));

    const totalValue = copyData?.reduce((acc, cur) => acc + (cur?.quantity * cur?.unitPrice), 0);

    copyData?.map((item) => {

      const discountValue = discountType === 'Percent' ? parseFloat(value) : ((item?.quantity * item?.unitPrice) / parseFloat(totalValue)) * parseFloat(value);
      const taxableValue = discountType === 'Percent' ? (item?.quantity * item?.unitPrice) * (1 - discountValue / 100) : Math.max(item?.quantity * item?.unitPrice - discountValue, 0);

      item.discount = discountValue;
      item.lineDiscountType = discountType;
      item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, user?.tenant_info?.state, billingAddress?.state)?.tax_info?.child_taxes;
    })

    this.setState({
      data: copyData,
      discountPercentage: parseFloat(value),
    })
  }

  render() {
    const { createSOLoading, getSOByIdLoading, updateSOLoading, updatePoStatusLoading, getProductsSuccess, getCustomers, user, selectedSO, match, getDocConfigSOLoading, getProductByIdLoading, getCustomersLoading, MONEY, getApplyPriceLists, getOrgStatusSalesOrderLoading, CurrenciesResults, getCurrenciesLoading, isSalesEstimate, getSOById, location, getDocConfig, priceMasking, isCarryForward, history,
    } = this.props;
    const { data, formSubmitted, shippingAddress, chargeData, termsAndConditions, tncId, withApproval, paymentTerms, orderDate, showAddressDrawer, selectedAddressType, deliveryDate, billingAddress, discountPercentage, selectedCustomer, fileList, gstNumber, soNumber, cfSalesOrderDoc,
      tenantDepartmentId, currentAction, remark, isLineWiseDiscount, showAddCustomer, showNewCustomerModal, productSkuIds, pricesToApply, selectedPriceList, selectedCurrencyID, selectedCurrencyName, sendWhatsappNotification, accountManager, charge1Value, selectedOrder,
      visibleColumns, cfSalesOrderLine, selectedTags, createExpenseCheckbox, transporterId, vehicleNumber, transporterBillNumber, freightTaxInfo, openFreightTax, freightSacCode, freightTax, freightTaxId, isEstimate, estimateExpiryDate, selectedTenant, isAutomaticConversionRate,
      currencyConversionRate, documentUINames, updateDocumentReason, freightTaxData, discountType, salesOrderType, cfSalesOrderDocAMC, cfSalesOrderLineAMC, cfSalesOrderDocSO, cfSalesOrderLineSO, visibleColumnsSO, visibleColumnsAMC, docSeqId, initialSoNumber,
    } = this.state;
    const departmentLevelStock =
      user?.tenant_info?.inventory_config?.settings?.department_level_stock;
    const barCoding =
      user?.tenant_info?.global_config?.sub_modules?.barcoding?.is_active;
    const selectedTenantIdSo =
      selectedTenant || location?.state?.selectedTenantIdSo;
    const restrictAccManager =
      !isSalesEstimate && window.location.href.includes('/estimate')
        ? user?.tenant_info?.sales_config?.sub_modules?.sales_order?.settings
          ?.restrict_estimate_of_other_acc_manager
        : user?.tenant_info?.sales_config?.sub_modules?.sales_order?.settings
          ?.restrict_orders_of_other_acc_manager;

    let copyDocLevelError;
    let copyLineLevelError;
    if (formSubmitted) {
      const { docLevelError, lineLevelError } = this.getSalesErrors();
      copyDocLevelError = docLevelError;
      copyLineLevelError = lineLevelError;
    }
    const accountingGSTTransactionAsPerMaster =
      user?.tenant_info?.sales_config?.sub_modules?.customer?.settings
        ?.gst_details_in_transaction === 'AS_PER_MASTER';

    const isPriceListMandatory = user?.tenant_info?.sales_config?.sub_modules?.sales_order?.settings?.is_price_list_mandatory;
    const isDeliveryDateMandatory = user?.tenant_info?.sales_config?.sub_modules?.sales_order?.settings?.delivery_date_mandatory;

    const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } =
      priceMasking;

    const splitChargesData = (charge) => {
      const chargeWithTaxName = charge?.filter((line) => line?.chargesTaxInfo?.tax_id) || [];
      const chargeWithoutTaxName = charge?.filter((line) => !line?.chargesTaxInfo?.tax_id) || [];
      return { chargeWithTaxName, chargeWithoutTaxName };
    };

    const renderCharges = (charge) => charge?.map((line) => (
      <div
        key={line?.chargeKey}
        className="form-calculator__field"
      >
        <div className="form-calculator__field-name">
          {!user?.tenant_info?.sales_config?.sub_modules
            ?.sales_order?.settings?.use_custom_charges ? (
            <div className="select_extra_charge_wrapper">
              <SelectExtraCharge
                containerClassName="orgInputContainer"
                selectedChargeName={line.charge_name}
                disabled={
                  createSOLoading ||
                  updatePoStatusLoading ||
                  getOrgStatusSalesOrderLoading
                }
                onChange={(value) => {
                  const copyChargeData = JSON.parse(
                    JSON.stringify(chargeData)
                  );
                  copyChargeData.map((item) => {
                    if (item.chargeKey === line.chargeKey) {
                      item.charge_name = value?.ledger_name;
                      item.chargesSacCode = (value?.charge_sac_code) || null;
                    }
                  });
                  this.setState({ chargeData: copyChargeData });
                }}
                customStyle={{
                  width: '220px',
                  backgroundColor: 'white',
                }}
                excludeCharges={chargeData?.map(
                  (item) => item?.charge_name
                )}
                entityName="SALES"
              />
              {line?.chargesTax && (
                <div style={{
                  color: '#2d7df7',
                  fontWeight: '400',
                  fontSize: '12px',
                }}
                >
                  {`tax@${line?.chargesTax}%`}
                </div>
              )}
            </div>
          ) : (
            <H3FormInput
              value={line?.charge_name}
              type="text"
              containerClassName={`${formSubmitted &&
                Number(line?.charge_name) <= 0
                ? 'form-error__input'
                : ''
                }`}
              labelClassName="orgFormLabel"
              inputClassName="orgFormInput"
              onChange={(e) => {
                const copyChargeData = JSON.parse(
                  JSON.stringify(chargeData)
                );
                copyChargeData.map((item) => {
                  if (item.chargeKey === line.chargeKey) {
                    // eslint-disable-next-line no-param-reassign
                    item.charge_name = e.target.value;

                  }
                  return data;
                });
                this.setState({ chargeData: copyChargeData });
              }}
            />
          )}
        </div>
        <div
          className="form-calculator__field-value"
          style={{ display: 'flex', gap: '0px' }}
        >
          <div style={{ width: '140px', marginRight: '-27px' }}>
            <H3FormInput
              value={line?.charge_amount}
              type="number"
              containerClassName={`orgInputContainer ${formSubmitted &&
                Number(line?.charge_amount) <= 0
                ? 'form-error__input'
                : ''
                }`}
              labelClassName="orgFormLabel"
              inputClassName="orgFormInput"
              onChange={(e) => {
                const copyChargeData = JSON.parse(
                  JSON.stringify(chargeData),
                );
                copyChargeData.map((item) => {
                  if (item.chargeKey === line.chargeKey) {
                    const updatedChargeAmount = parseFloat(e.target.value) || 0;

                    // Update charge_amount
                    item.charge_amount = updatedChargeAmount;
                    // Compute chargesTaxData with updated values
                    const computedTaxData = Helpers.computeTaxation(
                      updatedChargeAmount,
                      line.chargesTaxInfo,
                      user?.tenant_info?.state,
                      billingAddress?.state
                    );
                    // Update chargesTaxData
                    item.chargesTaxData = {
                      ...line?.chargesTaxInfo,
                      child_taxes: computedTaxData?.tax_info?.child_taxes || [],
                    };
                  }
                  return data;
                });
                this.setState({ chargeData: copyChargeData });
              }}
            />
          </div>
          <ChargesTaxInput
            openChargesTax={line?.openChargesTax}
            setOpenChargesTax={(value) => {
              const copyChargeData = JSON.parse(
                JSON.stringify(chargeData),
              );
              copyChargeData.map((item) => {
                if (item.chargeKey === line.chargeKey) {
                  // eslint-disable-next-line no-param-reassign
                  item.openChargesTax = value;
                }
                return data;
              });
              this.setState({ chargeData: copyChargeData });
            }}
            sacCode={line?.chargesSacCode}
            setSacCode={(value) => {
              const copyChargeData = JSON.parse(
                JSON.stringify(chargeData),
              );
              copyChargeData.map((item) => {
                if (item.chargeKey === line.chargeKey) {
                  // eslint-disable-next-line no-param-reassign
                  item.chargesSacCode = value;
                }
                return data;
              });
              this.setState({ chargeData: copyChargeData });
            }}
            chargesTaxId={line?.chargesTaxId}
            setChargesTaxData={(value) => {
              const updatedChargeData = chargeData?.map((item) => {
                if (item.chargeKey === line.chargeKey) {
                  return {
                    ...item,
                    chargesTaxId: !value ? 'Not Applicable' : value?.tax_id,
                    chargesTax: !value ? null : value?.tax_value,
                    chargesTaxInfo: !value ? null : value,
                    chargesTaxData: !value ? {
                      child_taxes: [
                        {
                          tax_amount: 0,
                          tax_type_name: '',
                        },
                      ],
                    } : {
                      ...value,
                      child_taxes: Helpers.computeTaxation(line?.charge_amount, value, user?.tenant_info?.state, billingAddress?.state)?.tax_info?.child_taxes,
                    },
                  };
                }
                return item;
              });
              this.setState({ chargeData: updatedChargeData });
            }}
          />
          <div
            className="form-calculator__delete-line-button"
            onClick={() =>
              this.handleDeleteCharge(line?.chargeKey)
            }
          >
            <FontAwesomeIcon
              icon={faCircleXmark}
              size="sm"
              style={{ color: '#6f7276' }}
            />
          </div>
        </div>
      </div>
    ))

    const roundOffMethod = !isSalesEstimate && window.location.href.includes('/estimate') ? user?.tenant_info?.sales_config?.sub_modules?.sales_estimate?.settings?.round_off_method : user?.tenant_info?.sales_config?.sub_modules?.sales_order?.settings?.round_off_method;

    const userMenuConfig = JSON.parse(localStorage.getItem('user_menu_config'));

    return (
      <Fragment>
        <Drawer
          open={showNewCustomerModal}
          onClose={() => this.setState({ showNewCustomerModal: false })}
          width="720px"
          destroyOnClose
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header" style={{ width: '680px' }}>
              <H3Text
                text="Add New Customer"
                className="custom-drawer__title"
              />
              <H3Image
                src={closeIcon}
                className="custom-drawer__close-icon"
                onClick={() => this.setState({ showNewCustomerModal: false })}
              />
            </div>
          </div>
          <CustomerForm
            callback={(createdCustomer) => {
              getCustomers(
                '',
                selectedTenant || selectedTenantIdSo,
                1,
                1000,
                '',
                (newCustomers) => {
                  this.showNewCustomerInSelect(
                    newCustomers?.customers.filter(
                      (customer) =>
                        customer?.customer_id === createdCustomer.customer_id
                    )[0]
                  );
                  this.setState({ showNewCustomerModal: false });
                }
              );
            }}
          />
        </Drawer>
        <Drawer
          open={showAddressDrawer}
          onClose={() =>
            this.setState({ showAddressDrawer: false, selectedAddressType: '' })
          }
          width="360"
          destroyOnClose
        >
          <AddressSelector
            title={
              selectedAddressType === 'TENANT_SHIPPING'
                ? 'Shipping Address'
                : 'Billing Address'
            }
            selectedAddressId={
              selectedAddressType === 'TENANT_SHIPPING'
                ? shippingAddress?.address_id
                : billingAddress?.address_id
            }
            onAddressChange={(address) => {
              if (selectedAddressType === 'TENANT_SHIPPING') {
                this.setState({
                  shippingAddress: address,
                  showAddressDrawer: false,
                });
              }
              if (selectedAddressType === 'TENANT_BILLING') {
                this.setState({
                  billingAddress: address,
                  showAddressDrawer: false,
                  data: data?.map((record) => ({
                    ...record,
                    child_taxes: Helpers.computeTaxation(
                      record?.quantity *
                      record?.unitPrice *
                      (record.discount
                        ? Number(100 - record.discount) / 100
                        : 1),
                      record.taxInfo,
                      address?.state,
                      user?.tenant_info?.state
                    )?.tax_info?.child_taxes,
                  })),
                });
              }
            }}
            entityId={selectedCustomer}
            entityType="CUSTOMER"
            tenantId={selectedTenant || selectedOrder?.tenant_id || selectedSO?.tenant_id || selectedTenantIdSo || user?.tenant_info?.tenant_id}
          />
        </Drawer>
        {getSOByIdLoading ? (
          <FormLoadingSkull />
        ) : (
          <div
            className={
              !isSalesEstimate
                ? 'form__wrapper form-component'
                : 'form__wrapper form-page'
            }
            style={{ paddingTop: isSalesEstimate ? '0px' : '90px' }}
          >
            {formSubmitted &&
              (copyDocLevelError?.length > 0 ||
                copyLineLevelError?.length > 0) && (
                <ErrorHandle
                  message="Mandatory fields required"
                  docLevelErrors={copyDocLevelError}
                  lineLevelErrors={copyLineLevelError}
                />
              )}
            <div className="ant-row">
              <div className="ant-col-md-24">
                <div className="form__section">
                  <div className="flex-display flex-align-c mg-bottom-5 pd-right-15">
                    <H3Text text="PART A" className="form__section-title" />
                    <div className="form__section-line" />
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'end',
                        alignItems: 'center',
                      }}
                    >
                      <CurrencyConversionV2
                        selectedCurrencyName={selectedCurrencyName}
                        selectedCurrencyID={selectedCurrencyID}
                        currencyConversionRate={currencyConversionRate}
                        isAutomaticConversionRate={isAutomaticConversionRate}
                        setCurrencyConversionRate={(val) =>
                          this.setState({ currencyConversionRate: val })
                        }
                        setIsAutomaticConversionRate={(val) =>
                          this.setState({ isAutomaticConversionRate: val })
                        }
                        setSelectedCurrencyName={(val) =>
                          this.setState({ selectedCurrencyName: val })
                        }
                        setSelectedCurrencyID={(val) =>
                          this.setState({ selectedCurrencyID: val })
                        }
                      />
                      <CustomDocumentInputs
                        customFields={cfSalesOrderDoc}
                        updateCustomFields={(cf) =>
                          this.setState({ cfSalesOrderDoc: cf })
                        }
                      />
                    </div>
                  </div>
                  <div className="form__section-inputs mg-bottom-20">
                    <div className="ant-row">
                      {(window.location.href.includes('/sales-orders') && !window.location.href.includes('/update') && (!selectedSO && window.location.href.includes('/create')) && <div className="ant-col-md-24">
                        <div className="form__input-row"
                          style={{
                            alignItems: 'center', marginBottom: '15px', display: 'flex', justifyContent: 'space-between',
                          }}
                        >
                          <Radio.Group
                            disabled={
                              createSOLoading || updateSOLoading || getDocConfigSOLoading || !(Helpers.getPermission(Helpers.permissionEntities.SALES_ORDER, Helpers.permissionTypes.CREATE, user) && Helpers.getPermission(Helpers.permissionEntities.ANNUAL_MAINTENANCE_CONTRACT, Helpers.permissionTypes.CREATE, user)) || !(user?.tenant_info?.sales_config?.sub_modules?.sales_order?.is_active && user?.tenant_info?.sales_config?.sub_modules?.annual_maintenance_contract?.is_active)
                            }
                            onChange={(event) => {
                              if (event.target.value === 'ANNUAL_MAINTENANCE_CONTRACT') {
                                getDocConfig(selectedTenant, event.target.value, (doc) => {
                                  const termsData = doc?.find((item) => item?.document_type === `${event.target.value}_DOWNLOAD`)?.allowed_fields?.find((field) => field?.field_name === 'default_t_and_c')?.field_value;
                                  this.setState({
                                    termsAndConditions: termsData,
                                  });
                                });
                              }
                              this.setState({
                                tncId: null,
                                salesOrderType: event.target.value,
                                formSubmitted: false,
                                cfSalesOrderDoc: event.target.value === 'ANNUAL_MAINTENANCE_CONTRACT' ? cfSalesOrderDocAMC : cfSalesOrderDocSO,
                                cfSalesOrderLine: event.target.value === 'ANNUAL_MAINTENANCE_CONTRACT' ? cfSalesOrderLineAMC : cfSalesOrderLineSO,
                                visibleColumns: event.target.value === 'ANNUAL_MAINTENANCE_CONTRACT' ? visibleColumnsAMC : visibleColumnsSO,
                                data: event.target.value === 'ANNUAL_MAINTENANCE_CONTRACT' ?
                                  data.map((item) => ({ ...item, lineCustomFields: cfSalesOrderLineAMC }))
                                  : data.map((item) => ({ ...item, lineCustomFields: cfSalesOrderLineSO })),
                                sendWhatsappNotification: event.target.value === 'ANNUAL_MAINTENANCE_CONTRACT' ? user?.tenant_info?.is_amc_automatic_notification_enabled : user?.tenant_info?.is_so_automatic_notification_enabled,
                              });
                            }}
                            value={salesOrderType}
                            className="mg-top-5"
                          >
                            <Radio value={'SALES_ORDER'}>Sales Order</Radio>
                            <Radio value={'ANNUAL_MAINTENANCE_CONTRACT'}>AMC Sales Order </Radio>
                          </Radio.Group>
                        </div>
                      </div>)}
                      <DocumentNumberSeqInput
                        valueFromProps={soNumber}
                        updateCase={match?.url?.includes('/update')}
                        setInitialDocSeqNumber={(value) => this.setState({ initialSoNumber: value })}
                        entityName={salesOrderType}
                        docSeqId={docSeqId}
                        tenantId={selectedTenant || selectedSO?.tenant_id}
                        onChangeFromProps={(event, newValue, seqId) => {
                          this.setState({
                            soNumber: newValue ? (newValue || '') : (event?.target?.value || ''),
                            docSeqId: seqId,
                          })
                        }}
                        docTitle={salesOrderType === 'ANNUAL_MAINTENANCE_CONTRACT' ? 'AMC Sales Order #' : `${documentUINames.uiName} #`}
                        formSubmitted={formSubmitted}
                      />
                      {!window.location.href.includes('/estimate') && (
                        <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <H3Text
                              required
                              text="Select Location"
                              className="form__input-row__label"
                            />

                            <div className="form__input-row__input">
                              <TenantSelector
                                hideTitle
                                selectedTenant={selectedTenant}
                                showSearch={false}
                                onChange={(value) => {
                                  const defaultDepForSales =
                                    user?.user_tenants?.find(
                                      (item) => item?.tenant_id === value
                                    )?.default_department_for_sales;
                                  const defaultDepId = user?.user_tenants?.find(
                                    (item) => item?.tenant_id === value
                                  )?.tenant_department_info
                                    ?.tenant_department_id;
                                  const currentTenantInfo =
                                    user?.user_tenants?.find(
                                      (item) => item?.tenant_id === value
                                    );
                                  this.setState({
                                    tncId: null,
                                    selectedTenant: value,
                                    tenantDepartmentId: departmentLevelStock
                                      ? defaultDepForSales
                                      : defaultDepId,
                                    currentSelectedTenantInfo:
                                      currentTenantInfo,
                                    selectedCustomer: null,
                                    selectedCustomerInfo: null,
                                    selectedPriceList: null,
                                    gstNumber: '',
                                    fileList: [],
                                    chargeData: [],
                                    data: [
                                      {
                                        key: uuidv4(),
                                        product_sku_name: '',
                                        quantity: '',
                                        unitPrice: '',
                                        taxId: '',
                                        lot: '',
                                        child_taxes: [
                                          {
                                            tax_amount: 0,
                                            tax_type_name: '',
                                          },
                                        ],
                                        lineCustomFields: cfSalesOrderLine,
                                      },
                                    ],
                                  });
                                  if (salesOrderType === 'ANNUAL_MAINTENANCE_CONTRACT') {
                                    getDocConfig(value, 'ANNUAL_MAINTENANCE_CONTRACT', (doc) => {
                                      const termsData = doc
                                        ?.find(
                                          (item) =>
                                            item?.document_type ===
                                            `${'ANNUAL_MAINTENANCE_CONTRACT'}_DOWNLOAD`
                                        )
                                        ?.allowed_fields?.find(
                                          (field) =>
                                            field?.field_name ===
                                            'default_t_and_c'
                                        )?.field_value;
                                      this.setState({
                                        termsAndConditions: termsData,
                                        sendWhatsappNotification:
                                          user?.tenant_info
                                            ?.is_so_automatic_notification_enabled,
                                      });
                                    });
                                  }
                                }}
                                placeholder="Select Business Unit"
                                labelClassName="form__input-row__label"
                                inputClassName="orgFormInput input"
                                includedTenants={Helpers.getTenantEntityPermission(
                                  user?.user_tenants,
                                  Helpers.permissionEntities[(!isSalesEstimate && window.location.href.includes('/estimate')) ? 'SALES_ESTIMATE' : 'SALES_ORDER'],
                                  Helpers.permissionTypes.CREATE
                                )}
                                loading={
                                  createSOLoading ||
                                  updateSOLoading ||
                                  getDocConfigSOLoading
                                }
                                disabled={
                                  match.params.prId ||
                                  match?.params?.orderId ||
                                  createSOLoading ||
                                  updateSOLoading ||
                                  getDocConfigSOLoading
                                }
                              />
                            </div>
                          </div>
                        </div>
                      )}
                      <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <H3Text
                            required
                            text="Department"
                            className="form__input-row__label"
                          />
                          {/* {!match?.params?.orderId ||
                            location?.state?.cloneSo ? ( */}
                          <div className="form__input-row__input">
                            <SelectDepartment
                              hideTitle
                              tenantId={
                                selectedTenant || selectedSO?.tenant_id
                              }
                              selectedDepartment={tenantDepartmentId}
                              onChange={(value) => {
                                this.setState({
                                  tenantDepartmentId:
                                    value?.tenant_department_id,
                                });
                                getProductsSuccess(null, false);
                              }}
                              emptyNotAllowed
                              loading={
                                createSOLoading ||
                                updateSOLoading ||
                                getDocConfigSOLoading
                              }
                              disabled={
                                createSOLoading ||
                                updateSOLoading ||
                                getDocConfigSOLoading
                              }
                              labelClassName="form__input-row__label"
                              inputClassName="orgFormInput input"
                              tenentLevelDepartment
                              showSearch
                            />
                            {formSubmitted && !tenantDepartmentId && (
                              <div className="input-error">
                                *Please select a department
                              </div>
                            )}
                          </div>
                          {/* ) : (
                            <div
                              className="form__input-row"
                              style={{ marginBottom: "10px" }}
                            >
                              <H3Text
                                text={`\u00A0\u00A0${selectedSO?.tenant_department_info?.alias_name}`}
                                className="form__input-row__text"
                              />
                            </div>
                          )} */}
                        </div>
                      </div>
                      <div className="ant-col-md-6">
                        <div className="form__input-row orgInputContainer">
                          <H3Text
                            required
                            // text={isEstimate ? 'Estimate Date' : 'Order Date'}
                            text={salesOrderType === 'ANNUAL_MAINTENANCE_CONTRACT' ? 'AMC SO Date' : `${documentUINames.shortUIName} Date`}
                            className="form__input-row__label"
                          />
                          <div className={`form__input-row__input ${(formSubmitted && !orderDate) ? 'form__input-row__input-error' : ''}`}>
                            <DatePicker
                              value={orderDate}
                              onChange={(value) => {
                                this.setState({ orderDate: value });
                              }}
                              style={{
                                border: '1px solid rgba(68, 130, 218, 0.2)',
                                borderRadius: '2px',
                                height: '28px',
                                padding: '1px 3px',
                                width: '100%',
                                background: 'white',
                              }}
                              disabled={
                                createSOLoading ||
                                updateSOLoading ||
                                getDocConfigSOLoading
                              }
                              format="DD/MM/YYYY"
                            />
                            {formSubmitted && !orderDate && (
                              <div className="input-error">
                                *Please enter order date
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="ant-col-md-6">
                        <div className="form__input-row orgInputContainer">
                          <H3Text
                            required={!isEstimate && isDeliveryDateMandatory}
                            text="Delivery Date"
                            className="form__input-row__label"
                          />
                          <div className={`form__input-row__input ${(!isEstimate && formSubmitted && !deliveryDate && isDeliveryDateMandatory) ? 'form__input-row__input-error' : ''}`}>
                            <DatePicker
                              value={deliveryDate}
                              onChange={(value) => {
                                this.setState({ deliveryDate: value });
                              }}
                              style={{
                                border: '1px solid rgba(68, 130, 218, 0.2)',
                                borderRadius: '2px',
                                height: '28px',
                                padding: '1px 3px',
                                width: '100%',
                                background: 'white',
                              }}
                              disabled={
                                createSOLoading ||
                                updateSOLoading ||
                                getDocConfigSOLoading
                              }
                              format="DD/MM/YYYY"
                            />
                            {formSubmitted && !deliveryDate && isDeliveryDateMandatory && !isEstimate && (
                              <div className="input-error">
                                *Please enter delivery  date
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <H3Text
                            required
                            text="Customer"
                            className="form__input-row__label"
                          />
                          <div className={`form__input-row__input ${(formSubmitted && !selectedCustomer) ? 'form__input-row__input-error' : ''}`}>
                            <SelectCustomer
                              showAddCustomer={showAddCustomer}
                              selectedCustomer={selectedCustomer}
                              addCustomer={() =>
                                this.setState({ showNewCustomerModal: true })
                              }
                              hideTitle
                              onChange={(value) => {

                                const docCf = CustomFieldHelpers.postCfStructure(cfSalesOrderDoc?.filter((item) => (item?.isActive && item?.visible))) || [];
                                const customerCf = value?.custom_field_values?.filter((item) => (item?.is_active)) || [];
                                const mergedCf = CustomFieldHelpers.mergeCustomFields(docCf, customerCf) || [];

                                this.setState({
                                  selectedCustomer: value?.customer_id,
                                  billingAddress:
                                    value?.customer_info
                                      ?.billing_address_details,
                                  shippingAddress:
                                    value?.customer_info
                                      ?.shipping_address_details,
                                  gstNumber: value?.customer_info?.gst_number,
                                  selectedCustomerInfo: value?.customer_info,
                                  accountManager: restrictAccManager
                                    ? user?.user_id
                                    : value?.customer_info?.account_manager_info
                                      ?.account_manager_id,
                                  data: data?.map((record) => ({
                                    ...record,
                                    child_taxes: Helpers.computeTaxation(
                                      record?.quantity *
                                      record?.unitPrice *
                                      (record.discount
                                        ? Number(100 - record.discount) / 100
                                        : 1),
                                      record.taxInfo,
                                      value?.customer_info
                                        ?.billing_address_details?.state,
                                      user?.tenant_info?.state
                                    )?.tax_info?.child_taxes,
                                  })),
                                  selectedCurrencyID: value?.org_currency_id,
                                  selectedCurrencyName: value?.currency_info,
                                  currencyConversionRate:
                                    isAutomaticConversionRate
                                      ? value?.currency_info
                                        ?.automatic_conversion_rate
                                      : value?.currency_info?.conversion_rate,
                                  selectedPriceList: null,
                                  paymentTerms: value?.customer_info?.default_payment_terms?.due_days || 0,
                                  cfSalesOrderDoc: mergedCf,
                                });
                              }}
                              tenantId={selectedTenant}
                              disabled={
                                createSOLoading ||
                                updateSOLoading ||
                                getDocConfigSOLoading ||
                                (window.location.href.includes('/estimate') && window.location.href.includes('/view'))
                              }
                              containerClass="form__input-row"
                            />
                            {formSubmitted && !selectedCustomer && (
                              <div className="input-error">
                                *Please select customer
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <H3Text
                            text="GST Number"
                            className="form__input-row__label"
                          />
                          <H3FormInput
                            name="a valid GST Number"
                            type="text"
                            containerClassName="orgInputContainer form__input-row__input"
                            labelClassName="orgFormLabel"
                            inputClassName="orgFormInput input"
                            placeholder=""
                            onChange={(e) =>
                              this.setState({ gstNumber: e.target.value })
                            }
                            value={gstNumber}
                            showError={
                              formSubmitted &&
                              (gstNumber
                                ? String(gstNumber)?.length !== 15
                                : false)
                            }
                            disabled={
                              createSOLoading ||
                              updateSOLoading ||
                              getDocConfigSOLoading ||
                              accountingGSTTransactionAsPerMaster
                            }
                          />
                        </div>
                      </div>

                      <div className="ant-col-md-6">
                        <SelectPaymentTerm
                          selectedPaymentTerm={paymentTerms}
                          onChange={(value) => {
                            this.setState({ paymentTerms: value?.due_days });
                          }}
                          callback={(value) => {
                            this.setState({ paymentTerms: Number(value) });
                          }}
                          containerClassName="orgInputContainer form__input-row so-payment-terms"
                          inputClassName="form__input-row__input"
                          labelClassName="form__input-row__label"
                          showError={formSubmitted && !paymentTerms}
                          disabled={
                            createSOLoading ||
                            updateSOLoading ||
                            getDocConfigSOLoading
                          }
                          showAddPaymentTerm
                          placeholder="Select Payment Term"
                        />
                      </div>
                      <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <H3Text
                            text="Payment Remark"
                            className="form__input-row__label"
                          />
                          <H3FormInput
                            name="payment terms remarks"
                            type="text"
                            containerClassName="orgInputContainer form__input-row__input"
                            inputClassName="orgFormInput"
                            placeholder="Additional remarks on payment terms.."
                            onChange={(event) =>
                              this.setState({ remark: event.target.value })
                            }
                            maxlength="100"
                            value={remark}
                            disabled={
                              createSOLoading ||
                              updateSOLoading ||
                              getDocConfigSOLoading
                            }
                          />
                        </div>
                      </div>
                      {isEstimate && (
                        <div className="ant-col-md-6">
                          <div className="form__input-row orgInputContainer">
                            <H3Text
                              text={`${documentUINames.estimateUIName} Expiry Date`}
                              className="form__input-row__label"
                            />
                            <div className="form__input-row__input">
                              <DatePicker
                                value={estimateExpiryDate}
                                onChange={(value) => {
                                  this.setState({ estimateExpiryDate: value });
                                }}
                                style={{
                                  border: '1px solid rgba(68, 130, 218, 0.2)',
                                  borderRadius: '2px',
                                  height: '28px',
                                  padding: '1px 3px',
                                  width: '100%',
                                  background: 'white',
                                }}
                                disabled={
                                  createSOLoading ||
                                  updateSOLoading ||
                                  getDocConfigSOLoading
                                }
                                format="DD/MM/YYYY"
                                placeholder="Select expiry date"
                              />
                            </div>
                          </div>
                        </div>
                      )}
                      <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <div style={{ display: 'flex' }}>
                            <H3Text
                              text="Sales Manager"
                              className="form__input-row__label"
                            />
                            <div className="form__input-info">
                              <SettingsInfo
                                // settingName="Restrict Sales Orders of Other Account Managers"
                                settingName={`Restrict ${documentUINames.salesOrderUIName}s of Other Account Managers`}
                                path="/admin/configuration?tab=%2Fadmin-config%2Fsales-order&subTab=%2Fadmin-config%2FSALES_ORDER%2Fgeneral"
                                customStyle={{ marginTop: '4px' }}
                              />
                            </div>
                          </div>
                          <div className="form__input-row__input">
                            <SelectAppUser
                              hideTitle
                              containerClassName="orgInputContainer"
                              labelClassName="orgFormLabel"
                              inputClassName="orgFormInput"
                              selectedUser={accountManager}
                              onChange={(value) =>
                                this.setState({ accountManager: value })
                              }
                              disabled={
                                createSOLoading ||
                                updateSOLoading ||
                                getDocConfigSOLoading ||
                                restrictAccManager
                              }
                            />
                          </div>
                        </div>
                      </div>
                      {user?.tenant_info?.sales_config?.addons?.price_list
                        ?.is_active && (
                          <div className="ant-col-md-6">
                            <div
                              className="form__input-row"
                              style={{ marginBottom: '10px' }}
                            >
                              <H3Text
                                required={isEstimate ? false : isPriceListMandatory}
                                text="Price List"
                                className="form__input-row__label"
                              />
                              <div className='form__input-row__input'>
                                <SelectPriceList
                                  selectedPriceList={selectedPriceList}
                                  hideTitle
                                  allowClear
                                  onChange={(value) => {
                                    this.setState({
                                      selectedPriceList: value?.price_list_id,
                                    });
                                    if (productSkuIds?.length > 0) {
                                      getApplyPriceLists(
                                        selectedTenant ||
                                        selectedSO?.tenant_id ||
                                        user?.tenant_info?.tenant_id,
                                        value?.price_list_id,
                                        selectedCustomer,
                                        shippingAddress?.state,
                                        productSkuIds,
                                        (toApplyPriceList) => {
                                          this.setState({
                                            pricesToApply: toApplyPriceList,
                                          });
                                          const copyData = JSON.parse(
                                            JSON.stringify(data)
                                          );
                                          for (
                                            let i = 0;
                                            i <= toApplyPriceList?.length;
                                            i++
                                          ) {
                                            for (
                                              let j = 0;
                                              j <= copyData?.length;
                                              j++
                                            ) {
                                              const copyDataItem = copyData[j];
                                              if (
                                                toApplyPriceList[i]
                                                  ?.product_sku_id ===
                                                copyDataItem?.product_sku_id &&
                                                (toApplyPriceList[i]
                                                  ?.product_sku_id !==
                                                  undefined ||
                                                  copyDataItem?.product_sku_id !==
                                                  undefined)
                                              ) {
                                                copyDataItem.unitPrice =
                                                  toApplyPriceList[i]
                                                    ?.is_inclusive_of_tax
                                                    ? toApplyPriceList[i]
                                                      ?.price_list_amount /
                                                    ((100 +
                                                      copyDataItem?.taxInfo
                                                        ?.tax_value) /
                                                      100)
                                                    : toApplyPriceList[i]
                                                      ?.price_list_amount;
                                                copyDataItem.discount =
                                                  toApplyPriceList[
                                                    i
                                                  ]?.discount_percentage;
                                                copyData[j] = copyDataItem;
                                                break;
                                              }
                                            }
                                          }
                                          this.setState({ data: copyData });
                                        }
                                      );
                                    }
                                  }}
                                  customerId={selectedCustomer}
                                  tenantId={
                                    selectedTenant || selectedSO?.tenant_id
                                  }
                                  state={shippingAddress?.state}
                                  disabled={
                                    createSOLoading ||
                                    updateSOLoading ||
                                    getDocConfigSOLoading ||
                                    !selectedCustomer ||
                                    !billingAddress ||
                                    !shippingAddress
                                  }
                                  containerClass={(formSubmitted && !selectedPriceList && isPriceListMandatory && !isEstimate) ? 'form__input-row__input-error' : 'form__input-row price_list_select_container'}
                                />
                              </div>
                            </div>
                          </div>
                        )}
                      <div className="ant-col-md-6">
                        <div className="form__input-row">
                          <div className="form__input-row__label">Labels</div>
                          <TagSelector
                            hideTitle
                            entityType="SALES_ORDER"
                            selectedTags={selectedTags}
                            isMultiple
                            showSearch
                            onChange={(value) => {
                              this.setState({ selectedTags: value });
                            }}
                            maxTagCount="responsive"
                            placeholder="Select Tags"
                            isForm
                            containerWrapper="form__input-row__input"
                            disabled={
                              createSOLoading ||
                              updateSOLoading ||
                              getDocConfigSOLoading
                            }
                          />
                        </div>
                      </div>
                      <CustomFieldV3
                        customFields={cfSalesOrderDoc}
                        formSubmitted={formSubmitted}
                        customInputChange={(value, cfId) =>
                          this.customInputChange(value, cfId)
                        }
                        wrapperClassName="ant-col-md-6"
                        containerClassName="form__input-row"
                        labelClassName="form__input-row__label"
                        inputClassName="form__input-row__input"
                        errorClassName="form__input-row__input-error"
                        disableCase={
                          createSOLoading ||
                          updateSOLoading ||
                          getDocConfigSOLoading
                        }
                        hideTitle
                        isCarryForward={true}
                      />
                    </div>
                  </div>
                </div>
                <div className="form__section">
                  <div className="flex-display flex-align-c mg-bottom-5 pd-right-15">
                    <H3Text text="PART B" className="form__section-title" />
                    <div className="form__section-line" />
                  </div>
                  <div className="ant-row">
                    <div className="ant-col-md-8">
                      <div className="form__input-row">
                        <H3Text
                          required={
                            isEstimate
                              ? user?.tenant_info?.sales_config?.sub_modules
                                ?.sales_estimate?.settings
                                ?.is_shipping_address_mandatory
                              : true
                          }
                          text="Shipping Address"
                          className="form__input-row__label"
                        />
                        <div
                          className={`form__input-row__address__wrapper ${formSubmitted &&
                            !shippingAddress &&
                            isEstimate &&
                            user?.tenant_info?.sales_config?.sub_modules
                              ?.sales_estimate?.settings
                              ?.is_shipping_address_mandatory
                            ? 'form__input-row__address-error'
                            : ''
                            } ${!selectedCustomer
                              ? 'form__input-row__address-disabled'
                              : ''
                            }`}
                        >
                          <div className="form__input-row__address">
                            {shippingAddress && (
                              <div className="form__input-row__address-info">
                                <div className="form__input-row__address-l1">
                                  {shippingAddress?.address1}
                                </div>
                                <div className="form__input-row__address-l2">
                                  {`${shippingAddress?.city}, ${shippingAddress?.state}, ${shippingAddress?.postal_code}, ${shippingAddress?.country}`}
                                </div>
                              </div>
                            )}
                            {!shippingAddress && (
                              <H3Text
                                text="Select address.."
                                className="form__input-row__address-placeholder"
                              />
                            )}
                            <div
                              className="form__input-row__address-icon"
                              onClick={() =>
                                this.setState({
                                  selectedAddressType: 'TENANT_SHIPPING',
                                  showAddressDrawer: true,
                                })
                              }
                            >
                              <EditFilled /> Update
                            </div>
                          </div>
                          {formSubmitted && !shippingAddress && (
                            <div className="input-error">
                              *Please select shipping address
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="ant-col-md-8">
                      <div className="form__input-row">
                        <H3Text
                          required={
                            isEstimate
                              ? user?.tenant_info?.sales_config?.sub_modules
                                ?.sales_estimate?.settings
                                ?.is_billing_address_mandatory
                              : true
                          }
                          text="Billing Address"
                          className="form__input-row__label"
                        />
                        <div
                          className={`form__input-row__address__wrapper ${formSubmitted &&
                            !billingAddress &&
                            isEstimate &&
                            user?.tenant_info?.sales_config?.sub_modules
                              ?.sales_estimate?.settings
                              ?.is_billing_address_mandatory
                            ? 'form__input-row__address-error'
                            : ''
                            } ${!selectedCustomer
                              ? 'form__input-row__address-disabled'
                              : ''
                            }`}
                        >
                          {' '}
                          <div className="form__input-row__address">
                            {billingAddress && (
                              <div className="form__input-row__address-info">
                                <div className="form__input-row__address-l1">
                                  {billingAddress?.address1}
                                </div>
                                <div className="form__input-row__address-l2">
                                  {`${billingAddress?.city}, ${billingAddress?.state} ${billingAddress?.postal_code}, ${billingAddress?.country}`}
                                </div>
                              </div>
                            )}
                            {!billingAddress && (
                              <H3Text
                                text="Select address.."
                                className="form__input-row__address-placeholder"
                              />
                            )}
                            <div
                              className="form__input-row__address-icon"
                              onClick={() =>
                                this.setState({
                                  selectedAddressType: 'TENANT_BILLING',
                                  showAddressDrawer: true,
                                })
                              }
                            >
                              <EditFilled /> Update
                            </div>
                          </div>
                          {formSubmitted && !billingAddress && (
                            <div className="input-error">
                              *Please select billing address
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    {user?.tenant_info?.integration_config?.sub_modules
                      ?.whatsapp?.is_active && (
                        <div className="ant-col-md-24">
                          <div className="form__input-row">
                            <Checkbox
                              disabled={
                                createSOLoading ||
                                updateSOLoading ||
                                getDocConfigSOLoading
                              }
                              checked={sendWhatsappNotification}
                              onChange={() => {
                                this.setState({
                                  sendWhatsappNotification:
                                    !sendWhatsappNotification,
                                });
                              }}
                            />
                            <span
                              style={{
                                fontWeight: '500',
                                fontSize: '12px',
                                marginLeft: '5px',
                              }}
                            >
                              Send automatic whatsapp notification
                            </span>
                          </div>
                        </div>
                      )}
                  </div>
                </div>
              </div>
            </div >

            <div className="form__lines-wrapper">
              {tenantDepartmentId && (
                <SoLines
                  title={() => (
                    <div className="form-title__wrapper">
                      <H3Text
                        text={`${documentUINames.uiName} Products (${data.filter(
                          (item) => item.product_sku_name?.length > 0
                        )?.length
                          })`}
                        className="form__input-row__label"
                      />
                      <div className="form-title-left">
                        <Checkbox
                          checked={!isLineWiseDiscount}
                          onChange={() => {
                            const copyData = JSON.parse(JSON.stringify(data));
                            copyData.map((i) => {
                              i.discount = 0;
                              i.unitDiscount = 0;
                              i.showUnitDiscount = false;
                              i.lineDiscountType = 'Percent';
                              i.child_taxes = Helpers.computeTaxation(i?.quantity * i?.unitPrice, i?.taxInfo, user?.tenant_info?.state, billingAddress?.state)?.tax_info?.child_taxes;
                            });
                            this.setState({
                              isLineWiseDiscount: !isLineWiseDiscount,
                              discountPercentage: 0,
                              data: copyData,
                              discountType: 'Percent',
                            });
                          }}
                          disabled={
                            getSOByIdLoading ||
                            createSOLoading ||
                            updateSOLoading ||
                            getDocConfigSOLoading ||
                            getProductByIdLoading ||
                            getOrgStatusSalesOrderLoading
                          }
                        />
                        <div style={{ margin: '0px 10px 0px 5px' }}>
                          {isEstimate
                            ? 'Estimate Level Discount'
                            : 'Order Level Discount'}
                        </div>
                        {!selectedOrder && (
                          <div className="so-lines__bulk-upload">
                            <BulkUploadSo
                              disabled={!billingAddress || !selectedCustomer}
                              onBulkUpload={(updatedData, importedData) =>
                                this.onBulkUpload(updatedData, importedData)
                              }
                              entityType="sales_order"
                              customClass="custom-doc-columns__wrapper"
                            />
                          </div>
                        )}
                        &nbsp; &nbsp;
                        <CustomDocumentColumns
                          visibleColumns={visibleColumns}
                          setVisibleColumns={(val) =>
                            this.setState({ visibleColumns: val })
                          }
                          customColumns={cfSalesOrderLine}
                          data={data}
                          updateData={(updatedData) =>
                            this.setState({ data: updatedData })
                          }
                        />
                      </div>
                    </div>
                  )}
                  isLineWiseDiscount={isLineWiseDiscount}
                  handleDelete={this.handleDelete}
                  handleProductChange={this.handleProductChange}
                  handleProductChangeValue={this.handleProductChangeValue}
                  data={this.getDataSource(data)}
                  addNewRow={() => this.addNewRow()}
                  updateData={(updatedData) =>
                    this.setState({ data: updatedData })
                  }
                  formSubmitted={formSubmitted}
                  loading={
                    getSOByIdLoading ||
                    createSOLoading ||
                    updateSOLoading ||
                    getDocConfigSOLoading ||
                    getProductByIdLoading || getOrgStatusSalesOrderLoading
                  }
                  tenantDepartmentId={tenantDepartmentId}
                  pricesToApply={pricesToApply}
                  selectedCurrencyName={selectedCurrencyName}
                  tenantId={selectedTenant || selectedSO?.tenant_id}
                  selectedCustomer={selectedCustomer}
                  billFromState={user?.tenant_info?.state}
                  billToState={billingAddress?.state}
                  visibleColumns={visibleColumns}
                  cfSalesOrderLine={cfSalesOrderLine}
                  customLineInputChange={(value, key, rate) =>
                    this.customLineInputChange(value, key, rate)
                  }
                  isEstimate={isEstimate}
                  isCarryForward={isCarryForward}
                  handleMultiProductChange={this.handleMultiProductChange}
                />
              )}
            </div>

            <div className="form__data-wrapper">
              <div className="ant-row">
                <div className="ant-col-md-24">
                  <div
                    className={`new-row-button ${!!data?.find((item) => !(item?.product_sku_id || item?.product_sku_info?.product_sku_id)) ? 'new-row-button__disabled' : ''}`}
                    onClick={() => {
                      if (!data?.find((item) => !(item?.product_sku_id || item?.product_sku_info?.product_sku_id))) this.addNewRow()
                    }}
                  >
                    <span className="new-row-button__icon">
                      <PlusCircleFilled />
                    </span>
                    <div>New Item</div>
                  </div>
                </div>
                <div className="ant-col-md-12">
                  <div className="form__data-tc">
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <label className="orgFormLabel" style={{ marginRight: 8 }}>
                        Terms and Conditions
                      </label>
                      {salesOrderType === 'SALES_ORDER' && <TnCSelector
                        entityName="SALES_ORDER"
                        selectedTnC={termsAndConditions}
                        updateTnC={(value) => this.handleChangeTextArea(value)}
                        disabled={
                          createSOLoading ||
                          updateSOLoading ||
                          getDocConfigSOLoading
                        }
                        tenantId={selectedTenant || selectedSO?.tenant_id}
                        updateTncId={(value) => this.setState({ tncId: value })}
                        tncId={tncId}
                      />}
                    </div>
                    <RichTextEditor
                      onChange={(value) => this.handleChangeTextArea(value)}
                      value={termsAndConditions}
                      disabled={
                        createSOLoading ||
                        updateSOLoading ||
                        getDocConfigSOLoading
                      }
                    />
                  </div>
                  <div className="form__data-attachment">
                    <label className="orgFormLabel">Attachment(s)</label>
                    <Upload
                      action={Constants.UPLOAD_FILE}
                      listType="picture-card"
                      fileList={fileList}
                      disabled={
                        createSOLoading ||
                        updateSOLoading ||
                        getDocConfigSOLoading
                      }
                      multiple
                      onChange={(fileListData) => {
                        this.setState({
                          fileList: fileListData?.fileList?.map((item) => ({
                            ...item,
                            url:
                              item?.response?.response?.location || item?.url,
                          })),
                        });
                      }}
                    >
                      {fileList?.length >= 20 ? null : uploadButton}
                    </Upload>
                  </div>
                </div>
                <div className="ant-col-md-12">
                  <div className="form-calculator__wrapper">
                    <div className="form-calculator">
                      <div className="form-calculator__field">
                        <H3Text
                          text="Sub Total"
                          className="form-calculator__field-name"
                        />
                        <H3Text
                          text={MONEY(
                            this.getLineTotals().totalAmount,
                            selectedCurrencyName?.currency_code
                          )}
                          className="form-calculator__field-value"
                          hideText={
                            isDataMaskingPolicyEnable && isHideSellingPrice
                          }
                          popOverMessage={
                            'You don\'t have access to view sub total'
                          }
                        />
                      </div>
                      {isLineWiseDiscount && (
                        <div className="form-calculator__field">
                          <H3Text
                            text="Discount"
                            className="form-calculator__field-name"
                          />
                          <H3Text
                            text={MONEY(
                              this.getLineTotals().totalDiscount,
                              selectedCurrencyName?.currency_code
                            )}
                            className="form-calculator__field-value"
                            hideText={
                              isDataMaskingPolicyEnable && isHideSellingPrice
                            }
                            popOverMessage={
                              'You don\'t have access to view discount amount'
                            }
                          />
                        </div>
                      )}
                      {!isLineWiseDiscount && (
                        <div className="form-calculator__field">
                          <H3Text
                            text="Discount"
                            className="form-calculator__field-name"
                          />
                          <div
                            className="form-calculator__field-value"
                            style={{ display: 'flex' }}
                          >
                            <div style={{ width: '112px' }}>
                              <H3FormInput
                                value={discountPercentage}
                                type="number"
                                containerClassName={`${formSubmitted &&
                                  Number(discountPercentage) <= 0
                                  ? 'form-error__input'
                                  : ''
                                  }`}
                                labelClassName="orgFormLabel"
                                inputClassName="orgFormInput"
                                onChange={(e) => {

                                  const copyData = JSON.parse(JSON.stringify(data));
                                  const totalValue = copyData?.reduce((acc, cur) => acc + (cur?.quantity * cur?.unitPrice), 0);
                                  copyData.map((item) => {

                                    const discountValue = discountType === 'Percent' ?
                                      parseFloat(e.target.value || 0) :
                                      ((item?.quantity * item?.unitPrice) / parseFloat(totalValue)) * parseFloat(e.target.value || 0);

                                    const taxableValue = discountType === 'Percent' ?
                                      (item?.quantity * item?.unitPrice) * (1 - discountValue / 100) :
                                      Math.max(item?.quantity * item?.unitPrice - discountValue, 0);

                                    item.discount = discountValue;
                                    item.child_taxes = Helpers.computeTaxation(taxableValue, item.taxInfo, user?.tenant_info?.state, billingAddress?.state)?.tax_info?.child_taxes;
                                  });
                                  this.setState({
                                    data: copyData,
                                    discountPercentage: parseFloat(e.target.value || 0),
                                  });
                                }}
                              />
                            </div>
                            <div className="form-calculator__discount-type">
                              <PRZSelect
                                value={discountType}
                                onChange={(value) => {

                                  const copyData = JSON.parse(JSON.stringify(data));
                                  copyData.map((item) => {

                                    const totalValue = copyData?.reduce((acc, cur) => acc + (cur.quantity * cur.unitPrice), 0);
                                    const discountValue = value === 'Percent' ? Number(discountPercentage) : ((item.quantity * item.unitPrice) / parseFloat(totalValue)) * Number(discountPercentage);

                                    const taxableValue = value === 'Percent' ? (item?.quantity * item?.unitPrice) * (1 - discountValue / 100) : Math.max(item.quantity * item?.unitPrice - discountValue, 0);

                                    item.discount = discountValue;
                                    item.lineDiscountType = value;
                                    item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, user?.tenant_info?.state, billingAddress?.state)?.tax_info?.child_taxes;
                                  });
                                  this.setState({
                                    data: copyData,
                                    discountType: value,
                                  });
                                }}
                              >
                                <Option key="Amount" value="Amount">
                                  {`${selectedCurrencyName?.currency_symbol}`}
                                </Option>
                                <Option key="Percent" value="Percent">
                                  %
                                </Option>
                              </PRZSelect>
                            </div>
                          </div>
                        </div>
                      )}

                      <div className="form-calculator__field">
                        <div
                          className="form-calculator__field-name"
                          style={{ display: 'flex', alignItems: 'center' }}
                        >
                          <H3Text
                            text="Freight"
                            style={{ marginRight: '10px' }}
                          />
                          {freightTax && (
                            <div
                              style={{
                                color: '#2d7df7',
                                fontWeight: '400',
                                fontSize: '12px',
                              }}
                            >{`tax@${freightTax}%`}</div>
                          )}
                          {/* <Checkbox disabled={!(charge1Value > 0)} onChange={(e) => { this.setState({ createExpenseCheckbox: e.target.checked }); }} checked={createExpenseCheckbox}><span style={{ color: '#2d7df7', fontWeight: '400', fontSize: '12px' }}>+ Add To Expense</span></Checkbox> */}
                        </div>
                        <div
                          className="form-calculator__field-value"
                          style={{ display: 'flex' }}
                        >
                          <div style={{ width: '112px' }}>
                            <H3FormInput
                              value={charge1Value}
                              type="number"
                              containerClassName={`${formSubmitted && Number(charge1Value) < 0
                                ? 'form-error__input'
                                : ''
                                }`}
                              labelClassName="orgFormLabel"
                              inputClassName="orgFormInput"
                              onChange={(e) => {
                                this.setState({
                                  charge1Value: e.target.value,
                                  freightTaxData: {
                                    ...freightTaxData,
                                    child_taxes: Helpers.computeTaxation(e.target.value, freightTaxInfo, user?.tenant_info?.state, billingAddress?.state)?.tax_info?.child_taxes
                                  },
                                });
                              }}
                            />
                          </div>
                          <FreightTaxInput
                            freightTaxId={freightTaxId}
                            freightTax={freightTax}
                            openFreightTax={openFreightTax}
                            sacCode={freightSacCode}
                            setOpenFreightTax={(value) =>
                              this.setState({ openFreightTax: value })
                            }
                            setFreightTaxData={(value) =>
                              this.setState({
                                freightTaxId: !value
                                  ? 'Not Applicable'
                                  : value?.tax_id,
                                freightTax: !value ? null : value?.tax_value,
                                freightTaxInfo: !value ? null : value,
                                freightTaxData: !value ? {
                                  child_taxes: [
                                    {
                                      tax_amount: 0,
                                      tax_type_name: '',
                                    },
                                  ],
                                } : {
                                  ...value,
                                  child_taxes: Helpers.computeTaxation(charge1Value, value, user?.tenant_info?.state, billingAddress?.state)?.tax_info?.child_taxes
                                },
                              })
                            }
                            setSacCode={(value) =>
                              this.setState({ freightSacCode: value })
                            }
                          />
                        </div>
                      </div>

                      {renderCharges(splitChargesData(chargeData)?.chargeWithTaxName)}

                      <div className="form-calculator__field">
                        <H3Text
                          text="Taxable Amount"
                          className="form-calculator__field-name"
                        />
                        <H3Text
                          text={MONEY(
                            this.getLineTotals().totalBase,
                            selectedCurrencyName?.currency_code
                          )}
                          className="form-calculator__field-value"
                          hideText={
                            isDataMaskingPolicyEnable && isHideSellingPrice
                          }
                          popOverMessage="You don't have access to view sub total amount"
                        />
                      </div>
                      {data?.[0]?.child_taxes?.[0]?.tax_type_name && Helpers.groupAndSumByTaxName(FormHelpers.childTaxesData([...data, freightTaxData, ...chargeData?.flatMap((charge) => charge?.chargesTaxData)]))?.map((taxType, i) => (<Fragment>
                        <div className="form-calculator__field">
                          <H3Text
                            text={taxType?.tax_type_name}
                            className="form-calculator__field-name"
                          />
                          <H3Text
                            text={MONEY(
                              taxType?.tax_amount || '0',
                              selectedCurrencyName?.currency_code
                            )}
                            className="form-calculator__field-value"
                            hideText={
                              isDataMaskingPolicyEnable &&
                              isHideSellingPrice
                            }
                            popOverMessage={`You don't have access to view ${taxType?.tax_type_name?.toLowerCase()}`}
                          />
                        </div>
                      </Fragment>
                      ))}

                      {createExpenseCheckbox && (
                        <TransporterForm
                          transporterId={transporterId}
                          vehicleNumber={vehicleNumber}
                          transporterBillNumber={transporterBillNumber}
                          setTransporterId={(val) =>
                            this.setState({ transporterId: val })
                          }
                          setVehicleNumber={(val) =>
                            this.setState({ vehicleNumber: val })
                          }
                          setTransporterBillNumber={(val) =>
                            this.setState({ transporterBillNumber: val })
                          }
                        />
                      )}
                      {renderCharges(splitChargesData(chargeData)?.chargeWithoutTaxName)}
                      <div
                        className="new-charge-row-button"
                        onClick={() => this.addNewChargesRow()}
                      >
                        <span className="new-charge-row-button__icon">
                          <PlusCircleFilled />
                        </span>
                        <div>Add Charges</div>
                      </div>
                      {roundOffMethod !== 'NO_ROUND_OFF' && (
                        <div className="form-calculator__field">
                          <H3Text
                            text="Round Off"
                            className="form-calculator__field-name"
                          />
                          <Tooltip
                            title={`Round Off method for ${documentUINames.shortUIName} is set to ${roundOffMethod?.replace(/_/g, ' ')?.toProperCase()}`}
                          >
                            <div style={{ cursor: 'pointer', }}>
                              <FontAwesomeIcon icon={faCircleInfo} size='lg' style={{ color: '#2D7DF7', }} />
                            </div>
                          </Tooltip>
                          <H3Text
                            text={`${Helpers.configuredRoundOff(this.getLineTotals().orderTotal, roundOffMethod)?.roundOff < 0 ? '(-) ' : ''}${MONEY(
                              Math.abs(Helpers.configuredRoundOff(this.getLineTotals().orderTotal, roundOffMethod)?.roundOff),
                              selectedCurrencyName?.currency_code
                            )}`}
                            className="form-calculator__field-value"
                            hideText={
                              isDataMaskingPolicyEnable && isHideSellingPrice
                            }
                            popOverMessage={
                              'You don\'t have access to view sub total'
                            }
                          />
                        </div>
                      )}
                      <div className="form-calculator__field form-calculator__field-total">
                        <H3Text
                          // text={isEstimate ? 'Estimate Total' : 'Order Total'}
                          text={`${documentUINames.shortUIName} Total`}
                          className="form-calculator__field-name"
                        />
                        <H3Text
                          text={MONEY(
                            Helpers.configuredRoundOff(this.getLineTotals().orderTotal, roundOffMethod)?.value,
                            selectedCurrencyName?.currency_code
                          )}
                          className="form-calculator__field-value"
                          hideText={
                            isDataMaskingPolicyEnable && isHideSellingPrice
                          }
                          popOverMessage={`You don't have access to view ${documentUINames.shortUIName?.toLowerCase()} total`}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div >
        )
        }
        <div className={`form__footer ${userMenuConfig === 'FIXED' ? 'form__footer-fixed' : ''}`}>
          {(match?.url?.includes('/update')) ? (
            <Fragment>
              <PRZConfirmationPopover
                title="Are you sure you want to update?"
                content={
                  <Fragment>
                    <PRZText text="Reason" required />
                    <PRZInput
                      placeholder="Enter update reason"
                      value={updateDocumentReason}
                      onChange={(e) => this.setState({ updateDocumentReason: e.target.value })}
                    />
                  </Fragment>
                }
                onConfirm={() => {
                  this.setState({ currentAction: 'SAVE_AS_DRAFT' });
                  if (!createSOLoading) {
                    this.createSalesOrder(false);
                  }
                }}
                confirmButtonText="Confirm"
                cancelButtonText="Back"
                confirmDisabled={!updateDocumentReason}
              >
                <PRZButton
                  id="save-as-draft"
                  type="default"
                  wrapperStyle={{ marginRight: '10px' }}
                  buttonStyle={{
                    width: '130px',
                    height: '40px',
                    border: '1px solid #2d7df7',
                    color: '#2d7df7',
                  }}
                  isLoading={
                    currentAction === 'SAVE_AS_DRAFT' &&
                    ((!withApproval && createSOLoading) ||
                      updatePoStatusLoading ||
                      updateSOLoading ||
                      getDocConfigSOLoading ||
                      getOrgStatusSalesOrderLoading)
                  }
                  disabled={
                    updatePoStatusLoading ||
                    getOrgStatusSalesOrderLoading
                  }
                >
                  Save as Draft
                </PRZButton>
              </PRZConfirmationPopover>
              {(selectedSO ? selectedSO?.status !== 'VOID' : true) &&
                this.getDraftStatusType()?.length === 1 && (
                  <PRZConfirmationPopover
                    title="Are you sure you want to update?"
                    content={
                      <Fragment>
                        <PRZText text="Reason" required />
                        <PRZInput
                          placeholder="Enter update reason"
                          value={updateDocumentReason}
                          onChange={(e) => this.setState({ updateDocumentReason: e.target.value })}
                        />
                      </Fragment>
                    }
                    onConfirm={() => {
                      this.setState({ currentAction: 'SAVE_AND_ISSUED' });
                      if (!createSOLoading) {
                        this.createSalesOrder(true);
                      }
                    }}
                    confirmButtonText="Confirm"
                    cancelButtonText="Back"
                    confirmDisabled={!updateDocumentReason}
                  >
                    <PRZButton
                      id="save-and-issue"
                      isLoading={
                        currentAction === 'SAVE_AND_ISSUED' &&
                        ((withApproval && createSOLoading) ||
                          updatePoStatusLoading ||
                          updateSOLoading ||
                          getDocConfigSOLoading ||
                          getOrgStatusSalesOrderLoading)
                      }
                      disabled={
                        createSOLoading ||
                        updatePoStatusLoading ||
                        getOrgStatusSalesOrderLoading
                      }
                      buttonStyle={{ width: '130px', height: '40px' }}
                    >
                      Save and Issue
                    </PRZButton>
                  </PRZConfirmationPopover>
                )}
            </Fragment>
          ) : (
            <Fragment>
              <PRZButton
                id="save-as-draft"
                type="default"
                onClick={() => {
                  this.setState({ currentAction: 'SAVE_AS_DRAFT' });
                  if (!createSOLoading) {
                    this.createSalesOrder(false);
                  }
                }}
                isLoading={
                  currentAction === 'SAVE_AS_DRAFT' &&
                  ((!withApproval && createSOLoading) ||
                    updatePoStatusLoading ||
                    updateSOLoading ||
                    getDocConfigSOLoading ||
                    getOrgStatusSalesOrderLoading)
                }
                disabled={
                  createSOLoading ||
                  updatePoStatusLoading ||
                  getOrgStatusSalesOrderLoading
                }
                wrapperStyle={{ marginRight: '10px' }}
                buttonStyle={{
                  width: '130px',
                  height: '40px',
                  border: '1px solid #2d7df7',
                  color: '#2d7df7',
                }}
              >
                Save as Draft
              </PRZButton>
              {(selectedSO ? selectedSO?.status !== 'VOID' : true) &&
                this.getDraftStatusType()?.length === 1 && (
                  <PRZButton
                    onClick={() => {
                      this.setState({ currentAction: 'SAVE_AND_ISSUED' });
                      if (!createSOLoading) {
                        this.createSalesOrder(true);
                      }
                    }}
                    isLoading={
                      currentAction === 'SAVE_AND_ISSUED' &&
                      ((withApproval && createSOLoading) ||
                        updatePoStatusLoading ||
                        updateSOLoading ||
                        getDocConfigSOLoading ||
                        getOrgStatusSalesOrderLoading)
                    }
                    disabled={
                      createSOLoading ||
                      updatePoStatusLoading ||
                      getOrgStatusSalesOrderLoading
                    }
                    buttonStyle={{ width: '130px', height: '40px' }}
                  >
                    Save and Issue
                  </PRZButton>
                )}
            </Fragment>
          )}
          {isDataMaskingPolicyEnable &&
            (isHideCostPrice || isHideSellingPrice) && (
              <RestrictedAccessMessage
                message={'You don\'t have access to view or edit rate'}
              />
            )}

          <div className="form-barcode__wrapper" style={{ display: 'flex' }}>
            {!barCoding && (
              <Popconfirm
                placement="topRight"
                title="This feature is not available in your current plan"
                onConfirm={() => window.Intercom('showNewMessage')}
                okText="Contact Us"
                cancelText="Cancel"
              >
                <img className="barcode-restrict" src={Crown} alt="premium" />
              </Popconfirm>
            )}
            <BarcodeReader
              productTypes={['STORABLE', 'SERVICE', 'BUNDLE', 'NON_STORABLE']}
              isSalesProduct
              excludedProducts={
                data?.map((item) => JSON.stringify(item?.product_sku_id)) || []
              }
              tenantDepartmentId={tenantDepartmentId}
              excludeOutOfStock
              filterReservedQuantity
              onSearch={(tenantSku) => {
                if (data[data?.length - 1]?.product_sku_id) {
                  this.addNewRow((key) =>
                    this.handleProductChange(tenantSku, key)
                  );
                } else {
                  this.handleProductChange(
                    tenantSku,
                    data[data?.length - 1]?.key
                  );
                }
              }}
              disabled={!barCoding}
            />
          </div>
        </div>
      </Fragment >
    );
  }
}

const mapStateToProps = ({
  UserReducers,
  SOReducers,
  PurchaseOrderReducers,
  CustomerReducers,
  ProductReducers,
  CFV2Reducers,
  CustomStatusReducers,
  DocConfigReducers,
  CurrenciesReducers,
  TagReducers,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  createSOLoading: SOReducers.createSOLoading,
  updateSOLoading: SOReducers.updateSOLoading,
  getSOByIdLoading: SOReducers.getSOByIdLoading,
  selectedSO: SOReducers.selectedSO,
  getDocCFV2Loading: CFV2Reducers.getDocCFV2Loading,
  cfV2DocSalesOrder: CFV2Reducers.cfV2DocSalesOrder,
  cfV2DocSalesEstimate: CFV2Reducers.cfV2DocSalesEstimate,
  cfV2DocAnnualMaintenanceContract: CFV2Reducers.cfV2DocAnnualMaintenanceContract,
  selectedProduct: ProductReducers.selectedProduct,
  getProductByIdLoading: ProductReducers.getProductByIdLoading,
  getCustomersLoading: CustomerReducers.getCustomersLoading,
  getCustomerByIdLoading: CustomerReducers.getCustomerByIdLoading,
  orgStatusSalesOrder: CustomStatusReducers.orgStatusSalesOrder,
  getOrgStatusSalesOrderLoading:
    CustomStatusReducers.getOrgStatusSalesOrderLoading,
  docConfigSO: DocConfigReducers.docConfigSO,
  getDocConfigSOLoading: DocConfigReducers.getDocConfigSOLoading,
  CurrenciesResults: CurrenciesReducers.CurrenciesResults,
  getCurrenciesLoading: CurrenciesReducers.getCurrenciesLoading,
  createTagLoading: TagReducers.createTagLoading,
  priceMasking: UserReducers.priceMasking,
});

const mapDispatchToProps = (dispatch) => ({
  getCustomers: (keyword, tenantId, page, limit, customerId, callback) =>
    dispatch(
      CustomerActions.getCustomers(
        keyword,
        tenantId,
        page,
        limit,
        customerId,
        callback
      )
    ),
  createSO: (payload, callback) =>
    dispatch(SOActions.createSO(payload, callback)),
  updateSO: (payload, callback) =>
    dispatch(SOActions.updateSO(payload, callback)),
  getSOById: (payload, callback) =>
    dispatch(SOActions.getSOById(payload, callback)),
  getTenantSkuOffer: (tenantProductId, tenantSellerId, callback) =>
    dispatch(
      OfferActions.getOfferByTenantSku(
        tenantProductId,
        tenantSellerId,
        callback
      )
    ),
  getProductById: (
    tenantId,
    productSkuId,
    tenantDepartmentId,
    callback,
    filterReservedBatches,
    productSkuIds
  ) =>
    dispatch(
      ProductActions.getProductById(
        tenantId,
        productSkuId,
        tenantDepartmentId,
        callback,
        filterReservedBatches,
        productSkuIds
      )
    ),
  getSOByIdSuccess: (salesOrders) =>
    dispatch(SOActions.getSOByIdSuccess(salesOrders)),
  updateSOStatus: (payload, callback) =>
    dispatch(SOActions.updateSOStatus(payload, callback)),
  getDocCFV2Success: (customFields) =>
    dispatch(CFV2Actions.getDocCFV2Success(customFields)),
  getDocCFV2: (payload, callback) =>
    dispatch(CFV2Actions.getDocCFV2(payload, callback)),
  getApplyPriceLists: (
    tenantId,
    priceListId,
    customerId,
    states,
    skuId,
    callback
  ) =>
    dispatch(
      PriceListActions.getApplyPriceLists(
        tenantId,
        priceListId,
        customerId,
        states,
        skuId,
        callback
      )
    ),
  getProductsSuccess: (products, forOffers) =>
    dispatch(ProductActions.getProductsSuccess(products, forOffers)),
  getOrgStatus: (payload, callback) =>
    dispatch(CustomStatusActions.getOrgStatus(payload, callback)),
  getDocConfig: (tenantId, entityName, callback) =>
    dispatch(DocConfigActions.getDocConfig(tenantId, entityName, callback)),
  getCurrencies: (orgId) => dispatch(CurrenciesActions.getCurrencies(orgId)),
  createTag: (payload, callback) =>
    dispatch(TagActions.createTag(payload, callback)),
  getCharges: (orgId, entityName, callback) =>
    dispatch(ExtraChargesActions.getCharges(orgId, entityName, callback)),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withRouter(SalesForm));
