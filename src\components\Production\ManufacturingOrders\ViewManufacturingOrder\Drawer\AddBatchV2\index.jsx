import React, { Component, Fragment } from 'react';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { connect } from 'react-redux';
import Decimal from 'decimal.js';
import {
  DatePicker, Table, Checkbox, Progress, notification,
  Alert,
} from 'antd';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import {
  INFINITE_EXPIRY_DATE, QUANTITY, DEFAULT_CUR_ROUND_OFF,
} from '@Apis/constants';
import H3Button, { defaultButtonTypes } from '@Uilib/h3Button';
import H3Text from '@Uilib/h3Text';
import H3FormInput from '@Uilib/h3FormInput';
import MOActions from '@Actions/moActions';
import BOMActions from '@Actions/bomActions';
import Helpers from '@Apis/helpers';
import UpdateRawMaterial from '../AddBatch/UpdateRawMaterial';
import ByProduct from '../AddBatch/ByProduct';
import OtherCharges from '../AddBatch/OtherCharges';
import FormHelpers from '@Helpers/FormHelpers';
import BatchSelectorForFG from '../../BatchSelectorForFG';
import TemplateRouteLinesV2 from '../../../../../Inventory/ManageProducts/ViewProduct/ProductBOMList/ProductBOM/TemplateRouteLinesV2';
import CustomFieldHelpers from '../../../../../../helpers/CustomFieldHelpers';
import BatchCustomFields from '../../../../../Common/BatchCustomFields';
import PRZModal from '../../../../../Common/UI/PRZModal';
import './style.scss';

class AddBatchV2 extends Component {

  constructor(props) {
    super(props);
    const batchConfig = this.props.org?.organisation?.[0]?.inventory_config?.settings?.batch_management;
    const disableQuantityChange = this.props?.user?.tenant_info?.production_config?.sub_modules?.manufacturing_order?.settings?.disable_quantity_change_in_mo;
    const disablePriceChange = this.props?.user?.tenant_info?.production_config?.sub_modules?.manufacturing_order?.settings?.disable_price_change_in_mo;
    this.state = {
      columns: [
        {
          title: 'Pending',
          render: (record) => {
            const { isFgStep } = this.props;
            let pendingQty = isFgStep ? record?.quantity - record?.completed_quantity : record?.quantity + (record?.wastage_quantity || 0) - (record?.produced_quantity + (record.issued_quantity - record?.consumed_from_reserved_stock));
            return (
              `${QUANTITY(pendingQty > 0 ? pendingQty : 0, record?.tenant_product_info?.uom_info?.precision)} ${record?.tenant_product_info?.uom_info?.uqc?.toProperCase()}`
            );
          },
        },
        {
          title: 'Completion',
          width: '160px',
          render: (record) => {
            const { isFgStep } = this.props;

            return (
              isFgStep ? (
                <div style={{ width: '130px' }}>
                  <Progress size="small" percent={parseInt((record?.completed_quantity / record?.quantity) * 100)} strokeColor={{ from: '#108ee9', to: '#87d068' }} trailColor="#2d7df71a" />
                </div>
              ) : (
                <div style={{ width: '130px' }}>
                  <Progress size="small" percent={parseInt(((record?.produced_quantity + (record.issued_quantity - record?.consumed_from_reserved_stock)) / record?.quantity) * 100)} strokeColor={{ from: '#108ee9', to: '#87d068' }} trailColor="#2d7df71a" />
                </div>
              )
            );
          },
        },
        {
          title: 'Input Quantity',
          width: '120px',
          render: (item) => {
            const { createMOAdjustmentLoading } = this.props;
            const {
              formSubmitted, finishedGoods, rawMaterials, byProducts, extraCharges, totalReadyToProduce, addRmWithoutJobwork,
            } = this.state;
            return (
              <Fragment>
                <H3FormInput
                  value={item.batch_quantity}
                  onChange={(event) => {
                    this.handleTableChange(item.key, 'batch_quantity', event.target.value, (updatedFinishedGoods) => {
                      this.initComponentsAndFg({ totalReadyToProduce, finishedGoods: updatedFinishedGoods, rawMaterials, byProducts, extraCharges, fgQuantity: (event.target.value || 0), updateFgList: false, persistLineLevelGoodStockOnlyKey: true });
                    });
                    this.setState({ batchSize: event.target.value });
                  }}
                  type="number"
                  name=" quantity"
                  labelClassName="add-batch__label"
                  disabled={createMOAdjustmentLoading}
                  inputClassName="orgFormInput fg-batch-input"
                  required
                  showError={formSubmitted && !Number(item.batch_quantity)}
                />
                {
                  addRmWithoutJobwork && item.batch_quantity > totalReadyToProduce && <H3Text className="input-error" text="*Sufficient RM not available" />
                }
              </Fragment>
            );
          },
        },
        {
          title: 'Yield Quantity',
          width: '120px',
          render: (item) => {
            const { createMOAdjustmentLoading, isFgStep } = this.props;
            const {
              formSubmitted, finishedGoods, rawMaterials, byProducts, extraCharges, totalReadyToProduce, addRmWithoutJobwork,
            } = this.state;
            return (
              <Fragment>
                <H3FormInput
                  value={item.actual_output}
                  onChange={(event) => {
                    this.handleTableChange(item.key, 'actual_output', event.target.value, (updatedFinishedGoods) => {
                      this.initComponentsAndFg({ totalReadyToProduce, finishedGoods: updatedFinishedGoods, rawMaterials, byProducts, extraCharges, fgQuantity: (item.batch_quantity || 0), updateFgList: false, persistLineLevelGoodStockOnlyKey: true });
                    });
                  }}
                  type="number"
                  name="yield quantity"
                  labelClassName="add-batch__label"
                  disabled={createMOAdjustmentLoading || disableQuantityChange}
                  inputClassName="orgFormInput fg-batch-input"
                  required
                // showError={formSubmitted && !Number(item.actual_output)}
                />
                <H3Text text={` Default Yield : ${isFgStep ? item?.bom_info?.default_yield_percentage : item?.sfg_bom_info?.default_yield_percentage}% `} className="table-subscript" style={{ marginTop: '5px' }} />
                {/* {
                  addRmWithoutJobwork && item.actual_output > totalReadyToProduce && <H3Text className="input-error" text="*Sufficient RM not available" />
                } */}
              </Fragment>
            );
          },
        },
        {
          title: 'Batch Information',
          width: '250px',
          render: (item) => {
            const {
              createMOAdjustmentLoading, user, selectedMO, isFgStep,
            } = this.props;
            const isBatchNumberEditable = user?.tenant_info?.inventory_config?.settings?.batch_management?.is_batch_number_editable;
            const automaticArNumber = user?.tenant_info?.inventory_config?.settings?.enable_auto_generation_of_ar_number;
            const {
              formSubmitted, batchNumber, batchData, isBatchValid, lotNumber, arNumber, fgBatchCostPrice, sellingPrice,
              mrp, barcode, rollNumber, freightCost, otherCost, landedCost, margin, brand, mfgBatchNumber,
              mfgDate, expiryDate, showLess, inventoryLocation,
            } = this.state;
            return (
              <BatchSelectorForFG
                item={item}
                key={item.key}
                formSubmitted={formSubmitted}
                setFormSubmitted={(value) => this.setState({ formSubmitted: value })}
                grnTableData={batchData}
                updateGrnTableData={(value) => this.setState({ batchData: value })}
                setIsBatchValid={(value) => this.setState({ isBatchValid: value })}
                isBatchValidValue={isBatchValid}
                batchNumber={batchNumber}
                setBatchNumber={(value) => this.setState({ batchNumber: value })}
                disabled={createMOAdjustmentLoading}
                isBatchNumberEditable={!isBatchNumberEditable}
                lotNumber={lotNumber}
                setLotNumber={(value) => this.setState({ lotNumber: value })}
                arNumber={arNumber}
                setArNumber={(value) => this.setState({ arNumber: value })}
                fgBatchCostPrice={fgBatchCostPrice}
                setFgBatchCostPrice={(value) => this.setState({ fgBatchCostPrice: value })}
                sellingPrice={sellingPrice}
                setSellingPrice={(value) => this.setState({ sellingPrice: value })}
                mrp={mrp}
                setMrp={(value) => this.setState({ mrp: value })}
                barcode={barcode}
                setBarcode={(value) => this.setState({ barcode: value })}
                rollNumber={rollNumber}
                setRollNumber={(value) => this.setState({ rollNumber: value })}
                freightCost={freightCost}
                setFreightCost={(value) => this.setState({ freightCost: value })}
                otherCost={otherCost}
                setOtherCost={(value) => this.setState({ otherCost: value })}
                landedCost={landedCost}
                setLandedCost={(value) => this.setState({ landedCost: value })}
                margin={margin}
                setMargin={(value) => this.setState({ margin: value })}
                brand={brand}
                setBrand={(value) => this.setState({ brand: value })}
                mfgBatchNumber={mfgBatchNumber}
                setMfgBatchNumber={(value) => this.setState({ mfgBatchNumber: value })}
                mfgDate={mfgDate}
                setMfgDate={(value) => this.setState({ mfgDate: value })}
                expiryDate={expiryDate}
                setExpiryDate={(value) => this.setState({ expiryDate: value })}
                showLess={showLess}
                setShowLess={() => this.setState({ showLess: !showLess })}
                inventoryLocation={inventoryLocation}
                setInventoryLocation={(value) => this.setState({ inventoryLocation: value })}
                destDepartmentId={isFgStep ? selectedMO?.fg_tenant_department_id : selectedMO?.rm_tenant_department_id}
                disablePriceChange={disablePriceChange}
                manufacturingDateFormat={item?.tenant_product_info?.manufacturing_date_format}
                expiryDateFormat={item?.tenant_product_info?.expiry_date_format}
              />

            );
          },
        },
        {
          title: 'Batch Custom Fields',
          width: '236px',
          render: (item) => {
            const { formSubmitted } = this.state;
            return (
              <Fragment>
                <BatchCustomFields
                  cfWrapperClassName="create-grn__input-row"
                  labelClassName="create-grn__input-row__label"
                  customFields={item?.custom_fields || []}
                  formSubmitted={formSubmitted}
                  customInputChange={(value, cfId) => this.updateBatchCfs(value, cfId, item)}
                  isHorizontalUi
                  hideTitle
                />
              </Fragment>
            );
          },
        },
        {
          title: 'Auto Update',
          width: '160px',
          fixed: 'right',
          render: (item) => {
            const {
              addRmWithoutJobwork, updateByProduct, updateOtherCharges, byProducts, extraCharges, rawMaterials, finishedGoods,
            } = this.state;
            return (
              <Fragment>
                {byProducts?.length > 0 && (
                  <Checkbox
                    checked={updateByProduct}
                    onChange={() => this.setState({ updateByProduct: !updateByProduct })}
                    disabled={!this.props.user?.tenant_info?.production_config?.sub_modules?.manufacturing_order?.settings?.allow_flexible_consumption_in_mo}
                  >
                    By Products
                  </Checkbox>
                )}
                {this.props.isFgStep && extraCharges?.length > 0 && (
                  <Checkbox
                    checked={updateOtherCharges}
                    onChange={() => {
                      this.setState({ updateOtherCharges: !updateOtherCharges });
                      this.fgBatchCostPriceCalculator(rawMaterials, extraCharges, finishedGoods, addRmWithoutJobwork, !updateOtherCharges);
                    }}
                    disabled={!this.props.user?.tenant_info?.production_config?.sub_modules?.manufacturing_order?.settings?.allow_flexible_consumption_in_mo}
                  >
                    Other
                    Charges
                  </Checkbox>
                )}
              </Fragment>
            );
          },
        },
      ].filter((item) => !item.hidden),
      addRmWithoutJobwork: true,
      updateByProduct: true,
      updateOtherCharges: true,
      fgBatchCostPrice: 0,
      showLess: true,
      mfgDate: dayjs(),
      createJobWorks: false,
      addFgWithoutJobwork: true,
      batchSize: 0,
      jobworks: [],
      errors: [],
    };
  }

  componentDidMount() {
    const {
      finishedGood, rawMaterials, byProducts, extraCharges, isFgStep, getBomJobWork, getDocCFV2, user,
    } = this.props;
    const expiryDays = finishedGood?.tenant_product_info?.expiry_days;
    const onHandRatioArray = [];
    for (let i = 0; i < rawMaterials?.length; i++) {
      const availableQty = new Decimal(rawMaterials[i]?.tenant_product_info?.total_available_batches_quantity || 0);
      const finishedGoodQty = new Decimal(finishedGood?.quantity || 0);
      const rawMaterialQty = new Decimal(rawMaterials[i]?.quantity || 0);

      const ratio = rawMaterialQty.equals(0)
        ? new Decimal(0)
        : availableQty.times(finishedGoodQty).dividedBy(rawMaterialQty);

      onHandRatioArray.push(ratio.toNumber());
    }
    const totalReadyToProduce = QUANTITY(onHandRatioArray?.sort((a, b) => a - b)[0], finishedGood?.uom_info?.precision);
    const totalReadyToProduceNoPrecision = onHandRatioArray?.sort((a, b) => a - b)?.[0];
    // const fgQuantity = isFgStep ? Math.min(totalReadyToProduceNoPrecision, finishedGood?.quantity - finishedGood?.completed_quantity) : Math.min(totalReadyToProduceNoPrecision, finishedGood?.quantity + finishedGood?.wastage_quantity - (finishedGood?.produced_quantity + (finishedGood.issued_quantity - finishedGood?.consumed_from_reserved_stock)));

    let fgQuantity;

    if (isFgStep) {
      const totalReady = new Decimal(totalReadyToProduceNoPrecision || 0);
      const remaining = new Decimal(finishedGood?.quantity || 0).minus(finishedGood?.completed_quantity || 0);
      fgQuantity = Decimal.min(totalReady, remaining).toNumber();
    } else {
      const totalReady = new Decimal(totalReadyToProduceNoPrecision || 0);
      const quantity = new Decimal(finishedGood?.quantity || 0);
      const wastageQty = new Decimal(finishedGood?.wastage_quantity || 0);
      const producedQty = new Decimal(finishedGood?.produced_quantity || 0);
      const issuedQty = new Decimal(finishedGood?.issued_quantity || 0);
      const reservedConsumedStock = new Decimal(finishedGood?.consumed_from_reserved_stock || 0);

      const total = quantity.plus(wastageQty).minus(producedQty.plus(issuedQty.minus(reservedConsumedStock)));
      fgQuantity = Decimal.min(totalReady, total).toNumber();
    }

    getBomJobWork({
      bomId: finishedGood?.bom_id,
    }, (bom) => {
      let selectedFgRoutes = bom?.bom_route?.br_lines_sfg_step_level_view?.find((i) => i?.product_sku_info?.product_sku_id === finishedGood?.tenant_product_info?.product_sku_id);
      if (selectedFgRoutes?.br_lines?.length) {
        this.setState({ createJobWorks: true, jobworks: [selectedFgRoutes] });
      } else {
        this.setState({
          createJobWorks: false, jobworks: [{
            br_lines: [],
            brl_sfg_bom_line_id: finishedGood?.bom_line_id,
            brl_sfg_step_number: null,
            is_fg_step: finishedGood?.bom_line_id ? false : true,
            product_sku_info: {
              children_materials: rawMaterials?.map((item) => ({
                bom_line_id: item?.bom_line_id,
                internal_sku_code: item?.tenant_product_info?.internal_sku_code,
                product_sku_id: item?.tenant_product_info?.product_sku_id,
                product_sku_name: item?.tenant_product_info?.product_sku_name,
                quantity: item?.quantity,
                ref_product_code: item?.tenant_product_info?.ref_product_code,
                uom_info: item?.tenant_product_info?.uom_info,
                wastage_percentage: item?.bom_line_info?.wastage_percentage,
              })),
              internal_sku_code: finishedGood?.tenant_product_info?.internal_sku_code,
              product_sku_id: finishedGood?.tenant_product_info?.product_sku_id,
              product_sku_name: finishedGood?.tenant_product_info?.product_sku_name,
              quantity: fgQuantity,
              ref_product_code: finishedGood?.tenant_product_info?.ref_product_code,
              uom_info: finishedGood?.tenant_product_info?.uom_info,
            }
          }]
        });
      }
    });

    this.initComponentsAndFg({ totalReadyToProduce, finishedGoods: [{ ...finishedGood, key: uuidv4 }], rawMaterials, byProducts, extraCharges, fgQuantity: Math.max(fgQuantity, 0), updateFgList: true });
    this.setState({
      totalReadyToProduce,
      batchSize: Math.max(fgQuantity, 0),
      batchNumber: `${finishedGood?.tenant_product_info?.internal_sku_code} /${dayjs().format('DDMMYY')}/${finishedGood?.tenant_product_info?.product_batch_counter} `,
      costPrice: Number(finishedGood?.mo_cost_price?.toFixed(DEFAULT_CUR_ROUND_OFF)),
      expiryDate: expiryDays > 0 ? (dayjs().add(expiryDays || 0, 'day')) : dayjs(INFINITE_EXPIRY_DATE),
    });
  }

  initComponentsAndFg({ totalReadyToProduce, finishedGoods, rawMaterials, byProducts, extraCharges, fgQuantity, updateFgList, persistLineLevelGoodStockOnlyKey }) {
    const { addRmWithoutJobwork, updateOtherCharges } = this.state;
    const { isFgStep, cfV2DocManufacturingOrder, } = this.props;

    const batchCustomFields = CustomFieldHelpers.getCfStructure(cfV2DocManufacturingOrder?.data?.batch_custom_fields, true);

    // const getBatches = (selectedQty, productBatches) => {
    //   let remainingQty = Number(selectedQty);
    //   const batches = JSON.parse(JSON.stringify(productBatches));
    //   for (let i = 0; i < batches?.length; i++) {
    //     batches[i].consumed_qty = 0;
    //     batches[i].batch_in_use = true;
    //   }
    //   for (let i = 0; i < batches?.length; i++) {
    //     if (batches[i]?.batch_in_use) {
    //       if (remainingQty > 0 && Number(batches[i].consumed_qty || '0') < batches?.[i]?.available_qty) {
    //         batches[i].consumed_qty = batches?.[i]?.available_qty > remainingQty ? remainingQty : batches?.[i]?.available_qty;
    //         remainingQty -= batches[i].consumed_qty;
    //       } else {
    //         batches[i].consumed_qty = 0;
    //         remainingQty -= batches[i].consumed_qty;
    //       }
    //     }
    //   }
    //   return batches;
    // };
    const getBatches = (selectedQty, productBatches, goodStockOnly, currentLine) => {
      let remainingQty = new Decimal(selectedQty || 0);
      const batches = JSON.parse(JSON.stringify(productBatches));

      for (let i = 0; i < batches?.length; i++) {
        batches[i].consumed_qty = 0;
        batches[i].batch_in_use = goodStockOnly ? (!batches[i].is_rejected_batch ? true : false) : true;
      }

      for (let i = 0; i < batches?.length; i++) {
        if (batches[i]?.batch_in_use) {
          const consumed = new Decimal(batches[i].consumed_qty || 0);
          const available = new Decimal(batches[i].available_qty || 0);

          if (remainingQty.gt(0) && consumed.lt(available)) {
            const consumeQty = Decimal.min(available, remainingQty);
            batches[i].consumed_qty = QUANTITY(consumeQty.toNumber(), currentLine?.uom_info?.precision);
            remainingQty = remainingQty.minus(consumeQty);
          } else {
            batches[i].consumed_qty = 0;
          }
        }
      }

      return batches;
    };

    // const updatedRawMaterials = rawMaterials?.map((item) => {
    //   const qtyPerUnit = item?.quantity / (Number(finishedGoods?.[0]?.quantity) + Number(finishedGoods?.[0]?.wastage_quantity || 0));
    //   const batchQty = qtyPerUnit * fgQuantity;
    //   let wastageQty = 0;
    //   if (item?.bom_line_info) {
    //     wastageQty = ((qtyPerUnit * fgQuantity) * (Number(item?.bom_line_info?.wastage_percentage) / 100));
    //   } else {
    //     const wastagePct = item?.wastage_quantity > 0 ? (Number(item?.wastage_quantity) / Number(item?.quantity)) : 0;
    //     wastageQty = ((qtyPerUnit * fgQuantity) * Number(wastagePct));
    //   }
    //   return {
    //     ...item,
    //     key: uuidv4(),
    //     batch_quantity: QUANTITY(batchQty, item?.uom_info?.precision),
    //     wastage: QUANTITY(wastageQty, item?.uom_info?.precision),
    //     product_category_info: item?.tenant_product_info?.product_category_info,
    //     tenant_product_info: { ...item?.tenant_product_info, product_batches: getBatches((Number(batchQty) + Number(wastageQty)), item?.tenant_product_info?.product_batches, persistLineLevelGoodStockOnlyKey? item.goodStockOnly : true) },
    //     goodStockOnly: persistLineLevelGoodStockOnlyKey? item.goodStockOnly : true,
    //   };
    // });
    const updatedRawMaterials = rawMaterials?.map((item) => {
      const fgQty = Decimal(finishedGoods?.[0]?.quantity || 0);
      const fgWastageQty = Decimal(finishedGoods?.[0]?.wastage_quantity || 0);
      const fgTotalQty = fgQty.plus(fgWastageQty);

      const itemQty = Decimal(item?.quantity || 0);
      const qtyPerUnit = itemQty.dividedBy(fgTotalQty);

      const batchQty = qtyPerUnit.times(fgQuantity);
      let wastageQty = new Decimal(0);

      if (item?.bom_line_info) {
        const wastagePct = Decimal(item?.bom_line_info?.wastage_percentage || 0).dividedBy(100);
        wastageQty = qtyPerUnit.times(fgQuantity).times(wastagePct);
      } else {
        const wastageBaseQty = Decimal(item?.wastage_quantity || 0);
        const wastagePct = item?.wastage_quantity > 0
          ? wastageBaseQty.dividedBy(itemQty)
          : new Decimal(0);
        wastageQty = qtyPerUnit.times(fgQuantity).times(wastagePct);
      }

      const totalQty = batchQty.plus(wastageQty);

      return {
        ...item,
        key: uuidv4(),
        batch_quantity: QUANTITY(batchQty.toNumber(), item?.uom_info?.precision),
        wastage: QUANTITY(wastageQty.toNumber(), item?.uom_info?.precision),
        product_category_info: item?.tenant_product_info?.product_category_info,
        tenant_product_info: {
          ...item?.tenant_product_info,
          product_batches: getBatches(
            totalQty.toNumber(),
            item?.tenant_product_info?.product_batches,
            persistLineLevelGoodStockOnlyKey ? item.goodStockOnly : true,
            item,
          ),
        },
        goodStockOnly: persistLineLevelGoodStockOnlyKey ? item.goodStockOnly : true,
      };
    });

    // const updatedByProducts = byProducts?.map((item) => {
    //   const qtyPerUnit = item?.quantity / (Number(finishedGoods?.[0]?.quantity) + Number(finishedGoods?.[0]?.wastage_quantity || 0));
    //   return {
    //     ...item,
    //     key: uuidv4(),
    //     batch_quantity: QUANTITY(qtyPerUnit * fgQuantity, item?.uom_info?.precision),
    //     batch_number: `${item?.tenant_product_info?.internal_sku_code} /${dayjs().format('DDMMYY')}/${item?.tenant_product_info?.product_batch_counter} `,
    //     expiry_date: item?.tenant_product_info?.expiry_days > 0 ? (dayjs().add(item?.tenant_product_info?.expiry_days || 0, 'day')) : dayjs(INFINITE_EXPIRY_DATE),
    //     cost_price: Number(item?.tenant_product_info?.cost_price?.toFixed(DEFAULT_CUR_ROUND_OFF)),
    //     product_category_info: item?.tenant_product_info?.product_category_info,
    //     custom_fields: batchCustomFields,
    //   };
    // });

    const updatedByProducts = byProducts?.map((item) => {
      const fgQty = new Decimal(finishedGoods?.[0]?.quantity || 0);
      const wastageQty = new Decimal(finishedGoods?.[0]?.wastage_quantity || 0);
      const totalOutputQty = fgQty.plus(wastageQty);

      const itemQty = new Decimal(item?.quantity || 0);
      const qtyPerUnit = totalOutputQty.gt(0) ? itemQty.div(totalOutputQty) : new Decimal(0);

      const batchQty = qtyPerUnit.mul(fgQuantity);

      return {
        ...item,
        key: uuidv4(),
        batch_quantity: QUANTITY(
          batchQty.toNumber(),
          item?.uom_info?.precision
        ),
        batch_number: `${item?.tenant_product_info?.internal_sku_code} /${dayjs().format('DDMMYY')}/${item?.tenant_product_info?.product_batch_counter} `,
        expiry_date: item?.tenant_product_info?.expiry_days > 0
          ? dayjs().add(item?.tenant_product_info?.expiry_days || 0, 'day')
          : dayjs(INFINITE_EXPIRY_DATE),
        cost_price: new Decimal(item?.tenant_product_info?.cost_price || 0)
          .toDecimalPlaces(DEFAULT_CUR_ROUND_OFF)
          .toNumber(),
        product_category_info: item?.tenant_product_info?.product_category_info,
        custom_fields: batchCustomFields,
      };
    });

    // const updatedExtraCharges = extraCharges?.map((item) => ({
    //   ...item, key: uuidv4(), charge_amount: item?.charge_amount || 0, charge_value: Number(((item?.est_charge_amount / (finishedGoods[0]?.quantity + Number(finishedGoods?.[0]?.wastage_quantity || 0)))  * fgQuantity).toFixed(DEFAULT_CUR_ROUND_OFF)),
    // }));

    const updatedExtraCharges = extraCharges?.map((item) => {
      const fgQty = new Decimal(finishedGoods?.[0]?.quantity || 0);
      const wastageQty = new Decimal(finishedGoods?.[0]?.wastage_quantity || 0);
      const totalOutputQty = fgQty.plus(wastageQty);

      const estChargeAmount = new Decimal(item?.est_charge_amount || 0);
      const perUnitCharge = totalOutputQty.gt(0) ? estChargeAmount.div(totalOutputQty) : new Decimal(0);
      const chargeValue = perUnitCharge.mul(fgQuantity).toDecimalPlaces(DEFAULT_CUR_ROUND_OFF).toNumber();

      return {
        ...item,
        key: uuidv4(),
        charge_amount: item?.charge_amount || 0,
        charge_value: chargeValue,
      };
    });

    this.setState({
      rawMaterials: updatedRawMaterials,
      byProducts: updatedByProducts,
      extraCharges: updatedExtraCharges,
    });

    // if (updateFgList) {

    //   const updatedFinishedGoods = finishedGoods?.map((item) => ({
    //     ...item,
    //     key: uuidv4(),
    //     batch_quantity: QUANTITY(Number(fgQuantity), item?.uom_info?.precision),
    //     actual_output: QUANTITY(Number(fgQuantity), item?.uom_info?.precision) * (isFgStep ? item?.bom_info?.default_yield_percentage : item?.sfg_bom_info?.default_yield_percentage) / 100,
    //     custom_fields: batchCustomFields,
    //   }));
    //   this.setState({
    //     finishedGoods: updatedFinishedGoods,
    //   });
    //   this.fgBatchCostPriceCalculator(updatedRawMaterials, updatedExtraCharges, updatedFinishedGoods, addRmWithoutJobwork, updateOtherCharges);
    // } else {
    //   const updatedFinishedGoods = finishedGoods?.map((item) => {
    //     return {
    //     ...item,
    //     key: uuidv4(),
    //     batch_quantity: QUANTITY(Number(fgQuantity) + Number(finishedGoods?.[0]?.wastage_quantity || 0), item?.uom_info?.precision),
    //   }});
    //   this.fgBatchCostPriceCalculator(updatedRawMaterials, updatedExtraCharges, updatedFinishedGoods, addRmWithoutJobwork, updateOtherCharges);
    // }

    if (updateFgList) {
      const updatedFinishedGoods = finishedGoods?.map((item) => {
        const fgQty = new Decimal(fgQuantity || 0);
        const yieldPercentage = new Decimal(
          isFgStep ? item?.bom_info?.default_yield_percentage || 0 : item?.sfg_bom_info?.default_yield_percentage || 0
        );

        const batchQty = QUANTITY(fgQty.toNumber(), item?.uom_info?.precision);
        const actualOutput = QUANTITY(
          fgQty.mul(yieldPercentage).div(100).toNumber(),
          item?.uom_info?.precision
        );

        return {
          ...item,
          key: uuidv4(),
          batch_quantity: batchQty,
          actual_output: actualOutput,
          custom_fields: batchCustomFields,
        };
      });

      this.setState({ finishedGoods: updatedFinishedGoods });
      this.fgBatchCostPriceCalculator(
        updatedRawMaterials,
        updatedExtraCharges,
        updatedFinishedGoods,
        addRmWithoutJobwork,
        updateOtherCharges
      );
    } else {
      const updatedFinishedGoods = finishedGoods?.map((item) => {
        const fgQty = new Decimal(fgQuantity || 0);
        const wastageQty = new Decimal(finishedGoods?.[0]?.wastage_quantity || 0);
        const totalQty = fgQty.plus(wastageQty);

        return {
          ...item,
          key: uuidv4(),
          batch_quantity: QUANTITY(totalQty.toNumber(), item?.uom_info?.precision),
        };
      });

      this.fgBatchCostPriceCalculator(
        updatedRawMaterials,
        updatedExtraCharges,
        updatedFinishedGoods,
        addRmWithoutJobwork,
        updateOtherCharges
      );
    }
  }

  addBatch = () => {
    this.setState({ formSubmitted: true });
    const {
      expiryDate, batchNumber, lotNumber, rawMaterials, byProducts, finishedGoods, extraCharges, addRmWithoutJobwork, updateByProduct, updateOtherCharges, fgBatchCostPrice, selectedBatchJobworks,
      arNumber, barcode, margin, brand, rollNumber, freightCost, otherCost, landedCost, mfgBatchNumber, mfgDate, sellingPrice, mrp, inventoryLocation, addFgWithoutJobwork, createJobWorks, goodStockOnlyFromProps,
    } = this.state;
    const {
      selectedMO, createMOAdjustment, callback, updateMOOtherCharges, org, isFgStep, user,
    } = this.props;

    const validatedRms = rawMaterials?.some((record) => (
      record?.tenant_product_info?.product_batches?.filter((item) => item.available_qty > 0)?.length === 0
      || (Number(record?.batch_quantity) + Number(record.wastage))
      > Helpers.getValueTotalInObject(
        record?.tenant_product_info?.product_batches?.filter(
          (batchData) => batchData?.batch_in_use && (record.goodStockOnly ? !batchData?.is_rejected_batch : true),
        ),
        'available_qty',
      )
    ));
    const config = org?.organisation?.[0]?.inventory_config?.settings?.batch_management;
    if (this.isDataValid() && this.validateBatchDetailsSimple(config)) {
      const payload = [];
      finishedGoods?.map((fgLine) => {
        payload.push({
          entity_type: isFgStep ? 'FINISHED_GOOD' : 'SEMI_FINISHED_GOOD',
          entity_id: isFgStep ? fgLine?.mo_fg_id : fgLine?.mo_line_id,
          desired_output: fgLine?.batch_quantity,
          quantity: Number(fgLine?.actual_output),
          expiry_date: addFgWithoutJobwork ? (fgLine?.tenant_product_info?.expiry_days > 0 ? FormHelpers.dateFormatter(expiryDate, fgLine?.tenant_product_info?.expiry_date_format) : INFINITE_EXPIRY_DATE) : null,
          mo_id: fgLine?.mo_id,
          status: 'ISSUED',
          lot_number: addFgWithoutJobwork ? (lotNumber || null) : null,
          custom_batch_number: addFgWithoutJobwork ? batchNumber : null,
          custom_fields: CustomFieldHelpers.postCfStructure(fgLine?.custom_fields),
          cost_price: addFgWithoutJobwork ? (fgBatchCostPrice || null) : null,
          selling_price: addFgWithoutJobwork ? (sellingPrice || null) : null,
          wastage_quantity: 0,
          ar_number: addFgWithoutJobwork ? (arNumber || null) : null,
          batch_barcode: addFgWithoutJobwork ? (barcode || null) : null,
          brand: addFgWithoutJobwork ? (brand || null) : null,
          freight_cost: addFgWithoutJobwork ? (freightCost || null) : null,
          landed_cost: addFgWithoutJobwork ? (landedCost || null) : null,
          manufacturing_date: addFgWithoutJobwork && mfgDate ? FormHelpers.dateFormatter(mfgDate, fgLine?.tenant_product_info?.manufacturing_date_format) : null,
          margin: addFgWithoutJobwork ? (margin || null) : null,
          mfg_batch_no: addFgWithoutJobwork ? (mfgBatchNumber || null) : null,
          other_cost: addFgWithoutJobwork ? (otherCost || null) : null,
          roll_no: addFgWithoutJobwork ? (rollNumber || null) : null,
          mrp: addFgWithoutJobwork ? (mrp || null) : null,
          reservation_id: null,
          inventory_location_id: addFgWithoutJobwork ? (inventoryLocation || null) : null,
          dummy_adjustment: addFgWithoutJobwork ? false : true,
          job_works: createJobWorks ? selectedBatchJobworks?.[0]?.br_lines?.map((brLine) => ({
            step_number: brLine?.step_number,
            org_id: user?.tenant_info?.org_id,
            tenant_id: selectedMO?.tenant_id,
            process_name: brLine?.process_name,
            fg_tenant_product_id: isFgStep ? fgLine?.tenant_product_info?.tenant_product_id : null,
            sfg_tenant_product_id: !isFgStep ? fgLine?.tenant_product_info?.tenant_product_id : null,
            rm_tenant_product_id: null,
            machine_resource_group: brLine?.machine_resource_group,
            mo_fg_id: isFgStep ? fgLine?.mo_fg_id : null,
            mo_line_id: isFgStep ? null : fgLine?.mo_line_id,
            remarks: brLine?.remarks,
            attachments: brLine?.attachments,
            status: 'PENDING',
            assignees: [],
            bom_route_line_id: brLine?.bom_route_line_id,
            sequence_number: brLine?.sequence_number,
            pr_line_input_materials: brLine?.consumption?.map((inputLine) => ({
              product_sku_id: inputLine?.productSkuId,
              required_input_qty: inputLine?.quantity,
              input_material_mo_line_id: brLine?.childrenMaterials?.find((innerItems) => innerItems?.product_sku_id === inputLine?.productSkuId)?.mo_line_id,
            })),
            pr_line_output_materials: brLine?.production?.map((outputLine) => ({
              product_sku_id: outputLine?.productSkuId,
              required_output_qty: outputLine?.quantity,
              output_material_mo_fg_id: isFgStep ? fgLine?.mo_fg_id : null,
              output_material_mo_line_id: isFgStep ? null : fgLine?.mo_line_id,
            })),
            pr_line_processing_materials: brLine?.processing?.map((processingLine) => {
              // Log the current processingLine being iterated
              const childMaterial = brLine?.childrenMaterials?.find(
                (innerItems) => innerItems?.product_sku_id === processingLine?.productSkuId
              );

              const tenantProductInfoSkuId = fgLine?.tenant_product_info?.product_sku_id;

              const processingProductSkuId = processingLine?.productSkuId;

              // Calculate pm_mo_fg_id and log
              const pmMoFgId = childMaterial ? null : fgLine?.mo_fg_id;

              // Calculate pm_mo_line_id and log
              const pmMoLineId = childMaterial
                ? childMaterial?.mo_line_id
                : (tenantProductInfoSkuId === processingProductSkuId
                  ? fgLine?.mo_line_id
                  : null);
              return {
                product_sku_id: processingProductSkuId,
                required_processing_qty: processingLine?.quantity,
                pm_mo_fg_id: pmMoFgId,
                pm_mo_line_id: pmMoLineId,
              };
            }),
            mo_adjustment_id: null,
            route_line_charges: [
              {
                charge_name: 'FIXED',
                charge_amount: brLine?.fixed_charge,
                charge_type: 'fixed'
              },
              {
                charge_name: 'UNIT',
                charge_amount: brLine?.unit_charge,
                charge_type: 'unit'
              }
            ],
            pending_qty: null,
            completed_qty: null,
            rework_qty: null,
            rejected_qty: null,
            subcontractor_seller_id: brLine?.subcontractor_seller_id,
            jw_product_sku_id: brLine?.product_info?.product_sku_id,
            lead_time_in_min: brLine?.lead_time_in_min,
          })) : [],
        });
      });
      if (addRmWithoutJobwork) {
        if (validatedRms) {
          notification.error({
            message: 'Raw material line does not have enough available quantity. Please remove the line and proceed.',
            duration: 4,
            placement: 'top'
          });
          return;
        }
        rawMaterials?.map((moLine) => {
          moLine.tenant_product_info.product_batches?.filter((item) => item?.consumed_qty > 0)?.map((item) => {
            payload.push({
              entity_type: 'RAW_MATERIAL',
              entity_id: moLine?.mo_line_id,
              quantity: -item?.consumed_qty,
              mo_id: selectedMO?.mo_id,
              status: 'ISSUED',
              batch_id: item?.batch_id,
              cost_price: item?.cost_price,
              selling_price: item?.selling_price,
              lot_number: item?.lot_number || '',
              custom_batch_number: item?.batch_number,
              expiry_date: item?.expiry_date ? FormHelpers.dateFormatter(item?.expiry_date, moLine?.tenant_product_info?.expiry_date_format) : null,
              wastage_quantity: -moLine?.wastage || 0,
              reservation_id: item?.reservation_id || null,
            });
          });
        });
      }
      if (updateByProduct && addFgWithoutJobwork) {
        byProducts?.map((bpLine) => {
          payload.push({
            entity_type: 'BY_PRODUCT',
            entity_id: bpLine?.by_product_id,
            quantity: bpLine?.batch_quantity,
            expiry_date: bpLine?.tenant_product_info?.expiry_days > 0 ? FormHelpers.dateFormatter(bpLine?.expiry_date, bpLine?.tenant_product_info?.expiry_date_format) : INFINITE_EXPIRY_DATE,
            mo_id: bpLine?.mo_id,
            status: 'ISSUED',
            lot_number: bpLine?.lot_number || '',
            custom_batch_number: bpLine?.batch_number,
            cost_price: bpLine?.tenant_product_info?.cost_price,
            selling_price: 0,
            wastage_quantity: 0,
            reservation_id: null,
            custom_fields: CustomFieldHelpers.postCfStructure(bpLine?.custom_fields),
          });
        });
      }
      createMOAdjustment({ mo_adjustments: payload }, () => {
        if (updateOtherCharges) {
          const otherFgCharges = selectedMO?.extra_charges?.filter((item) => item?.fg_tenant_product_id != finishedGoods[0]?.tenant_product_id);
          const chargesPaylod = {
            mo_id: selectedMO?.mo_id,
            extra_charges: [...otherFgCharges, ...extraCharges?.map((item) => ({
              bom_charge_value: item?.bom_charge_value,
              bom_id: item?.bom_id,
              charge_name: item?.charge_name,
              est_charge_amount: item?.est_charge_amount,
              charge_amount: Number(item?.charge_amount) + Number(item?.charge_value),
              fg_tenant_product_id: item?.fg_tenant_product_id,
            }))],
          };
          updateMOOtherCharges(chargesPaylod, () => callback());
        } else {
          callback();
        }
      });

    } else {
      notification.open({
        type: 'error',
        placement: 'top',
        duration: 4,
        message: 'Please enter all the required information.',
      });
    }
  };

  fgBatchCostPriceCalculator = (rawMaterials, extraCharges, finishedGoods, addRmWithoutJobwork, updateOtherCharges) => {
    const finishedGoodToBeProducedQuantity = Number(finishedGoods?.[0]?.actual_output || 0);
    let totalRmIncurredCost = 0;
    let totalExtraChargesCost = 0;
    let incurredCostPerQuantity = 0;
    if (addRmWithoutJobwork) {
      rawMaterials?.map((rm) => {
        const consumedProductBatches = (rm?.tenant_product_info?.product_batches || [])?.filter((batch) => batch.consumed_qty) || [];
        consumedProductBatches.map((batch) => {
          totalRmIncurredCost += Number(batch?.consumed_qty) * Number(batch?.landed_cost);
        });
      });
    }
    if (updateOtherCharges) {
      extraCharges?.map((ec) => {
        totalExtraChargesCost += Number(ec?.charge_value || 0);
      });
    }
    incurredCostPerQuantity = finishedGoodToBeProducedQuantity ? ((totalRmIncurredCost + totalExtraChargesCost) / finishedGoodToBeProducedQuantity) : 0;

    this.setState({ fgBatchCostPrice: Number.parseFloat(Number(incurredCostPerQuantity).toFixed(DEFAULT_CUR_ROUND_OFF)) });
  };

  validateBatchDetailsSimple = (config) => {
    // Helper function to check if a field is missing when it's mandatory
    const {
      costPrice, lotNumber, arNumber, sellingPrice, mrp, barcode, rollNumber, addFgWithoutJobwork,
      freightCost, otherCost, landedCost, margin, brand, mfgBatchNumber, mfgDate, inventoryLocation, finishedGoods, byProducts, updateByProduct,
    } = this.state;
    const { user } = this.props;

    if (!addFgWithoutJobwork) {
      return true;
    }

    const isFieldValid = (isEnabled, isMandatory, value) => {
      // If the field is enabled and mandatory, check if the value is valid

      if (isEnabled && isMandatory) {
        return !(value === undefined || value === '' || value === 0);
      }
      // If the field is not enabled or not mandatory, it's considered valid
      return true;
    };
    const inventoryConfig = user?.tenant_info?.inventory_config?.settings;
    const enablesWarehouseLocations = inventoryConfig?.enable_location_based_stock;
    const isWarehouseLocationMandatory = inventoryConfig?.is_inventory_location_mandatory;

    if (
      !isFieldValid(config.cost_price_is_enabled, config.cost_price_is_mandatory, costPrice)
      || !isFieldValid(config.lot_number_is_enabled, config.lot_number_is_mandatory, lotNumber)
      || !isFieldValid(config.selling_price_is_enabled, config.selling_price_is_mandatory, sellingPrice)
      || !isFieldValid(config.mrp_is_enabled, config.mrp_is_mandatory, mrp)
      || !isFieldValid(config.ar_number_is_enabled, config.ar_number_is_mandatory, arNumber)
      || !isFieldValid(config.roll_no_is_enabled, config.roll_no_is_mandatory, rollNumber)
      || !isFieldValid(config.brand_is_enabled, config.brand_is_mandatory, brand)
      || !isFieldValid(config.mfg_batch_no_is_enabled, config.mfg_batch_no_is_mandatory, mfgBatchNumber)
      || !isFieldValid(config.manufacturing_date_is_enabled, config.manufacturing_date_is_mandatory, mfgDate)
      || !isFieldValid(enablesWarehouseLocations, isWarehouseLocationMandatory, inventoryLocation)
      // Include other conditions as needed
    ) {
      // If any field validation fails, return false
      return false;
    }
    // for mandatory batch custom fields
    if (addFgWithoutJobwork && finishedGoods) {
      for (const fg of finishedGoods) {
        if (!fg?.custom_fields) continue; // Skip if custom_fields is undefined

        for (const item of fg.custom_fields) {
          if ((item?.isActive && item?.isRequired)
            && (item?.fieldValue === undefined || item?.fieldValue === '' || item?.fieldValue === 0)) {
            return false;
          }
        }
      }
    }

    if (updateByProduct && byProducts) {
      for (const bp of byProducts) {
        if (!bp?.custom_fields) continue; // Skip if custom_fields is undefined

        for (const item of bp.custom_fields) {
          if ((item?.isActive && item?.isRequired)
            && (item?.fieldValue === undefined || item?.fieldValue === '' || item?.fieldValue === 0)) {
            return false;
          }
        }
      }
    }

    // If all batches pass validation, return true
    return true;
  };

  isDataValid() {
    const {
      totalReadyToProduce, finishedGoods, rawMaterials, byProducts, extraCharges, expiryDate, addRmWithoutJobwork,
      updateByProduct, updateOtherCharges, addFgWithoutJobwork, selectedBatchJobworks, createJobWorks,
    } = this.state;

    const errors = [];
    let isDataValid = true;

    // Helper function to add errors
    const addError = (condition, message) => {
      if (condition) errors.push(message);
    };

    // Validate finished goods without Job Card
    const validateFgWithoutJobwork = () => {
      addError(
        addRmWithoutJobwork && finishedGoods[0]?.batch_quantity > totalReadyToProduce,
        'Sufficient raw material is not available for the given batch size.'
      );
      addError(
        finishedGoods[0]?.batch_quantity <= 0 || Number(finishedGoods[0]?.actual_output) < 0 || !expiryDate,
        'Please enter a valid quantity for producing a new batch.'
      );
      if (updateByProduct) {
        const hasInvalidByProducts = byProducts?.some(record => record?.batch_quantity < 0);
        addError(hasInvalidByProducts, 'Please enter a valid quantity for by products.');
      }
      if (updateOtherCharges) {
        const hasInvalidCharges = extraCharges?.some(record => record?.charge_value < 0);
        addError(hasInvalidCharges, 'Please enter a valid value for incurred charge.');
      }
    };

    // Validate raw materials without Job Card
    const validateRmWithoutJobwork = () => {
      rawMaterials?.forEach(record => {
        const availableQty = Helpers.getValueTotalInObject(record?.tenant_product_info?.product_batches?.filter((item) => (item?.batch_in_use)), 'available_qty');
        addError(
          (Number(record?.batch_quantity) + Number(record?.wastage)) > availableQty || record?.batch_quantity < 0,
          'Sufficient raw material is not available for the given batch size.'
        );
      });
    };

    // Validate job cards
    const validateJobWorks = () => {
      const brLines = selectedBatchJobworks?.[0]?.br_lines ?? [];
      const finishedProductSkuId = selectedBatchJobworks?.[0]?.product_sku_info.product_sku_id;
      const childRms = selectedBatchJobworks?.[0]?.product_sku_info?.children_materials ?? [];

      //validation for process name missing from job card
      brLines.forEach(line => {
        addError(!line.process_name, `Please enter process name for job card with step number ${line?.step_number}.`);
      });

      //validation for checking if at least one job card is added in case job cards creation is enabled
      addError(
        createJobWorks && brLines.length === 0,
        'Please add at least one job card or disable the option to create job cards.'
      );

      if (createJobWorks) {
        if (addFgWithoutJobwork) {
          const hasProductionItems = brLines.some(line => line.production && line.production.length > 0);
          addError(hasProductionItems, 'You cannot add the finished/semi finished goods in job card if you are creating finished goods batch now.');
        } else {
          const productionSkuCount = new Map();
          brLines.forEach(line => {
            line.production?.forEach(item => {
              if (item.productSkuId === finishedProductSkuId) {
                productionSkuCount.set(item.productSkuId, (productionSkuCount.get(item.productSkuId) || 0) + 1);
              }
            });
          });
          let isDuplicateFg = false;
          productionSkuCount.forEach((value, key) => {
            if (value > 1) isDuplicateFg = true;
          });
          addError(isDuplicateFg, 'You cannot add the finished/semi finished goods in multiple job cards.');
          addError(productionSkuCount.size === 0, 'You have to allocate finished goods to job card if the FG batch is not issued here.');
        }
      }

      if (createJobWorks) {
        if (addRmWithoutJobwork) {
          const hasConsumptionItems = brLines.some(line => line.consumption && line.consumption.length > 0);
          addError(hasConsumptionItems, 'You cannot add the raw materials in job card if the raw material is being issued here.');
        } else {
          childRms.forEach(material => {
            const foundInAnyLine = brLines.some(line => line.consumption?.some(item => item.productSkuId === material.product_sku_id));
            addError(!foundInAnyLine, 'You have to allocate all raw materials to job card if the raw materials are not issued here.');
          });
        }
      }

      for (let i = 0; i < brLines.length; i++) {
        const line = brLines[i];

        const hasProductionItems = brLines.some(line => line.production && line.production.length > 0);
        if (hasProductionItems && createJobWorks) {
          // Validation for finished good in processing list
          line.processing?.forEach(item => {
            if (item.productSkuId === finishedProductSkuId) {
              const foundInProduction = brLines.slice(0, i).some(previousLine => previousLine.production?.some(prodItem => prodItem.productSkuId === finishedProductSkuId));
              addError(!foundInProduction, `The finished good in job card ${brLines[i + 1]?.process_name} must be processed in the subsequent job cards only.`);
            }
          });

          // Validation for raw materials in processing list
          line.processing?.forEach(item => {
            childRms.forEach(material => {
              if (item.productSkuId === material.product_sku_id) {
                const foundInConsumption = brLines.slice(i + 1).some(nextLine => nextLine.consumption?.some(consItem => consItem.productSkuId === material.product_sku_id));
                addError(!foundInConsumption, `The raw material ${material?.internal_sku_code} - ${material?.product_sku_name} in job card ${brLines[i]?.process_name} must be processed in the previous job cards only.`);
              }
            });
          });
        }
      }
    };

    // Perform validations
    if (addFgWithoutJobwork) validateFgWithoutJobwork();
    if (addRmWithoutJobwork) validateRmWithoutJobwork();
    if (selectedBatchJobworks?.length > 0) validateJobWorks();

    if (errors.length > 0) {
      isDataValid = false;
      this.setState({ errors });
    } else {
      this.setState({ errors: [] });
    }

    return isDataValid;
  }

  // handleTableChange(key, label, value, callback) {
  //   const { finishedGoods } = this.state;
  //   const { isFgStep } = this.props
  //   const copyData = JSON.parse(JSON.stringify(finishedGoods));
  //   const updatedData = copyData?.map((item) => {
  //     if (item.key === key) {
  //       // eslint-disable-next-line no-param-reassign
  //       if (label === 'batch_quantity') {
  //         item[label] = value;
  //         item.actual_output = value * (isFgStep ? item?.bom_info?.default_yield_percentage : item?.sfg_bom_info?.default_yield_percentage) / 100;

  //       } else {
  //         item[label] = value;
  //       }
  //     }
  //     return item;
  //   });
  //   this.setState({
  //     finishedGoods: updatedData,
  //   }, () => {
  //     if (callback) callback();
  //   });
  // }

  handleTableChange(key, label, value, callback) {
    const { finishedGoods } = this.state;
    const { isFgStep } = this.props;
    const copyData = JSON.parse(JSON.stringify(finishedGoods));

    const updatedData = copyData?.map((item) => {
      if (item.key === key) {
        if (label === 'batch_quantity') {
          item[label] = value;
          const yieldPercentage = isFgStep
            ? item?.bom_info?.default_yield_percentage
            : item?.sfg_bom_info?.default_yield_percentage;

          if (value && yieldPercentage) {
            const actualOutput = new Decimal(value || 0)
              .mul(yieldPercentage)
              .div(100);
            item.actual_output = actualOutput.toNumber();
          } else {
            item.actual_output = 0;
          }
        } else {
          item[label] = value;
        }
      }
      return item;
    });

    this.setState({ finishedGoods: updatedData }, () => {
      if (callback) callback(updatedData);
    });
  }

  handleTableChangeByProductLines(key, label, value) {
    const { byProducts } = this.state;
    const copyData = JSON.parse(JSON.stringify(byProducts));
    const updatedData = copyData?.map((item) => {
      if (item.key === key) {
        item[label] = value;
      }
      return item;
    });
    this.setState({
      byProducts: updatedData,
    });
  }

  handleTableChangeByProductCf(value, byProductId) {

    const { byProducts, } = this.state;
    const copyData = JSON.parse(JSON.stringify(byProducts));

    const updateData = copyData?.map((item) => {

      if (item?.by_product_id === byProductId) {
        item.custom_fields = value;
      }
      return item;
    });
    this.setState({ byProducts: updateData, });
  }

  getDataSource(data) {
    if (data) return JSON.parse(JSON.stringify(data));
    return [];
  }

  rawMaterialStock() {
    const { rawMaterials } = this.state;

    return rawMaterials?.every((rm) =>
      rm?.tenant_product_info?.product_batches?.length > 0 &&
      rm.tenant_product_info.product_batches.some(batch => batch?.available_qty > 0)
    ) || false;
  }

  updateBatchCfs = (fieldValue, cfId, record) => {

    const { finishedGoods, } = this.state;
    const copyData = JSON.parse(JSON.stringify(finishedGoods));

    for (const item of copyData) {

      if (record?.key === item?.key && item?.key) {

        const copyLineCfs = item?.custom_fields;
        for (const cf of copyLineCfs) {

          if (cf.cfId === cfId) {

            cf.fieldValue = cf.fieldType === 'ATTACHMENT' ? fieldValue?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url,
              type: attachment?.type,
              name: attachment?.name,
              uid: attachment?.uid,
            })) : fieldValue;
            break;
          }
        }
        break;
      }
    }
    this.setState({ finishedGoods: copyData, });
  };

  render() {
    const {
      selectedMO, createMOAdjustmentLoading, finishedGood, updateMOOtherChargesLoading, org, isFgStep, getBomJobWorkLoading,
    } = this.props;
    const {
      byProducts, finishedGoods, rawMaterials, extraCharges, createJobWorks, addFgWithoutJobwork, addRmWithoutJobwork,
      columns, updateByProduct, updateOtherCharges, totalReadyToProduce, batchSize, formSubmitted, jobworks, errors, showPRZModal, selectedDocumentId,
    } = this.state;

    const disableQuantityChange = this.props?.user?.tenant_info?.production_config?.sub_modules?.manufacturing_order?.settings?.disable_quantity_change_in_mo;
    const disablePriceChange = this.props?.user?.tenant_info?.production_config?.sub_modules?.manufacturing_order?.settings?.disable_price_change_in_mo;

    const enableInternalRefCode = true || user?.tenant_info?.inventory_config?.enable_internal_ref_code;
    const enableInternalSKUCode = true || user?.tenant_info?.inventory_config?.enable_internal_sku_code;

    return (
      <div>
        {/* <Link to={`/inventory/product/view/${finishedGood?.tenant_product_info?.product_sku_id}?tab = 1`} target="_blank">
          <H3Text text={`#${finishedGood?.tenant_product_info?.internal_sku_code} ${finishedGood?.tenant_product_info?.product_sku_name} `} className="fg-add-batch__product-name" />
        </Link> */}
        <div onClick={() => this.setState({ showPRZModal: true, selectedDocumentId: finishedGood?.tenant_product_info?.product_sku_id })}>
          <a>
            {`${enableInternalSKUCode ? `#${finishedGood?.tenant_product_info?.internal_sku_code}` : ''} ${enableInternalRefCode ? `${finishedGood?.tenant_product_info?.ref_product_code} ` : ''} ${finishedGood?.tenant_product_info?.product_sku_name} `}
          </a>
        </div>
        <div className="ant-row">
          <div className="ant-col-md-12">
            <br />
            <Checkbox checked={createJobWorks} onChange={(e) => this.setState({ createJobWorks: e.target.checked, addFgWithoutJobwork: !e.target.checked ? true : addFgWithoutJobwork })}>
              Create Job Cards
            </Checkbox><br />
            <Checkbox checked={addFgWithoutJobwork} onChange={(e) => this.setState({ addFgWithoutJobwork: e.target.checked })} disabled={!createJobWorks}>
              {`Create ${isFgStep ? 'Finished Good' : 'Semi Finished Good'} Batch Without Job Card`}
            </Checkbox><br />
            <Checkbox checked={addRmWithoutJobwork} onChange={(e) => this.setState({ addRmWithoutJobwork: e.target.checked, addFgWithoutJobwork: e.target.checked ? true : addFgWithoutJobwork })}>
              Issue Raw Materials Without Job Cards
            </Checkbox><br />
            <br />
            {(createJobWorks && !addFgWithoutJobwork) && <H3FormInput
              type="number"
              containerClassName="orgInputContainer create-category-input"
              labelClassName="orgFormLabel"
              inputClassName="orgFormInput"
              placeholder=""
              label="Enter Batch Size"
              onChange={(event) => {
                this.handleTableChange(finishedGoods?.[0]?.key, 'batch_quantity', event.target.value, (updatedFinishedGoods) => {
                  this.initComponentsAndFg({ totalReadyToProduce, finishedGoods: updatedFinishedGoods, rawMaterials, byProducts, extraCharges, fgQuantity: (event.target.value || 0), updateFgList: false, persistLineLevelGoodStockOnlyKey: true });
                });
                this.setState({ batchSize: event.target.value });
              }}
              value={batchSize}
              maxLength="100"
              required
              showError={formSubmitted && !batchSize}
            />}
          </div>
        </div>
        {errors?.filter((value, index) => errors?.indexOf(value) === index)?.length > 0 && <Alert
          showIcon
          type="error"
          message="Batch entry cannot be created because of following errors:"
          description={<div>
            <ul>
              {errors?.map((error) => (
                <li>{error}</li>
              ))}
            </ul>
          </div>}
        />}
        {addFgWithoutJobwork && <div className="rm-table-wrapper rm-fg-table-wrapper hide__in-mobile">
          <Table
            title={() => `Ready to Produce - ${totalReadyToProduce} ${finishedGood?.tenant_product_info?.uom_info?.uqc?.toProperCase()} `}
            columns={columns}
            dataSource={this.getDataSource(finishedGoods)}
            size="small"
            scroll={{ x: 'max-content' }}
            pagination={false}
            loading={getBomJobWorkLoading}
          />
        </div>}
        <hr className="add-batch__components-partition" />
        <div className="add-batch__components">
          {addRmWithoutJobwork && (
            <UpdateRawMaterial
              moLines={rawMaterials}
              updateRawMaterialLines={(value) => {
                this.setState({ rawMaterials: value });
                this.fgBatchCostPriceCalculator(value, extraCharges, finishedGoods, addRmWithoutJobwork, updateOtherCharges);
              }}
              isFlexible={!!(selectedMO?.bom_info?.flexible_consumption === 'ALLOWED')}
              totalReadyToProduce={totalReadyToProduce}
              loading={getBomJobWorkLoading}
              disableQuantityChange={disableQuantityChange}
            />
          )}
          {updateByProduct && addFgWithoutJobwork && byProducts?.length > 0 && (
            <ByProduct
              data={byProducts}
              handleTableChange={(key, label, value) => this.handleTableChangeByProductLines(key, label, value)}
              handleTableChangeCf={(value, byProductId) => this.handleTableChangeByProductCf(value, byProductId)}
              isFlexible={!!(selectedMO?.bom_info?.flexible_consumption === 'ALLOWED')}
              totalReadyToProduce={totalReadyToProduce}
              loading={getBomJobWorkLoading}
              formSubmitted={formSubmitted}
            />
          )}
          {isFgStep && updateOtherCharges && addFgWithoutJobwork && extraCharges?.length > 0 && (
            <OtherCharges
              extraCharges={extraCharges}
              updateExtraCharges={(charges) => {
                this.setState({ extraCharges: charges });
                this.fgBatchCostPriceCalculator(rawMaterials, charges, finishedGoods, addRmWithoutJobwork, updateOtherCharges);
              }}
              loading={getBomJobWorkLoading}
              disablePriceChange={disablePriceChange}
            />
          )}
          {
            createJobWorks && <TemplateRouteLinesV2
              batchJobworks={jobworks}
              callback={() => {}}
              bomRawMaterialLines={rawMaterials}
              quantity={finishedGood?.bom_info?.quantity}
              batchSize={batchSize}
              setBatchJobworks={(data) => this.setState({ selectedBatchJobworks: data })}
              finishedGood={finishedGood}
            />
          }
        </div>
        <div className="ant-row">
          <div className="custom-drawer__footer" style={{ width: '984px' }}>
            <div className="ant-col-md-6" style={{ marginTop: '0.5rem' }}>
              <H3Button
                buttonType={defaultButtonTypes.BLUE_ROUNDED}
                text="Create"
                onClick={() => this.addBatch()}
                isLoading={createMOAdjustmentLoading || updateMOOtherChargesLoading}
                disabled={createMOAdjustmentLoading || updateMOOtherChargesLoading || (addRmWithoutJobwork && !this.rawMaterialStock())}
                style={{
                  width: '180px',
                  borderRadius: '5px',
                  padding: '7px 0px',
                  marginLeft: '0',
                }}
              />
            </div>
          </div>
        </div>
        <PRZModal
          isOpen={showPRZModal}
          onClose={() => {
            this.setState({ showPRZModal: false, selectedDocumentId: null });
          }}
          entityType="product"
          documentId={selectedDocumentId}
        />
      </div>
    );
  }
}

const mapStateToProps = ({
  UserReducers, MOReducers, BOMReducers, CFV2Reducers,
}) => ({
  user: UserReducers.user,
  createMOAdjustmentLoading: MOReducers.createMOAdjustmentLoading,
  updateMOOtherChargesLoading: MOReducers.updateMOOtherChargesLoading,
  MONEY: UserReducers.MONEY,
  org: UserReducers.org,
  getBomJobWorkLoading: BOMReducers.getBomJobWorkLoading,
  cfV2DocManufacturingOrder: CFV2Reducers.cfV2DocManufacturingOrder,
});
const mapDispatchToProps = (dispatch) => ({
  createMOAdjustment: (payload, callback) => dispatch(MOActions.createMOAdjustment(payload, callback)),
  updateMOOtherCharges: (payload, callback) => dispatch(MOActions.updateMOOtherCharges(payload, callback)),
  getBomJobWork: (payload, callback) => dispatch(BOMActions.getBomJobWork(payload, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(AddBatchV2));