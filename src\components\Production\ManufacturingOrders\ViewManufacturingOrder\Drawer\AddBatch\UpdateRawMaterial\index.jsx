import React, { Component, Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import Decimal from 'decimal.js';
import PropTypes from 'prop-types';
import H3Button, { defaultButtonTypes } from '@Uilib/h3Button';
import {
  Alert, Table,
} from 'antd';
import Helpers from '@Apis/helpers';
import H3FormInput from '@Uilib/h3FormInput';
import MOActions from '@Actions/moActions';
import { v4 as uuidv4 } from 'uuid';
import RawMaterialBatchSelector from './RawMaterialBatchSelector';
import { QUANTITY } from '@Apis/constants';
import H3Text from '@Uilib/h3Text';
import ProductCategoryLabel from '../../../../../../Common/ProductCategoryLabel';
import PRZModal from '../../../../../../Common/UI/PRZModal';
import ProductCodesAndName from '@Components/Common/ProductCodesAndName';
import './style.scss';

/**
 *
 */
class UpdateRawMaterial extends Component {
  /**
   *
   * @param props
   */
  constructor(props) {
    super(props);
    this.state = {
      rawMaterialColumns: [
        {
          title: 'Product',
          width: '240px',
          render: (item) => {
            const enableInternalRefCode = this.props?.user?.tenant_info?.inventory_config?.settings?.enable_internal_ref_code;
            const enableInternalSKUCode = this.props?.user?.tenant_info?.inventory_config?.settings?.enable_internal_sku_code;
            return (
              <Fragment>
                <div onClick={() => this.setState({ showPRZModal: true, selectedDocumentId: item?.tenant_product_info?.product_sku_id })}>
                  <ProductCodesAndName
                    skuCode={item?.tenant_product_info?.internal_sku_code}
                    refCode={item?.tenant_product_info?.ref_product_code}
                    name={item?.tenant_product_info?.product_sku_name}
                    showSku={enableInternalSKUCode}
                    showRefCode={enableInternalRefCode}
                  />
                </div>

                {item?.product_category_info?.category_path?.length > 0 && (
                  <ProductCategoryLabel
                    categoryPath={item?.product_category_info?.category_path}
                    categoryName={item?.product_category_info?.category_path?.at(-1)}
                    containerStyle={{
                      width: 'fit-content',
                    }}
                  />
                )}
              </Fragment>
            );
          }
        },
        {
          title: 'Available Stock',
          render: (item) => {
            const availableQty = Helpers.getValueTotalInObject(item?.tenant_product_info?.product_batches?.filter((batchData) => (batchData?.batch_in_use && (item.goodStockOnly ? !batchData?.is_rejected_batch : true))), 'available_qty');
            return `${QUANTITY(availableQty, item?.uom_info?.precision)} ${item?.uom_info?.uqc?.toProperCase()}`;
          },

        },
        {
          title: 'Estimated Usage',
          render: (item) => (
            <Fragment>
              {`${QUANTITY(Number(item?.quantity), item?.uom_info?.precision)} ${item?.uom_info?.uqc?.toProperCase()}`}
              <H3Text text={`+${QUANTITY(Number(item?.wastage_quantity), item?.uom_info?.precision)} ${item?.uom_info?.uqc?.toProperCase()} Wastage`} className="table-subscript" />
            </Fragment>
          ),
        },
        {
          title: 'Used',
          render: (item) => `${QUANTITY(Math.abs(item?.issued_quantity), item?.uom_info?.precision)} ${item?.uom_info?.uqc?.toProperCase()}`,

        },
        {
          title: 'Pending',
          render: (item) => `${QUANTITY((item?.quantity + Number(item?.wastage_quantity)) - Math.abs(item?.issued_quantity), item?.uom_info?.precision)} ${item?.uom_info?.uqc?.toProperCase()}`,

        },
        {
          title: 'Quantity',
          width: '150px',
          render: (record) => {
            const {
              isFlexible, updateRawMaterialLines, moLines, disableQuantityChange,
            } = this.props;

            return (
              <Fragment>
                {!record?.tenant_product_info?.product_batches?.map((item) => item.available_qty > 0)?.length > 0 ? <div className="input-error">OUT OF STOCK</div> : (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <H3FormInput
                      value={record.batch_quantity}
                      type="number"
                      disabled={!this.props.user?.tenant_info?.production_config?.sub_modules?.manufacturing_order?.settings?.allow_flexible_consumption_in_mo || disableQuantityChange}
                      labelClassName="orgFormLabel"
                      inputClassName="orgFormInput"
                      name="quantity is more than available quantity"
                      onChange={(e) => {
                        let remainingQty = Number(e.target.value) + Number(record.wastage);
                        const batches = record?.tenant_product_info?.product_batches;
                        const copyData = JSON.parse(JSON.stringify(moLines));
                        for (let i = 0; i < batches?.length; i++) {
                          batches[i].consumed_qty = 0;
                        }
                        for (let i = 0; i < batches?.length; i++) {
                          if (record.goodStockOnly && batches[i]?.is_rejected_batch) continue;

                          if (batches[i]?.batch_in_use) {
                            if (remainingQty > 0 && Number(batches[i].consumed_qty || '0') < batches?.[i]?.available_qty) {
                              batches[i].consumed_qty = batches?.[i]?.available_qty > remainingQty ? remainingQty : batches?.[i]?.available_qty;
                              remainingQty -= batches[i].consumed_qty;
                            } else {
                              batches[i].consumed_qty = 0;
                              remainingQty -= batches[i].consumed_qty;
                            }
                          }
                        }
                        copyData.map((item) => {
                          if (item.key === record.key) {
                            item.batch_quantity = e.target.value;
                            item.tenant_product_info.product_batches = batches;
                          }
                          return moLines;
                        });
                        updateRawMaterialLines(copyData);
                      }}
                    />
                    <div style={{ marginLeft: '6px' }}>
                      {record?.uom_info?.uqc?.toProperCase()}
                    </div>
                  </div>
                )}
                {(record?.tenant_product_info?.product_batches?.map((item) => item.available_qty > 0)?.length === 0 || (Number(record?.batch_quantity) + Number(record.wastage)) > Helpers.getValueTotalInObject(record?.tenant_product_info?.product_batches?.filter((batchData) => (batchData?.batch_in_use && (record.goodStockOnly ? !batchData?.is_rejected_batch : true))), 'available_qty')) && <H3Text text="*Sufficient stock not available" className="input-error" />}
              </Fragment>
            );
          },
        },
        {
          title: 'Overage', // -> Wastage
          width: '150px',
          render: (record) => {
            const {
              isFlexible, updateRawMaterialLines, moLines, disableQuantityChange,
            } = this.props;
            return (
              <Fragment>
                {!record?.tenant_product_info?.product_batches?.map((item) => item.available_qty > 0)?.length > 0 ? <div className="input-error">OUT OF STOCK</div> : (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <H3FormInput
                      value={record.wastage}
                      type="number"
                      labelClassName="orgFormLabel"
                      inputClassName="orgFormInput"
                      name="quantity is more than available quantity"
                      onChange={(e) => {
                        let remainingQty = Number(e.target.value) + Number(record.batch_quantity);
                        const batches = record?.tenant_product_info?.product_batches;
                        const copyData = JSON.parse(JSON.stringify(moLines));
                        for (let i = 0; i < batches?.length; i++) {
                          batches[i].consumed_qty = 0;
                        }
                        for (let i = 0; i < batches?.length; i++) {
                          if (batches[i]?.batch_in_use) {
                            if (remainingQty > 0 && Number(batches[i].consumed_qty || '0') < batches?.[i]?.available_qty) {
                              batches[i].consumed_qty = batches?.[i]?.available_qty > remainingQty ? remainingQty : batches?.[i]?.available_qty;
                              remainingQty -= batches[i].consumed_qty;
                            } else {
                              batches[i].consumed_qty = 0;
                              remainingQty -= batches[i].consumed_qty;
                            }
                          }
                        }
                        copyData.map((item) => {
                          if (item.key === record.key) {
                            item.wastage = e.target.value;
                            item.tenant_product_info.product_batches = batches;
                          }
                          return moLines;
                        });
                        updateRawMaterialLines(copyData);
                      }}
                      disabled={disableQuantityChange}
                    />
                    <div style={{ marginLeft: '6px' }}>
                      {record?.uom_info?.uqc?.toProperCase()}
                    </div>
                  </div>
                )}
                {record?.tenant_product_info?.product_batches?.map((item) => item.available_qty > 0)?.length === 0 && record?.batch_quantity > record?.tenant_product_info?.total_available_batches_quantity && <H3Text text="*Sufficient stock not available" className="input-error" />}
              </Fragment>
            );
          },
        },
      ],
    };
  }

  /**
   *
   * @param {*} data
   * @return
   */
  getDataSource(data) {
    if (!data) return [];

    // Deep copy of the data
    const clonedData = JSON.parse(JSON.stringify(data));

    // Adding the new key to each object in the data array
    return clonedData;
  }

  // getBatches = (selectedQty, productBatches) => {
  //   let remainingQty = Number(selectedQty);
  //   const batches = JSON.parse(JSON.stringify(productBatches));
  //   for (let i = 0; i < batches?.length; i++) {
  //     batches[i].consumed_qty = 0;
  //     // batches[i].batch_in_use = true;
  //   }
  //   for (let i = 0; i < batches?.length; i++) {
  //     if (batches[i]?.batch_in_use) {
  //       if (remainingQty > 0 && Number(batches[i].consumed_qty || '0') < batches?.[i]?.available_qty) {
  //         batches[i].consumed_qty = batches?.[i]?.available_qty > remainingQty ? remainingQty : batches?.[i]?.available_qty;
  //         remainingQty -= batches[i].consumed_qty;
  //       } else {
  //         batches[i].consumed_qty = 0;
  //         remainingQty -= batches[i].consumed_qty;
  //       }
  //     }
  //   }
  //   return batches;
  // };

  getBatches = (selectedQty, productBatches) => {
    let remainingQty = new Decimal(selectedQty);
    const batches = JSON.parse(JSON.stringify(productBatches));
    for (let i = 0; i < batches?.length; i++) {
      batches[i].consumed_qty = 0;
      // batches[i].batch_in_use = true;
    }

    for (let i = 0; i < batches?.length; i++) {
      if (batches[i]?.batch_in_use) {
        const availableQty = new Decimal(batches[i]?.available_qty || 0);
        const consumedQty = new Decimal(batches[i]?.consumed_qty || 0);

        if (remainingQty.gt(0) && consumedQty.lt(availableQty)) {
          const qtyToConsume = Decimal.min(availableQty, remainingQty);
          batches[i].consumed_qty = qtyToConsume.toNumber();
          remainingQty = remainingQty.minus(new Decimal(batches[i].consumed_qty));
        } else {
          batches[i].consumed_qty = 0;
          remainingQty = remainingQty.minus(new Decimal(batches[i].consumed_qty));
        }
      }
    }
    return batches;
  };

  /**
   *
   * @param record
   * @param productBatchesRow
   */
  toggleBatch(record, productBatchesRow, checkedValue) {
    const { moLines, updateRawMaterialLines } = this.props;

    if (checkedValue) {
      for (let i = 0; i < moLines?.length; i++) {
        if (moLines[i]?.key === productBatchesRow?.key) {
          const rowBatches = moLines[i]?.tenant_product_info?.product_batches;
          for (let j = 0; j < rowBatches?.length; j++) {
            if (rowBatches[j]?.batch_id === record?.batch_id) {
              rowBatches[j].batch_in_use = !rowBatches[j]?.batch_in_use;
              const newBatchesData = this.getBatches((Number(moLines[i].batch_quantity) + Number(moLines[i].wastage)), moLines[i]?.tenant_product_info?.product_batches);
              moLines[i].tenant_product_info.product_batches = newBatchesData;
            }
          }
          break;
        }
      }
      updateRawMaterialLines(moLines);
    } else {
      for (let i = 0; i < moLines?.length; i++) {
        if (moLines[i]?.key === productBatchesRow?.key) {
          const rowBatches = moLines[i]?.tenant_product_info?.product_batches;
          for (let j = 0; j < rowBatches?.length; j++) {
            if (rowBatches[j]?.batch_id === record?.batch_id) {
              rowBatches[j].batch_in_use = !rowBatches[j]?.batch_in_use;
              // Only subtract if `batch_quantity` is greater than or equal to `consumed_qty`
              // if (moLines[i].batch_quantity >= rowBatches[j].consumed_qty) {
              //   moLines[i].batch_quantity -= rowBatches[j].consumed_qty;
              // } else {
              const newBatchesData = this.getBatches((Number(moLines[i].batch_quantity) + Number(moLines[i].wastage)), moLines[i]?.tenant_product_info?.product_batches);
              moLines[i].tenant_product_info.product_batches = newBatchesData;
              // }
              rowBatches[j].consumed_qty = 0;
            }
          }
          break;
        }
      }
    }
    updateRawMaterialLines(moLines);
  }

  updateBatchValue(key, value, label, batchId) {
    const {
      updateRawMaterialLines, moLines,
    } = this.props;
    const copyData = JSON.parse(JSON.stringify(moLines));
    const updatedData = copyData.map((obj) => {
      if (obj.key === key) {
        let productBatches = obj?.tenant_product_info?.product_batches;
        productBatches = productBatches?.map((productBatch) => {
          if (productBatch.batch_id === batchId) {
            // Check if label is 'consumed_qty' and apply logic based on available_qty
            if (label === 'consumed_qty') {
              return {
                ...productBatch,
                [label]: value > productBatch?.available_qty ? productBatch?.available_qty : value,
              };
            }
            // For other labels, simply update with the provided value
            return {
              ...productBatch,
              [label]: value,
            };
          }
          return productBatch;
        });


        // const qty = productBatches.reduce((sum, batch) => sum + (Number(batch.consumed_qty) || 0), 0);
        const qty = productBatches.reduce(
          (sum, batch) => sum.plus(new Decimal(batch.consumed_qty || 0)),
          new Decimal(0)
        );
        return {
          ...obj,
          tenant_product_info: {
            ...obj.tenant_product_info,
            product_batches: productBatches,
          },
          batch_quantity: qty,
        };
      }
      return obj;
    });
    updateRawMaterialLines(updatedData);
  }

  render() {
    const {
      createMOAdjustmentLoading, getMOByIdLoading, moLines, loading, updateRawMaterialLines, setGoodStockOnlyFromProps
    } = this.props;
    const { rawMaterialColumns, showPRZModal, selectedDocumentId } = this.state;
    return (
      <div className="ant-row">
        <div className="ant-col-md-24">
          <div className="rm-table-wrapper">
            <Table
              showHeader
              title={() => `Raw Materials (${this.getDataSource(moLines)?.length})`}
              size="small"
              columns={rawMaterialColumns}
              dataSource={this.getDataSource(moLines) || []}
              loading={getMOByIdLoading || createMOAdjustmentLoading || loading}
              pagination={false}
              expandable={{
                rowExpandable: (record) => record?.tenant_product_info?.product_batches?.map((item) => item.available_qty > 0)?.length,
                expandedRowRender: (record) => (
                  <RawMaterialBatchSelector
                    allStock={record.goodStockOnly}
                    batches={record?.tenant_product_info?.product_batches}
                    uomInfo={record?.uom_info}
                    expiryDays={record?.tenant_product_info?.expiry_days}
                    itemsRow={record}
                    updateBatchValue={(value, label, batchId) => this.updateBatchValue(record?.key, value, label, batchId)}
                    onToggleBatch={(batch, productBatchesRow, checkedValue) => this.toggleBatch(batch, productBatchesRow, checkedValue)}
                    updateAllStock={(isGoodStockOnly) => {
                      for (let i = 0; i < moLines?.length; i++) {
                        if (moLines[i]?.key === record?.key) {
                          const rowBatches = moLines[i]?.tenant_product_info?.product_batches;
                          for (let j = 0; j < rowBatches?.length; j++) {
                            if (isGoodStockOnly && rowBatches[j]?.is_rejected_batch) {
                              rowBatches[j].batch_in_use = false;
                            } else if (!isGoodStockOnly && rowBatches[j]?.is_rejected_batch) {
                              rowBatches[j].batch_in_use = true;
                            }
                            // rowBatches[j].consumed_qty = 0;
                          }
                          // moLines[i].batch_quantity = 0;
                          const newBatchesData = this.getBatches((Number(moLines[i].batch_quantity) + Number(moLines[i].wastage)), moLines[i]?.tenant_product_info?.product_batches);
                          moLines[i].tenant_product_info.product_batches = newBatchesData;
                          moLines[i].goodStockOnly = isGoodStockOnly;
                        }
                      }
                      updateRawMaterialLines(moLines);
                    }}
                  />
                ),
              }}
              scroll={{ x: true }}
            />
          </div>
        </div>
      <PRZModal
        isOpen={showPRZModal}
        onClose={() => {
          this.setState({ showPRZModal: false, selectedDocumentId: null });
        }}
        entityType="product"
        documentId={selectedDocumentId}
      />
      </div>
    );
  }
}

const mapStateToProps = ({
  UserReducers, MOReducers,
}) => ({
  user: UserReducers.user,
  createMOAdjustmentLoading: MOReducers.createMOAdjustmentLoading,
  getMOByIdLoading: MOReducers.getMOByIdLoading,
});

const mapDispatchToProps = () => ({
});

UpdateRawMaterial.propTypes = {
  moLines: PropTypes.any,
  getMOByIdLoading: PropTypes.any,
  createMOAdjustmentLoading: PropTypes.any,
  updateRawMaterialLines: PropTypes.func,
  isFlexible: PropTypes.bool,
  user: PropTypes.any,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(UpdateRawMaterial));
