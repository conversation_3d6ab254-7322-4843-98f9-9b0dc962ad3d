import Helpers from '@Apis/helpers';
import Decimal from 'decimal.js';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import { DEFAULT_CUR_ROUND_OFF, QUANTITY } from '@Apis/constants';
import { notification } from 'antd';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import FormHelpers from '@Helpers/FormHelpers';
import InvoiceErrorList from './InvoiceErrors';

export const INITIAL_STATE = (user) => ({
  data: [
    {
      key: uuidv4(),
      asset1: '',
      product_sku_name: '',
      quantity: '',
      unitPrice: '',
      lot: 0,
      taxId: '',
      discount: 0,
      child_taxes: [{
        tax_amount: 0,
        tax_type_name: '',
      }],
      lineCustomFields: [],
    },
  ],
  chargeData: [],
  paymentTerms: 0,
  paymentRemark: "",
  checkedRecipients: false,
  toRecipients: [],
  poDate: dayjs(),
  invoiceDate: dayjs(),
  allStock: true,
  isLineWiseDiscount: true,
  showAddCustomer: true,
  showNewCustomerModal: false,
  pricesToApply: [],
  termsAndConditions: '',
  taxTypeName: 'TDS',
  selectedCurrencyName: '',
  selectedCurrencyID: '',
  sendWhatsappNotification: false,
  dontDeductStock: false,
  charge1Name: 'Freight',
  charge1Value: '',
  customVoucherNumber: '',
  createExpenseCheckbox: false,
  transporterId: null,
  vehicleNumber: '',
  transporterBillNumber: null,
  tncId: null,
  salesAccount: user?.tenant_info?.tally_configuration?.sales_account_name,
  visibleColumns: {
    PRODUCT: {
      label: 'Product',
      visible: true,
      disabled: true,
    },
    AVAILABLE_STOCK: {
      label: 'Available Stock',
      visible: true,
      disabled: true,
    },
    QUANTITY: {
      label: 'Quantity',
      visible: true,
      disabled: true,
    },
    UNIT_PRICE: {
      label: 'Unit Price',
      visible: true,
      disabled: true,
    },
    TAX: {
      label: 'TAX',
      visible: true,
      disabled: true,
    },
    DISCOUNT: {
      label: 'Discount',
      visible: true,
      disabled: true,
    },
    SALES_ACCOUNT: {
      label: 'Sales Account',
      visible: true,
      disabled: true,
    },
    LINE_TOTAL: {
      label: 'Line Total',
      visible: true,
      disabled: true,
    },
  },
  freightTaxId: 'Not Applicable',
  freightTax: null,
  openFreightTax: false,
  staticReservations: {},
  selectedWorkOrder: '',
  sellerId: '',
  selectedFg: [],
  calculateRateFromBatches: user?.tenant_info?.sales_config?.sub_modules?.invoice?.settings?.auto_calculate_selling_price_from_selected_batches,
  createDCAutomatically: false,
  isAutomaticConversionRate: null,
  currencyConversionRate: '',
  updateDocumentReason: '',
  narration: '',
  freightTaxData: {
    child_taxes: [
      {
        tax_amount: 0,
        tax_type_name: '',
      },
    ],
  },
  productSkuIds: [],
});

export const clearLineReservations = (lineKey) => {
  const staticReservations = JSON.parse(sessionStorage.getItem('staticReservations'));
  const staticReservationCopy = JSON.parse(JSON.stringify(staticReservations));
  Object.keys(staticReservationCopy)?.forEach((batchKey) => {
    if (staticReservationCopy?.[batchKey]?.[lineKey] !== undefined) {
      delete staticReservationCopy?.[batchKey]?.[lineKey];
    }
  });
  sessionStorage.setItem('staticReservations', JSON.stringify(staticReservationCopy));
};

export const getBatches = (
  oldBatches,
  quantity,
  lineUomInfo,
  productUomInfo,
  barcodeBatchId,
  lineKey,
  resetNotRequired,
  allStock,
  cBatchId,
  cBatchQty
) => {
  const batches = oldBatches?.map((batch) => ({ ...batch }));
  let remainingQty = new Decimal(quantity);
  const staticReservations = JSON.parse(sessionStorage.getItem('staticReservations'));
  const staticReservationCopy = JSON.parse(JSON.stringify(staticReservations));

  const clearLineReservations = () => {
    if (cBatchId) {
      // Delete only the specified batchKey for the given lineKey if cBatchId is present
      delete staticReservationCopy?.[cBatchId]?.[lineKey];
    } else {
      // Delete all batchKeys for the given lineKey
      Object.keys(staticReservationCopy)?.forEach((batchKey) => (
        delete staticReservationCopy?.[batchKey]?.[lineKey]
      ));
    }
  };
  clearLineReservations();
  const getBatchReservations = (batchId) => {
    let total = new Decimal(0);
    if (
      staticReservationCopy?.hasOwnProperty(Number(batchId)) &&
      Object.keys(staticReservationCopy[batchId])?.length > 0
    ) {
      Object.keys(staticReservationCopy[batchId])?.forEach((item) => {
        total = total.plus(new Decimal(staticReservationCopy[batchId][item]));
      });
    }
    return {
      total: total.toNumber(),
      reservations: staticReservationCopy[batchId],
    };
  };
  const addBatchReservation = (batchId, qty) => {
    const decimalQty = new Decimal(qty).toNumber();
    if (staticReservationCopy?.hasOwnProperty(batchId)) {
      if (staticReservationCopy[batchId]?.hasOwnProperty(lineKey))
        staticReservationCopy[batchId][lineKey] += decimalQty;
      else staticReservationCopy[batchId][lineKey] = decimalQty;
    } else {
      staticReservationCopy[batchId] = {
        [lineKey]: decimalQty,
      };
    }
  };

  for (let i = 0; i < batches?.length; i++) {
    const batchReservations = getBatchReservations(batches[i]?.batch_id);
    batches[i]._reserved = batchReservations;
    if (!resetNotRequired) {
      batches[i].batch_in_use = batchReservations?.batch_in_use || true;
    }
    batches[i].consumed_qty = cBatchId ? batches[i].consumed_qty || 0 : 0;
  }

  if (cBatchId) {
    // Allocate the given cBatchQty to the corresponding batch
    for (let i = 0; i < batches?.length; i++) {
      if (batches[i]?.batch_id === cBatchId) {
        // const avlQty = ((Number(batches?.[i]?.available_qty) - Number(batches?.[i]?._reserved?.total)) / lineUomInfo?.ratio) * productUomInfo?.ratio;
        const convertedQty = new Decimal(
          FormHelpers.uomConversion(productUomInfo, lineUomInfo, batches?.[i]?.available_qty) || 0
        );

        const reservedQty = new Decimal(batches?.[i]?._reserved?.total || 0);

        const avlQty = QUANTITY(convertedQty.minus(reservedQty).toNumber(), lineUomInfo?.precision);

        const allocatedQty = Decimal.min(new Decimal(cBatchQty || 0), new Decimal(avlQty));
        batches[i].consumed_qty = allocatedQty.toNumber();
        batches[i].batch_in_use = true;
        remainingQty = remainingQty.minus(allocatedQty);
        addBatchReservation(batches[i]?.batch_id, allocatedQty);
        break;
      }
    }
  } else if (barcodeBatchId) {
    for (let i = 0; i < batches?.length; i++) {
      const convertedQty = new Decimal(
        FormHelpers.uomConversion(productUomInfo, lineUomInfo, batches?.[i]?.available_qty)
      );

      const reservedQty = new Decimal(batches?.[i]?._reserved?.total || 0);

      const avlQty = QUANTITY(convertedQty.minus(reservedQty).toNumber(), lineUomInfo?.precision);

      if (batches[i]?.batch_id === barcodeBatchId) {
        const consumed = Decimal.min(avlQty, remainingQty);
        batches[i].consumed_qty = consumed.toNumber();
        batches[i].batch_in_use = true;
        remainingQty = remainingQty.minus(consumed);
        addBatchReservation(batches[i]?.batch_id, consumed);
      }
    }
  } else {
    for (let i = 0; i < batches?.length; i++) {
      // Skip the batch if allStock is true and the batch is marked as a rejected batch
      if (allStock && batches[i]?.is_rejected_batch) continue;

      // const avlQty = ((Number(batches?.[i]?.available_qty) - Number(batches?.[i]?._reserved?.total)) / lineUomInfo?.ratio) * productUomInfo?.ratio;
      const convertedQty = new Decimal(
        FormHelpers.uomConversion(productUomInfo, lineUomInfo, batches?.[i]?.available_qty) || 0
      );

      const reservedQty = new Decimal(batches?.[i]?._reserved?.total || 0);

      const avlQty = QUANTITY(convertedQty.minus(reservedQty).toNumber(), lineUomInfo?.precision);

      if (batches?.[i]?.batch_in_use) {
        const consumedQty = new Decimal(batches[i]?.consumed_qty || '0');
        if (remainingQty.greaterThan(0) && consumedQty.lessThan(avlQty)) {
          const consumed = Decimal.min(avlQty, remainingQty);
          batches[i].consumed_qty = consumed.toNumber();
          batches[i].batch_in_use = true;
          remainingQty = remainingQty.minus(consumed);
          addBatchReservation(batches[i]?.batch_id, consumed);
        } else {
          batches[i].consumed_qty = 0;
          batches[i].batch_in_use = true;
          remainingQty = remainingQty.minus(batches[i]?.consumed_qty);
        }
      }
    }
  }
  sessionStorage.setItem('staticReservations', JSON.stringify(staticReservationCopy));
  return batches?.map((item) => ({
    ...item,
    _reserved: getBatchReservations(item?.batch_id),
  }));
};

export const getLineTotals = (data, chargeData, taxTypeInfo, taxTypeName, charge1Value, freightTax, freightTaxData) => {
  let totalAmount = 0;
  let totalDiscount = 0;
  let totalBase = 0;
  let totalTcs = 0;
  let totalTds = 0;
  let totalTax = 0;
  let totalOtherCharge = 0;
  let invoiceTotal = 0;
  let freightCharge = Number(charge1Value);
  let freightTaxAmount = 0;
  let totalOtherChargesTaxableAmount = 0;

  // Taxes Bifurcation
  const totalTaxValue = Helpers.groupAndSumByTaxName(data?.map((item) => item?.child_taxes)?.flat())?.reduce((acc, curr) => acc + curr?.tax_amount, 0);

  for (let i = 0; i < chargeData?.length; i++) {
    let currentOtherCharge = 0;
    let currentChargesTaxAmount = 0;

    // Check if the current charge has a valid charge amount
    if (chargeData[i]?.charge_amount) {
      // Calculate the total tax amount for the current charge line
      currentChargesTaxAmount = Helpers.groupAndSumByTaxName(
        [...(chargeData[i]?.chargesTaxData?.child_taxes || []).flat()],
      )?.reduce((acc, curr) => acc + (curr?.tax_amount || 0), 0);
      // Calculate the total current other charge including tax, if applicable
      currentOtherCharge = chargeData[i]?.chargesTax
        ? chargeData[i].charge_amount + currentChargesTaxAmount
        : chargeData[i].charge_amount;

      // If chargesTax is true, add the charge amount to the taxable total
      if (chargeData[i]?.chargesTax) {
        totalOtherChargesTaxableAmount += Number(chargeData[i].charge_amount);
      }
    }
    // Add the current charge (including tax if applicable) to the total charges
    totalOtherCharge += currentOtherCharge;
  }

  for (let i = 0; i < data?.length; i++) {
    let currentAmount = 0;
    let currentDiscount = 0;
    let currentBase = 0;
    let currentTax = 0;
    let currentInvoice = 0;
    if (data[i].quantity) {
      const discountValue = Number(data[i].discount) || 0;
      currentAmount += (data[i].quantity * data[i].unitPrice);
      currentDiscount += data[i].discount ? (data?.[i]?.lineDiscountType == 'Percent' ? (data[i].quantity * data[i].unitPrice) * (data[i].discount / 100) : discountValue) : 0;
      currentBase += data?.[i]?.discount ? (data?.[i]?.lineDiscountType == 'Percent' ? (data[i].quantity * data[i].unitPrice) * (data[i].discount ? Number(100 - data[i].discount) / 100 : 1) : (data[i].quantity * data[i].unitPrice) - discountValue) : data?.[i]?.quantity * data?.[i]?.unitPrice;
    }
    // Subtract TDS from currentBase or Add TCS
    if (taxTypeInfo && taxTypeName === 'TCS') {
      const tcsRate = taxTypeInfo?.tax_value / 100;
      const tcsAmount = currentBase * tcsRate;
      totalTcs += tcsAmount;
    } else if (taxTypeInfo && taxTypeName === 'TDS') {
      const tdsRate = taxTypeInfo?.tax_value / 100;
      const tdsAmount = currentBase * tdsRate;
      totalTds -= tdsAmount;
    }
    if (data[i].taxId && data[i].taxInfo?.tax_value) currentTax += currentBase * (data?.[i]?.taxId ? data[i].taxInfo?.tax_value / 100 : 0);
    if (currentBase) currentInvoice = currentBase;
    totalAmount += currentAmount;
    totalDiscount += currentDiscount;
    totalBase += currentBase;
    totalTax += currentTax;
    invoiceTotal += currentInvoice;
  }

  invoiceTotal += totalTaxValue;

  if (freightTax) {
    freightTaxAmount = Helpers.groupAndSumByTaxName([...freightTaxData?.child_taxes?.flat()])?.reduce((acc, curr) => acc + curr?.tax_amount, 0);

    totalBase += freightCharge;
    freightCharge += freightTaxAmount;
    // Subtract TDS from currentBase or Add TCS
    if (taxTypeInfo && taxTypeName === 'TCS') {
      const tcsRate = taxTypeInfo?.tax_value / 100;
      totalTcs = totalBase * tcsRate;
    } else if (taxTypeInfo && taxTypeName === 'TDS') {
      const tdsRate = taxTypeInfo?.tax_value / 100;
      totalTds = -((totalBase + totalOtherChargesTaxableAmount) * tdsRate);
    }
  }

  // Add TCS from to total amount including GST ,Freight Other charges
  if (taxTypeInfo && taxTypeName === 'TCS') {
    const tcsRate = taxTypeInfo?.tax_value / 100;

    const tcsAmount = (totalBase + totalTaxValue + Number(totalOtherCharge) + (freightTax ? freightTaxAmount : freightCharge)) * tcsRate;
    totalTcs = tcsAmount;
  }

  invoiceTotal += (taxTypeName === 'TCS' ? totalTcs : totalTds);
  invoiceTotal += totalOtherCharge;
  invoiceTotal += freightCharge;
  totalBase += totalOtherChargesTaxableAmount;
  return {
    totalAmount,
    totalDiscount,
    totalBase,
    totalTcs,
    totalTds,
    totalTax,
    invoiceTotal,
  };
};

export const getAvailableQty = (record, productBatches, state, props, setState) => {
  let availableQty;
  if (record?.bundle_products?.length) {
    const arr = [];
    for (let i = 0; i < record?.bundle_products?.length; i++) {
      const lineKey = record?.bundle_products?.[i]?.key;
      const bundleProductBatches = getBatches(record?.bundle_products?.[i]?.available_batches, Math.min(record?.bundle_products?.[i]?.quantity, Helpers.getValueTotalInObject(record?.bundle_products?.[i]?.available_batches, 'available_qty')), record?.bundle_products?.[i]?.uom_info?.[0], record?.bundle_products?.[i]?.product_sku_info?.uom_info, null, lineKey, state, props, setState);
      const qty = Helpers.getValueTotalInObject(bundleProductBatches?.filter((item) => item?.batch_in_use), 'available_qty');
      arr.push(qty / (record?.bundle_products[i]?.quantity / record?.quantity));
    }
    availableQty = Math.min(...arr);
  } else {
    availableQty = Helpers.getValueTotalInObject(productBatches?.filter((item) => item?.batch_in_use), 'available_qty');
  }
  return QUANTITY(availableQty, record?.uom_info?.[0]?.precision);
};

export const getDraftStatusType = (orgStatusInvoice) => {
  const orgStatusData = orgStatusInvoice?.statuses;
  return orgStatusData?.filter((status) => status?.type === 'DRAFT');
};

const createDefaultRow = ({
  dataItem, product, tenantSku, productData, existingSameProduct, toApplyPriceList, calculateRateFromBatches, user, billingAddress, autoPrintDescription, isLineWiseDiscount, discountPercentage, cfInvoiceLine, discountType,
}) => {
  const parentKey = uuidv4();
  const copyDataItem = dataItem;
  const uomId = existingSameProduct ? existingSameProduct?.uomId : product?.product_info?.uom_id || 0;
  const uomInfo = existingSameProduct ? existingSameProduct?.uom_info : product?.uom_info;
  const uomList = existingSameProduct ? existingSameProduct?.uom_list : product?.uom_list;
  const uomGroup = existingSameProduct ? existingSameProduct?.uomGroup : product?.group_id;

  const pBatches = getBatches(product?.product_batches, 1, uomInfo, product?.product_info?.uom_info, tenantSku?.barcode_batch_id, parentKey);
  const firstBatchSp = pBatches?.filter((i) => i?.batch_in_use)?.[0]?.selling_price;
  copyDataItem.key = parentKey;
  copyDataItem.tenant_product_id = product?.tenant_product_id;
  copyDataItem.product_category_info = product?.product_category_info;
  copyDataItem.product_sku_name = product?.product_info.product_sku_name;
  copyDataItem.hsn_code = product?.product_info.hsn_code;
  copyDataItem.product_sku_id = product?.product_info.product_sku_id;
  copyDataItem.product_type = product?.product_type;
  copyDataItem.unitPrice = calculateRateFromBatches ? firstBatchSp : (product?.selling_price || 0);
  copyDataItem.sku = product?.product_info?.product_sku_id || '';
  copyDataItem.taxInfo = tenantSku?.tax_info;
  copyDataItem.child_taxes = Helpers.computeTaxation((1 * tenantSku?.selling_price), tenantSku?.tax_info, user?.tenant_info?.state, billingAddress?.state)?.tax_info?.child_taxes;
  copyDataItem.taxId = product?.product_info?.tax_id;
  copyDataItem.lot = product?.total_price || 0;
  copyDataItem.uomId = uomId;
  copyDataItem.uom_info = uomInfo;
  copyDataItem.uomInfo = uomInfo;
  copyDataItem.uom_list = uomList;
  copyDataItem.uomGroup = uomGroup;
  copyDataItem.batchConsumptionMethod = product?.default_outwards_method || Helpers.getBatchMethod(product);
  copyDataItem.product_batches = pBatches;
  copyDataItem.expiry_days = product?.product_info?.expiry_days;
  copyDataItem.so_quantity = 1;
  copyDataItem.quantity = 1;
  copyDataItem.remarks = autoPrintDescription ? (productData?.description || '')?.replace(/<[^>]+>/g, '') : '';
  copyDataItem.remarkRequired = autoPrintDescription && productData?.description?.replace(/<[^>]+>/g, '');
  copyDataItem.discount = !isLineWiseDiscount ? discountPercentage : 0;
  copyDataItem.lineDiscountType = !isLineWiseDiscount ? discountType : 'Percent';
  copyDataItem.lineCustomFields = FormHelpers.lineSystemFieldValue(cfInvoiceLine, [...(tenantSku?.custom_fields ?? []), ...(productData?.system_custom_fields ?? [])])?.map((k) => ({
    ...k,
    fieldValue: k?.fieldName === 'Rate' ? copyDataItem.unitPrice : (k?.fieldName === 'Quantity' ? copyDataItem.quantity : k?.fieldValue),
  }));
  copyDataItem.bundle_products = product?.bundle_products?.map((item) => {
    const lineKey = uuidv4();
    return {
      ...item,
      key: lineKey,
      parentKey,
      unitPrice: QUANTITY(item?.unit_price) || 0,
      uomId: Number(item?.bundle_uom_id),
      uomGroup: Number(item?.group_id),
      uom_info: item?.bundle_uom_info,
      taxId: Number(item?.tax_info?.tax_id),
      taxInfo: item?.tax_info,
      product_sku_name: item?.product_info?.product_sku_name,
      so_quantity: item?.bundle_quantity,
      expiry_days: item?.product_info?.expiry_days,
      product_sku_info: item?.product_info,
      product_batches: getBatches(item?.product_batches, item?.bundle_quantity, item?.bundle_uom_info, item?.product_info?.uom_info, null, lineKey),
    };
  });
  copyDataItem.product_sku_info = {
    ...product?.product_info,
    expiry_days: product?.product_info?.expiry_days,
    uom_info: product?.uom_info,
  };
  copyDataItem.manufacturingDateFormat = product?.product_info?.manufacturing_date_format;
  copyDataItem.expiryDateFormat = product?.product_info?.expiry_date_format;

  if (toApplyPriceList && toApplyPriceList?.product_sku_id === tenantSku?.product_info.product_sku_id && copyDataItem !== undefined) {
    copyDataItem.unitPrice = calculateRateFromBatches ? firstBatchSp : (toApplyPriceList?.is_inclusive_of_tax ? toApplyPriceList?.price_list_amount / ((100 + tenantSku?.product_info?.tax_value) / 100) : toApplyPriceList?.price_list_amount);

    copyDataItem.discount = toApplyPriceList?.discount_percentage;

    copyDataItem.child_taxes = Helpers.computeTaxation((1 * tenantSku?.selling_price) * (toApplyPriceList?.discount_percentage ? Number(100 - toApplyPriceList?.discount_percentage) / 100 : 1), tenantSku?.tax_info, user?.tenant_info?.state, billingAddress?.state)?.tax_info?.child_taxes;
  }

  return copyDataItem;
};

export const handleProductChangeHelper = (state, props, tenantSku, key, productData, setState) => {
  const {
    data, tenantDepartmentId, selectedCurrentInvoice, isLineWiseDiscount, discountPercentage, pricesToApply, selectedPriceList, selectedCustomer, shippingAddress, billingAddress, cfInvoiceLine, calculateRateFromBatches, selectedTenant, discountType,
  } = state;
  const {
    getProductById, user, selectedOrderForInvoice, tenantId, getApplyPriceLists,
  } = props;
  const autoPrintDescription = user?.tenant_info?.global_config?.settings?.print_description_automatically;
  getProductById(selectedTenant || selectedOrderForInvoice?.tenant_id || selectedCurrentInvoice?.tenant_id || tenantId || user?.tenant_info?.tenant_id, tenantSku?.product_sku_id, tenantDepartmentId, (product) => {
    if (product?.product_type === 'BUNDLE' && product?.bundle_products?.length === 0) {
      notification.open({
        message: 'This product is a bundle but it does not have any child components.',
        duration: 4,
        type: 'error',
        placement: 'top',
      });
      return {
        updateState: false,
      };
    } if (selectedPriceList) {
      getApplyPriceLists(selectedTenant || selectedOrderForInvoice?.tenant_id || selectedCurrentInvoice?.tenant_id || tenantId || user?.tenant_info?.tenant_id, selectedPriceList, selectedCustomer, shippingAddress?.state, tenantSku?.product_sku_id, (toApplyPriceList) => {
        const existingSameProduct = data?.find((item) => item?.product_sku_id === product?.product_sku_id);
        const copyData = JSON.parse(JSON.stringify(data));
        for (let i = 0; i < copyData?.length; i++) {
          if (copyData[i].key === key) {
            const copyDataItem = createDefaultRow({
              dataItem: copyData[i],
              product,
              tenantSku,
              productData,
              existingSameProduct,
              toApplyPriceList: toApplyPriceList?.[0],
              calculateRateFromBatches,
              user,
              billingAddress,
              autoPrintDescription,
              isLineWiseDiscount,
              discountPercentage,
              cfInvoiceLine,
              discountType,
            });
            copyData[i] = copyDataItem;
          }
        }

        if (!isLineWiseDiscount) {
          const totalValue = copyData?.reduce((acc, cur) => acc + (cur?.quantity * cur?.unitPrice), 0);

          copyData?.map((item) => {
            const discountValue = state.discountType === 'Percent' ? parseFloat(state.discountPercentage) : ((item?.quantity * item?.unitPrice) / parseFloat(totalValue)) * parseFloat(state.discountPercentage);
            const taxableValue = state.discountType === 'Percent' ? (item?.quantity * item?.unitPrice) * (1 - discountValue / 100) : Math.max(item?.quantity * item?.unitPrice - discountValue, 0);

            item.discount = discountValue;
            item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, props.user?.tenant_info?.state, state.billingAddress?.state)?.tax_info?.child_taxes;
          });
        }
        setState({
          data: copyData,
          productSkuIds: copyData?.map((productSkuId) => productSkuId?.product_sku_id),
          pricesToApply: [...pricesToApply, ...toApplyPriceList],
        });
      });
    } else {
      const existingSameProduct = data?.find((item) => item?.product_sku_id === product?.product_sku_id);
      const copyData = JSON.parse(JSON.stringify(data));
      for (let i = 0; i < copyData?.length; i++) {
        if (copyData[i].key === key) {
          const copyDataItem = createDefaultRow({
            dataItem: copyData[i],
            product,
            tenantSku,
            productData,
            existingSameProduct,
            calculateRateFromBatches,
            user,
            billingAddress,
            autoPrintDescription,
            isLineWiseDiscount,
            discountPercentage,
            cfInvoiceLine,
            discountType,
          });
          copyData[i] = copyDataItem;
        }
      }

      if (!isLineWiseDiscount) {
        const totalValue = copyData?.reduce((acc, cur) => acc + (cur?.quantity * cur?.unitPrice), 0);

        copyData?.map((item) => {
          const discountValue = state.discountType === 'Percent' ? parseFloat(state.discountPercentage) : ((item?.quantity * item?.unitPrice) / parseFloat(totalValue)) * parseFloat(state.discountPercentage);
          const taxableValue = state.discountType === 'Percent' ? (item?.quantity * item?.unitPrice) * (1 - discountValue / 100) : Math.max(item?.quantity * item?.unitPrice - discountValue, 0);

          item.discount = discountValue;
          item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, props.user?.tenant_info?.state, state.billingAddress?.state)?.tax_info?.child_taxes;
        });
      }
      setState({
        data: copyData,
        productSkuIds: copyData?.map((productSkuId) => productSkuId?.product_sku_id),
      });
    }
  }, true);
};

export const handleProductChangeValueHelper = (state, props, value, key, setState) => {
  const { data } = state;
  const copyData = JSON.parse(JSON.stringify(data));
  for (let i = 0; i < copyData?.length; i++) {
    if (copyData[i].key === key) {
      const copyDataItem = copyData[i];
      copyDataItem.product_sku_name = value;
      copyData[i] = copyDataItem;
    }
  }
  setState({ data: copyData });
};

export const handleMultiProductChangeHelper = (state, props, tenantSku, key, productData, setState) => {
  const {
    data, tenantDepartmentId, selectedCurrentInvoice, isLineWiseDiscount, discountPercentage, pricesToApply, selectedPriceList, selectedCustomer, shippingAddress, billingAddress, cfInvoiceLine, calculateRateFromBatches, selectedTenant, discountType,
  } = state;
  const {
    getProductById, user, selectedOrderForInvoice, tenantId, getApplyPriceLists,
  } = props;
  const autoPrintDescription = user?.tenant_info?.global_config?.settings?.print_description_automatically;

  const productSkuIds = tenantSku?.map((item) => item?.product_info?.product_sku_id)?.join(',');

  getProductById(
    selectedTenant || selectedOrderForInvoice?.tenant_id || selectedCurrentInvoice?.tenant_id || tenantId || user?.tenant_info?.tenant_id,
    '',
    tenantDepartmentId,
    (products) => {
      if (selectedPriceList) {
        const newProductSkuIds = products?.map((item) => item?.product_info?.product_sku_id)?.join(',');
        getApplyPriceLists(
          selectedTenant || selectedOrderForInvoice?.tenant_id || selectedCurrentInvoice?.tenant_id || tenantId || user?.tenant_info?.tenant_id,
          selectedPriceList,
          selectedCustomer,
          shippingAddress?.state,
          newProductSkuIds,
          (toApplyPriceList) => {
            const copyData = [];
            const copyToApplyPriceList = [];
            products.forEach((product) => {
              if (product?.product_type === 'BUNDLE' && product?.bundle_products?.length === 0) {
                notification.open({
                  message: `${product?.internal_sku_code} Product is a bundle but it does not have any child components.`,
                  duration: 4,
                  type: 'error',
                  placement: 'top',
                });
              } else {
                const existingSameProduct = data?.find((item) => item?.product_sku_id === product?.product_sku_id);

                const tenantSkuData = tenantSku?.find((item) => item?.product_info?.product_sku_id === product?.product_info?.product_sku_id);

                const productSkuData = productData?.find((item) => item?.product_sku_id === product?.product_info?.product_sku_id);

                const currentToApplyPriceList = toApplyPriceList?.find((item) => item?.product_sku_id === product?.product_info?.product_sku_id);

                const newDataLine = {
                  key: uuidv4(),
                  lineCustomFields: cfInvoiceLine,
                };

                const copyDataItem = createDefaultRow({
                  dataItem: newDataLine,
                  product,
                  tenantSku: tenantSkuData,
                  productData: productSkuData,
                  existingSameProduct,
                  toApplyPriceList: currentToApplyPriceList,
                  calculateRateFromBatches,
                  user,
                  billingAddress,
                  autoPrintDescription,
                  isLineWiseDiscount,
                  discountPercentage,
                  cfInvoiceLine,
                  discountType,
                });

                copyData.push(copyDataItem);
                copyToApplyPriceList.push(currentToApplyPriceList);
              }
            });

            setState({
              data: copyData,
              productSkuIds: copyData?.map((productSkuId) => productSkuId?.product_sku_id),
              pricesToApply: copyToApplyPriceList,
            });
          },
        );
      } else {
        const copyData = [];

        products.forEach((product) => {
          if (product?.product_type === 'BUNDLE' && product?.bundle_products?.length === 0) {
            notification.open({
              message: `${product?.internal_sku_code} Product is a bundle but it does not have any child components.`,
              duration: 4,
              type: 'error',
              placement: 'top',
            });
          } else {
            const existingSameProduct = data?.find((item) => item?.product_sku_id === product?.product_sku_id);

            const tenantSkuData = tenantSku?.find((item) => item?.product_info?.product_sku_id === product?.product_info?.product_sku_id);

            const productSkuData = productData?.find((item) => item?.product_sku_id === product?.product_info?.product_sku_id);

            const newDataLine = {
              key: uuidv4(),
              lineCustomFields: cfInvoiceLine,
            };

            const copyDataItem = createDefaultRow({
              dataItem: newDataLine,
              product,
              tenantSku: tenantSkuData,
              productData: productSkuData,
              existingSameProduct,
              calculateRateFromBatches,
              user,
              billingAddress,
              autoPrintDescription,
              isLineWiseDiscount,
              discountPercentage,
              cfInvoiceLine,
              discountType,
            });

            copyData.push(copyDataItem);
          }
        });

        setState({
          data: copyData,
          productSkuIds: copyData?.map((productSkuId) => productSkuId?.product_sku_id),
        });
      }
    },
    true,
    productSkuIds,
  );
};

export const isDataValid = (state, props) => {
  const {
    data, allStock, selectedCustomer, shippingAddress, selectedPriceList, shipFrom, billFrom, docSeqId, initialInvNumber,
    billingAddress, invoiceDate, dontDeductStock, salesAccount, cfInvoiceDoc, invoiceNumber, selectedOrderForInvoice, checkedRecipients, toRecipients, cfInvoiceLine, chargeData, moInfo, selectedWorkOrder,
  } = state;
  const { user, moRmLines, jwRmLines, selectedInvoice } = props;

  if (moRmLines || jwRmLines) {
    for (let i = 0; i < data?.length; i++) {
      if (Number(data[i].quantity) <= 0 || !CustomFieldHelpers.isCfValid(data[i]?.lineCustomFields)) {
        return false;
      }
    }
    return true;
  }
  const isPriceListMandatory = user?.tenant_info?.sales_config?.sub_modules?.invoice?.settings?.is_price_list_mandatory;

  let isDataValid = true;
  if (!selectedCustomer || !shippingAddress || !billingAddress || !invoiceDate || (isPriceListMandatory && !selectedPriceList)) {
    isDataValid = false;
  }

  // Tally data validation : When sales account selection is on DOC level then only add this validation
  const isTallyConnected = user?.tenant_info?.tally_configuration?.sales_voucher_integration;
  if (user?.tenant_info?.tally_configuration?.sales_account_selection === 'DOC_LEVEL' && isTallyConnected) {
    if (!salesAccount) {
      isDataValid = false;
    }
  }

  for (let i = 0; i < data?.length; i++) {
    if (Number(data[i].quantity) <= 0 || !data[i].product_sku_name || Number(data[i]?.unitPrice) < 0 || !JSON.stringify(data[i].taxId) || !CustomFieldHelpers.isCfValid(data[i]?.lineCustomFields.filter((field) => field?.fieldName !== 'Rate'))) {
      isDataValid = false;
    }

    // Tally data validation : When sales account selection is on line level then only add this validation
    if (['LINE_LEVEL'].includes(user?.tenant_info?.tally_configuration?.sales_account_selection) && isTallyConnected) {
      if (!data[i].sales_account_name) {
        isDataValid = false;
      }
    }

    if (!['SERVICE', 'BUNDLE', 'NON_STORABLE'].includes(data[i]?.product_sku_info?.product_type)) {
      let availableQty = Number(Helpers.getValueTotalInObject(data[i]?.product_batches?.filter((item) => (item?.batch_in_use && (allStock ? !item?.is_rejected_batch : true))), 'available_qty') || 0);
      // availableQty = QUANTITY((availableQty / data[i]?.uom_info?.ratio) * data[i]?.product_sku_info?.uom_info?.ratio, data[i]?.uom_info?.precision);
      availableQty = FormHelpers.uomConversion(data[i]?.product_sku_info?.uom_info, data[i]?.uom_info, availableQty, true);
      if (!dontDeductStock && Number(availableQty - (Number(data[i]?.quantity || 0) + Number(data[i]?.free_quantity || '0'))) < 0) {
        isDataValid = false;
      }
      const DELTA = 0.005;
      const lineConsumedQty = Number(Helpers.getValueTotalInObject(data[i]?.product_batches?.filter((item) => (item?.batch_in_use && (allStock ? !item?.is_rejected_batch : true))), 'consumed_qty') || 0);
      const totalQuantity = (Number(data[i]?.quantity || 0) + Number(data[i]?.free_quantity || 0));
      const quantityFormRange = (totalQuantity - totalQuantity * (DELTA / 100));
      const quantityToRange = (totalQuantity + totalQuantity * (DELTA / 100));
      if (!dontDeductStock && !(lineConsumedQty >= quantityFormRange && lineConsumedQty <= quantityToRange)) {
        isDataValid = false;
        notification.error({
          message: `Quantity Missmatch for ${data[i]?.product_sku_info?.internal_sku_code} line quantity = ${data[i]?.quantity} and batch consumed quantity = ${lineConsumedQty}`,
          placement: 'top',
          duration: 4,
        });
      }
    } else if (data[i]?.product_sku_info?.product_type === 'BUNDLE') {
      const arr = [];
      for (let j = 0; j < data[i]?.bundle_products?.length; j++) {
        const bundleProductBatches = data[i]?.bundle_products?.[j]?.product_batches;
        const qty = Helpers.getValueTotalInObject(bundleProductBatches?.filter((item) => item?.batch_in_use), 'available_qty');
        arr.push(qty / (data[i]?.bundle_products[j]?.quantity / data[i]?.quantity));
      }

      if ((data[i]?.product_sku_info?.product_type === 'STORABLE') && (Math.min(...arr) - Number(data[i]?.quantity || 0)) < 0) {
        isDataValid = false;
      }
    }
  }

  const { docLevelError, lineLevelError } = InvoiceErrorList({
    cfInvoiceDoc, invoiceNumber, invoiceDate, selectedOrderForInvoice, selectedCustomer, billingAddress, shipFrom, billFrom, shippingAddress, checkedRecipients, toRecipients, data, cfInvoiceLine, chargeData, moInfo, selectedWorkOrder, selectedPriceList, isPriceListMandatory, jwRmLines, user, selectedInvoice
  });
  if (lineLevelError?.length || docLevelError?.length) {
    return false;
  }
  return isDataValid;
};

export const isDataValid2 = (state) => {
  const { chargeData } = state;
  let isDataValid = true;
  if (chargeData?.length) {
    for (let i = 0; i < chargeData?.length; i++) {
      if (!chargeData[i].charge_name || !chargeData[i].charge_amount) {
        isDataValid = false;
      }
    }
  }
  return isDataValid;
};

export const getInvoiceStatus = (state, props, withApproval) => {
  const { orgStatusInvoice } = props;
  const status = {};
  if (withApproval) {
    const requiredStatus = orgStatusInvoice?.statuses?.filter((invoiceStatus) => invoiceStatus?.type === 'CONFIRMED' && invoiceStatus?.is_default === true)?.[0];
    status.custom_status_id = Number(requiredStatus?.custom_status_id);
    status.secondary_status = requiredStatus?.status_name;
  } else {
    const requiredStatus = orgStatusInvoice?.statuses?.filter((invoiceStatus) => invoiceStatus?.type === 'DRAFT' && invoiceStatus?.is_default === true)?.[0];
    status.custom_status_id = Number(requiredStatus?.custom_status_id);
    status.secondary_status = requiredStatus?.status_name;
  }
  return status;
};

export const createInvoiceHelper = (state, props, withApproval, setState) => {
  setState({ withApproval });
  const {
    createInvoice, history, user, updateInvoice, selectedInvoice, match, selectedOrderForInvoice,
    callback, getInvoiceByIdSuccess, moRmLines, jwRmLines, moInfo, moCallback, tenantId, createTag, isSalesEstimate, rmTenantDepartmentId,
  } = props;
  const {
    data, terms, shippingAddress, paymentTerms, termsAndConditions, selectedCustomer, billingAddress, paymentRemark, shipFrom, billFrom,
    invoiceDate, chargeData, discountPercentage, isLineWiseDiscount, taxTypeInfo, taxType, taxTypeName, selectedTenant,tncId,
    selectedCustomerInfo, gstNumber, fileList, cfInvoiceDoc, tenantDepartmentId, selectedCurrentInvoice,
    salesAccount, selectedPriceList, selectedCurrencyName, sendWhatsappNotification, accountManager, charge1Name, currentSelectedTenantInfo,
    charge1Value, invoiceNumber, customVoucherNumber, dontDeductStock, freightTaxInfo, transporterId, vehicleNumber, docSeqId, initialInvNumber,
    transporterBillNumber, freightTaxId, freightSacCode, selectedTags, toRecipients, checkedRecipients, selectedWorkOrder, sellerId, createDCAutomatically, isAutomaticConversionRate, currencyConversionRate, updateDocumentReason, narration, discountType,
  } = state;
  const invoiceLines = [];
  const getInvoiceLineBatches = (batches, manufacturingDateFormat, expiryDateFormat) => {
    const invoiceBatches = [];
    for (let j = 0; j < batches?.length; j++) {
      if (batches[j]?.batch_in_use && batches[j]?.consumed_qty > 0) {
        invoiceBatches.push({
          batch_id: batches[j]?.batch_id,
          quantity: batches[j]?.consumed_qty,
          inventory_location_id: batches[j]?.inventory_location_id,
          is_rejected_batch: batches[j]?.is_rejected_batch,
          manufacturing_date: batches?.[j]?.manufacturing_date ? FormHelpers.dateFormatter(batches?.[j]?.manufacturing_date, manufacturingDateFormat) : null,
          expiry_date: batches?.[j]?.expiry_date ? FormHelpers.dateFormatter(batches?.[j]?.expiry_date, expiryDateFormat) : null,
        });
      }
    }
    return invoiceBatches;
  };
  setState({ formSubmitted: true });
  if (isDataValid(state, props) && isDataValid2(state) && ((moRmLines || jwRmLines) ? true : CustomFieldHelpers.isCfValid(cfInvoiceDoc)) && (checkedRecipients ? toRecipients?.length : true) && FormHelpers.isValidBatchConsumption(data, 'product_batches', 'consumed_qty', 'available_qty')) {
    if (match?.params?.invoiceId) {
      for (let i = 0; i < data?.length; i++) {
        const lineDiscount = data?.[i]?.lineDiscountType === 'Percent' ? Number(data?.[i]?.discount || 0) : ((Number(data?.[i]?.discount || 0) / (Number(data?.[i]?.quantity) * parseFloat(data?.[i]?.unitPrice))) * 100);

        const invoiceLine = {
          delivery_challan_line_id: data[i].delivery_challan_line_id,
          invoice_line_id: data[i].invoice_line_id,
          mo_line_id: data[i]?.mo_line_id,
          mo_fg_id: data[i]?.mo_fg_id,
          order_line_id: data[i].order_line_id,
          tenant_product_id: data[i].tenant_product_id,
          line_discount_amount: data[i]?.lineDiscountType === 'Percent' ? (data[i].discount ? data[i].quantity * data[i].unitPrice * (data[i].discount / 100) : 0) : data?.[i]?.discount,
          offer_price: parseFloat(data[i].unitPrice) || 0,
          unit_price: parseFloat(data[i].unitPrice) || 0,
          quantity: Number(data[i].quantity),
          free_quantity: Number(data[i].free_quantity) || 0,
          tally_sales_account_name: data[i]?.sales_account_name,
          product_sku_name: data[i].product_sku_name,
          uom_id: data[i]?.uomId,
          tax_id: data[i]?.taxId,
          tax_group_info: data[i]?.taxInfo,
          uom_info: [data[i]?.uom_info],
          line_discount_percentage: lineDiscount,
          is_discount_in_percent: data[i]?.lineDiscountType === 'Percent',
          remarks: data[i]?.remarks,
          hsn_code: data[i]?.hsn_code?.toString(),
          product_batches: data[i]?.product_sku_info?.product_type === 'STORABLE' ? getInvoiceLineBatches(data?.[i]?.product_batches, data?.[i]?.manufacturingDateFormat, data?.[i]?.expiryDateFormat) : [],
          default_outwards_method: data[i]?.batchConsumptionMethod,
          custom_fields: CustomFieldHelpers.postCfStructure(data[i]?.lineCustomFields),
          bundle_products: data[i]?.bundle_products?.map((item) => ({
            tenant_product_id: item.tenant_product_id,
            order_line_id: item.order_line_id,
            offer_price: parseFloat(item.unitPrice) || 0,
            unit_price: parseFloat(item.unitPrice) || 0,
            tax_id: item?.taxInfo?.tax_id,
            tax_group_info: item?.taxInfo,
            uom_info: [item?.uom_info],
            quantity: Helpers.getValueTotalInObject(item?.product_batches, 'consumed_qty'),
            product_sku_name: item.product_sku_name,
            uom_id: item?.uomId,
            line_discount_percentage: data[i]?.lineDiscountType === 'Percent' ? Number(item?.discount || 0) : (Number(item?.discount || 0) * 100 / (Number(parseFloat(item?.unitPrice)) * Helpers.getValueTotalInObject(item?.product_batches, 'consumed_qty'))),
            product_batches: getInvoiceLineBatches(item?.product_batches, data?.[i]?.manufacturingDateFormat, data?.[i]?.expiryDateFormat),
            default_outwards_method: item?.batchConsumptionMethod,
          })),
        };
        invoiceLines.push(invoiceLine);
      }
      const tenantInfo = selectedInvoice ? selectedInvoice?.tenant_info : (selectedOrderForInvoice ? selectedOrderForInvoice.tenant_info : user?.tenant_info);
      delete tenantInfo?.tally_configuration;
      const payload = [{
        invoice_type: selectedInvoice?.invoice_type,
        invoice_id: selectedInvoice?.invoice_id,
        customer_info: { ...selectedCustomerInfo, gst_number: gstNumber },
        customer_id: selectedCustomer,
        account_manager_id: accountManager || null,
        tenant_id: selectedCurrentInvoice?.tenant_id || tenantId || user?.tenant_info?.tenant_id,
        tenant_info: {
          ...tenantInfo,
        },
        invoice_date: dayjs(invoiceDate).format('YYYY/MM/DD'),
        invoice_due_date: dayjs(invoiceDate).add(Number(paymentTerms || 0), 'day').format('YYYY/MM/DD'),
        invoice_number: initialInvNumber?.toLowerCase()?.trim() === invoiceNumber?.toLowerCase()?.trim() ? null : invoiceNumber,
        payment_terms: [{
          advance_amount: 0,
          due_days: paymentTerms,
          remark: paymentRemark,
        }],
        notification_recipients: toRecipients,
        is_automatic_notification_enabled: checkedRecipients,
        price_list_id: selectedPriceList || null,
        shipping_address: shippingAddress?.address_id,
        shipping_address_info: shippingAddress,
        billing_address: billingAddress?.address_id,
        billing_address_info: billingAddress,
        invoice_lines: invoiceLines,
        tally_sales_account_name: salesAccount,
        t_and_c: global.isBlankString(terms) ? '' : terms,
        attachments: fileList?.map((attachment) => ({
          url: attachment?.response?.response?.location || attachment?.url,
          type: attachment.type,
          name: attachment.name,
          uid: attachment.uid,
        })) || [],
        terms_and_conditions: global.isBlankString(termsAndConditions) ? '' : termsAndConditions,
        tc_id: tncId || null,
        status: withApproval ? 'CONFIRMED' : 'DRAFT',
        custom_fields: CustomFieldHelpers.postCfStructure(cfInvoiceDoc),
        tenant_department_id: tenantDepartmentId,
        other_charges: chargeData?.map((charge) => ({
          charge_name: charge?.charge_name,
          charge_amount: charge?.charge_amount,
          charge_type: '',
          charge_sac_code: charge?.chargesSacCode || null,
          tax_info: charge?.chargesTaxInfo || null,
          ledger_name: charge?.tallyLedgerName || null,
        })) || [],
        discount_percentage: discountType === 'Percent' ? discountPercentage : (discountPercentage / getLineTotals(state?.data, state?.chargeData, state?.taxTypeInfo, state?.taxTypeName, state?.charge1Value, state?.freightTax, state?.freightTaxData).totalAmount || 0) * 100,
        is_line_wise_discount: isLineWiseDiscount,
        is_discount_in_percent: discountType === 'Percent',
        secondary_status: getInvoiceStatus(state, props, withApproval)?.secondary_status,
        custom_status_id: getInvoiceStatus(state, props, withApproval)?.custom_status_id,
        tcs_id: (taxTypeInfo && taxTypeName === 'TCS') ? taxType : null,
        tcs_info: (taxTypeInfo && taxTypeName === 'TCS') ? {
          tax_id: taxTypeInfo?.tax_id,
          tax_name: taxTypeInfo?.tax_name,
          tax_value: taxTypeInfo?.tax_value,
          tax_type_name: taxTypeInfo?.tax_type_name,
        } : null,
        tds_id: (taxTypeInfo && taxTypeName === 'TDS') ? taxType : null,
        tds_info: (taxTypeInfo && taxTypeName === 'TDS') ? {
          tax_id: taxTypeInfo?.tax_id,
          tax_name: taxTypeInfo?.tax_name,
          tax_value: taxTypeInfo?.tax_value,
          tax_type_name: taxTypeInfo?.tax_type_name,
        } : null,
        conversion_rate: currencyConversionRate || selectedCurrencyName?.conversion_rate,
        org_currency_id: selectedCurrencyName?.org_currency_id,
        is_so_automatic_notification_enabled: sendWhatsappNotification,
        bypass_inventory_change: dontDeductStock,
        charge_1_name: charge1Name,
        charge_1_value: Number(charge1Value),
        transporter_id: transporterId,
        vehicle_number: vehicleNumber,
        freight_tax_id: freightTaxId === 'Not Applicable' ? null : freightTaxId,
        freight_tax_info: freightTaxInfo,
        freight_sac_code: freightSacCode,
        transporter_bill_number: transporterBillNumber,
        update_document_reason: updateDocumentReason,
        narration: narration || '',
        ship_from_address_info: shipFrom,
        bill_from_address_info: billFrom,
        ship_from_address_id: shipFrom?.address_id,
        bill_from_address_id: billFrom?.address_id,
      }];

      updateInvoice(payload, () => {
        setState({ currentAction: '' });
        getInvoiceByIdSuccess(null);

        if (selectedTags?.length) {
          const tagPayload = {
            entity_name: 'INVOICE',
            entity_id: selectedInvoice?.invoice_id,
            action: 'REPLACE',
            tag: selectedTags.join(','),
          };
          createTag(tagPayload, () => {
            if (callback) {
              callback();
            } else {
              history.push(`/sales/invoice/view/${selectedInvoice?.invoice_id}`);
            }
          });
        } else if (callback) {
          callback();
        } else {
          history.push(`/sales/invoice/view/${selectedInvoice?.invoice_id}`);
        }
      });
    } else {
      for (let i = 0; i < data?.length; i++) {
        const lineDiscount = data?.[i]?.lineDiscountType === 'Percent' ? Number(data?.[i]?.discount || 0) : ((Number(data?.[i]?.discount || 0) / (Number(data?.[i]?.quantity) * parseFloat(data?.[i]?.unitPrice))) * 100);

        const invoiceLine = {
          delivery_challan_line_id: data[i].delivery_challan_line_id,
          tenant_product_id: data?.[i]?.tenant_product_id,
          order_line_id: data?.[i]?.order_line_id,
          offer_price: parseFloat(data[i].unitPrice) || 0,
          unit_price: parseFloat(data[i].unitPrice) || 0,
          tally_sales_account_name: data[i]?.sales_account_name,
          tax_id: data[i]?.taxId,
          tax_group_info: data[i]?.taxInfo,
          uom_info: [data[i]?.uom_info],
          quantity: Number(data[i].quantity),
          free_quantity: Number(data[i].free_quantity) || 0,
          product_sku_name: data[i].product_sku_name,
          uom_id: data[i]?.uomId,
          remarks: data[i]?.remarks,
          hsn_code: data[i]?.hsn_code?.toString(),
          mo_line_id: data[i]?.mo_line_id,
          mo_fg_id: data[i]?.mo_fg_id,
          line_discount_percentage: lineDiscount,
          is_discount_in_percent: data[i]?.lineDiscountType === 'Percent',
          product_batches: dontDeductStock ? [] : data[i]?.product_sku_info?.product_type === 'STORABLE' ? getInvoiceLineBatches(data?.[i]?.product_batches, data?.[i]?.manufacturingDateFormat, data?.[i]?.expiryDateFormat) : [],
          fg_wise_breakup: moRmLines ? data[i]?.fg_wise_breakup?.map((item) => ({ mo_line_id: item?.mo_line_id, fg_tenant_product_id: item?.fg_tenant_product_id, quantity: item?.remaining_quantity || 0 })) : null,
          jw_wise_breakup: jwRmLines ? data[i]?.jw_wise_breakup?.map((item) => ({
            pr_line_input_material_id: item?.pr_line_input_material_id,
            pr_line_processing_material_id: item?.pr_line_processing_material_id,
            item_mo_line_id: item?.item_mo_line_id,
            item_mo_fg_id: item?.item_mo_fg_id,
            production_route_line_id: item?.production_route_line_id,
            quantity: item?.remaining_quantity,
          })) : null,
          default_outwards_method: data[i]?.batchConsumptionMethod,
          custom_fields: CustomFieldHelpers.postCfStructure(data[i]?.lineCustomFields),
          bundle_products: data[i]?.bundle_products?.map((item) => ({
            tenant_product_id: item?.tenant_product_id,
            order_line_id: item?.order_line_id,
            delivery_challan_line_id: item?.delivery_challan_line_id,
            offer_price: Number(item?.unitPrice || 0),
            unit_price: Number(item?.unitPrice || 0),
            tax_id: item?.taxInfo?.tax_id,
            tax_group_info: item?.taxInfo,
            uom_info: [item?.uom_info],
            // quantity: Number(item?.bundle_quantity),
            quantity: Number(data[i].quantity * item?.bundle_quantity),
            product_sku_name: item?.product_sku_name,
            uom_id: item?.uomId,
            line_discount_percentage: data[i]?.lineDiscountType === 'Percent' ? Number(item?.discount || 0) : (Number(item?.discount || 0) * 100 / (Number(item?.unitPrice || 0) * Number(data?.[i]?.quantity * item?.bundle_quantity))),
            product_batches: dontDeductStock ? [] : getInvoiceLineBatches(item?.product_batches, data?.[i]?.manufacturingDateFormat, data?.[i]?.expiryDateFormat),
            default_outwards_method: item?.batchConsumptionMethod,
          })),
        };
        invoiceLines.push(invoiceLine);
      }
      let tenantInfo = null;

      if (moInfo && moInfo.tenantInfo) {
        tenantInfo = moInfo.tenantInfo;
      } else if (selectedOrderForInvoice && selectedOrderForInvoice.tenant_info) {
        tenantInfo = selectedOrderForInvoice.tenant_info;
      } else if (selectedInvoice && selectedInvoice.tenant_info) {
        tenantInfo = currentSelectedTenantInfo || selectedInvoice.tenant_info;
      } else if (currentSelectedTenantInfo) {
        tenantInfo = currentSelectedTenantInfo;
      } else if (user && user.tenant_info) {
        tenantInfo = user.tenant_info;
      }

      // Safely delete the tally_configuration key from tenantInfo if it exists
      if (tenantInfo && tenantInfo.tally_configuration) {
        delete tenantInfo.tally_configuration;
      }

      const payload = [{
        seller_id: (moInfo || jwRmLines) ? sellerId : null,
        subcontractor_mo_id: moInfo ? moInfo?.mo_id : (jwRmLines ? jwRmLines?.mo_id : null),
        subcontractor_po_id: moInfo ? selectedWorkOrder : null,
        customer_id: !moRmLines ? selectedCustomer : null,
        customer_info: (!moRmLines && !jwRmLines) ? { ...selectedCustomerInfo, gst_number: gstNumber } : null,
        account_manager_id: accountManager || null,
        tenant_id: moInfo ? moInfo?.moTenantId : (selectedTenant || selectedOrderForInvoice?.tenant_id || selectedInvoice?.tenant_id || tenantId),
        tenant_info: {
          ...tenantInfo,
        },
        invoice_date: dayjs(moInfo ? new Date() : invoiceDate).format('YYYY/MM/DD'),
        invoice_due_date: dayjs(moInfo ? new Date() : invoiceDate).add(moInfo ? Number(moInfo?.seller?.default_payment_terms?.due_days || 0) : Number(paymentTerms || 0), 'day').format('YYYY/MM/DD'),
        invoice_number: initialInvNumber?.toLowerCase()?.trim() === invoiceNumber?.toLowerCase()?.trim() ? null : invoiceNumber,
        seq_id: docSeqId || null,
        payment_terms: moInfo ? [moInfo?.seller?.default_payment_terms || {
          advance_amount: 0,
          due_days: 0,
        }] : [{
          advance_amount: 0,
          due_days: paymentTerms,
          remark: paymentRemark,
        }],
        notification_recipients: toRecipients,
        is_automatic_notification_enabled: checkedRecipients,
        create_dc_automatically: createDCAutomatically,
        price_list_id: selectedPriceList || null,
        shipping_address: moInfo
          ? moInfo?.vendorAddress?.address_id
          : selectedOrderForInvoice
            ? selectedOrderForInvoice?.sales_order_info
              ? selectedOrderForInvoice?.sales_order_info?.shipping_address
              : selectedOrderForInvoice?.shipping_address || shippingAddress?.address_id
            : shippingAddress?.address_id,
        shipping_address_info: moInfo ? moInfo?.vendorAddress : shippingAddress,
        billing_address: moInfo ? moInfo?.vendorAddress?.address_id : selectedOrderForInvoice ? selectedOrderForInvoice?.sales_order_info ? selectedOrderForInvoice?.sales_order_info?.billing_address : selectedOrderForInvoice?.billing_address ? selectedOrderForInvoice?.billing_address : billingAddress?.address_id : billingAddress?.address_id,
        billing_address_info: moInfo ? moInfo?.vendorAddress : billingAddress,
        invoice_lines: invoiceLines,
        tally_sales_account_name: salesAccount,
        t_and_c: global.isBlankString(terms) ? '' : terms,
        attachments: fileList?.map((attachment) => ({
          url: attachment?.response?.response?.location || attachment?.url,
          type: attachment.type,
          name: attachment.name,
          uid: attachment.uid,
        })) || [],
        status: withApproval ? 'CONFIRMED' : 'DRAFT',
        terms_and_conditions: global.isBlankString(termsAndConditions) ? '' : termsAndConditions,
        tc_id: tncId || null,
        order_id: selectedOrderForInvoice?.order_id,
        mo_id: moInfo?.fgLines[0]?.mo_id,
        delivery_challan_id: selectedOrderForInvoice?.delivery_challan_id,
        source: selectedOrderForInvoice?.delivery_challan_id ? 'DELIVERY_CHALLAN' : selectedOrderForInvoice?.order_id ? 'SALES_ORDER' : (moInfo?.fgLines[0]?.mo_id || jwRmLines) ? 'MANUFACTURING_ORDER' : 'ADHOC',
        custom_fields: CustomFieldHelpers.postCfStructure(cfInvoiceDoc),
        tenant_department_id: moRmLines ? rmTenantDepartmentId : tenantDepartmentId,
        other_charges: chargeData?.map((charge) => ({
          charge_name: charge?.charge_name,
          charge_amount: charge?.charge_amount,
          charge_sac_code: charge?.chargesSacCode || null,
          tax_info: charge?.chargesTaxInfo || null,
          ledger_name: charge?.tallyLedgerName || null,
          charge_type: '',
        })) || [],
        discount_percentage: discountType === 'Percent' ? discountPercentage : (discountPercentage / getLineTotals(state?.data, state?.chargeData, state?.taxTypeInfo, state?.taxTypeName, state?.charge1Value, state?.freightTax, state?.freightTaxData).totalAmount || 0) * 100,
        is_line_wise_discount: isLineWiseDiscount,
        is_discount_in_percent: discountType === 'Percent',
        secondary_status: getInvoiceStatus(state, props, withApproval)?.secondary_status,
        custom_status_id: getInvoiceStatus(state, props, withApproval)?.custom_status_id,
        tcs_id: (taxTypeInfo && taxTypeName === 'TCS') ? taxType : null,
        tcs_info: (taxTypeInfo && taxTypeName === 'TCS') ? {
          tax_id: taxTypeInfo?.tax_id,
          tax_name: taxTypeInfo?.tax_name,
          tax_value: taxTypeInfo?.tax_value,
          tax_type_name: taxTypeInfo?.tax_type_name,
        } : null,
        tds_id: (taxTypeInfo && taxTypeName === 'TDS') ? taxType : null,
        tds_info: (taxTypeInfo && taxTypeName === 'TDS') ? {
          tax_id: taxTypeInfo?.tax_id,
          tax_name: taxTypeInfo?.tax_name,
          tax_value: taxTypeInfo?.tax_value,
          tax_type_name: taxTypeInfo?.tax_type_name,
        } : null,
        conversion_rate: currencyConversionRate || selectedCurrencyName?.conversion_rate,
        org_currency_id: selectedCurrencyName?.org_currency_id,
        is_so_automatic_notification_enabled: sendWhatsappNotification,
        bypass_inventory_change: dontDeductStock,
        charge_1_name: charge1Name,
        charge_1_value: Number(charge1Value),
        transporter_id: transporterId,
        vehicle_number: vehicleNumber,
        freight_tax_id: freightTaxId === 'Not Applicable' ? null : freightTaxId,
        freight_tax_info: freightTaxInfo,
        transporter_bill_number: transporterBillNumber,
        freight_sac_code: freightSacCode,
        sales_estimate_id: isSalesEstimate ? selectedOrderForInvoice?.order_id : null,
        narration: narration || '',
        ship_from_address_info: shipFrom,
        bill_from_address_info: billFrom,
        ship_from_address_id: shipFrom?.address_id,
        bill_from_address_id: billFrom?.address_id,
      }];
      createInvoice(payload, (invoice) => {
        setState({ currentAction: '' });
        if (selectedTags?.length) {
          const tagPayload = {
            entity_name: 'INVOICE',
            entity_id: invoice?.invoice_id,
            action: 'ADD',
            tag: selectedTags.join(','),
          };
          createTag(tagPayload, () => {
            if (moCallback) {
              moCallback();
            } else if (callback) {
              callback();
            } else {
              history.push(`/sales/invoice/view/${invoice?.invoice_id}`);
            }
          });
        } else if (moCallback) {
          moCallback();
        } else if (callback) {
          callback();
        } else {
          history.push(`/sales/invoice/view/${invoice?.invoice_id}`);
        }
      });
    }
  } else {
    notification.open({
      type: 'error',
      duration: '5',
      message: 'Please provide input for all mandatory fields',
      placement: 'top',
    });
  }
};

export const getDataSource = (data) => {
  const newData = JSON.parse(JSON.stringify(data));
  return [...newData.filter((item) => item?.product_sku_id), ...newData.filter((item) => !item?.product_sku_id)];
};

export const addNewRowHelper = (state, props, callback, setState) => {
  const { data, cfInvoiceLine } = state;
  const copyData = JSON.parse(JSON.stringify(data));
  const key = uuidv4();
  copyData.push({
    key,
    asset1: '',
    product_sku_name: '',
    quantity: '',
    unitPrice: '',
    taxId: '',
    lot: '',
    child_taxes: [{
      tax_amount: 0,
      tax_type_name: '',
    }],
    lineCustomFields: cfInvoiceLine,
  });
  setState({ data: copyData }, () => {
    if (callback) {
      callback(key);
    }
  });
};

export const addNewChargeRowHelper = (state, props, setState) => {
  const { chargeData } = state;
  const copychargeData = JSON.parse(JSON.stringify(chargeData));
  copychargeData.push({
    chargeKey: uuidv4(),
    charge_name: '',
    charge_amount: 0,
    chargesTaxData: {
      child_taxes: [
        {
          tax_amount: 0,
          tax_type_name: '',
        },
      ],
    },
  });
  setState({ chargeData: copychargeData });
};

export const toggleBatchHelper = (state, props, record, adjustmentRow, setState) => {
  const { data } = state;
  for (let i = 0; i < data?.length; i++) {
    if (data[i]?.key === adjustmentRow?.key) {
      const rowBatches = data[i]?.product_batches;
      for (let j = 0; j < rowBatches?.length; j++) {
        if (rowBatches[j]?.batch_id === record?.batch_id && rowBatches[j]?.is_reserved === record?.is_reserved) {
          rowBatches[j].batch_in_use = !rowBatches[j]?.batch_in_use;
          // data[i].quantity -= rowBatches[j].consumed_qty;
          // rowBatches[j].consumed_qty = 0;
          const rowQty = new Decimal(rowBatches[j]?.consumed_qty || 0);
          const dataQty = new Decimal(data[i]?.quantity || 0);
          data[i].quantity = dataQty.minus(rowQty).toNumber();
          rowBatches[j].consumed_qty = 0;
          clearLineReservations(data[i]?.key);
        }
      }
      const consumedBatches = rowBatches?.filter((i) => i?.consumed_qty > 0 && i?.batch_in_use)?.map((i) => ({
        sp: i?.selling_price,
        quantity: i?.consumed_qty,
      }));
      const weightedAvgSp = Number(calculateWASP(consumedBatches));
      data[i].unitPrice = weightedAvgSp;
      data[i].offerPrice = weightedAvgSp;
      break;
    }
  }
  setState({ data });
};

export const toggleBatchInnerHelper = (state, props, record, adjustmentRow, setState) => {
  const { data } = state;
  const copyData = JSON.parse(JSON.stringify(data));
  for (let i = 0; i < copyData?.length; i++) {
    const newData = copyData[i];
    if (newData?.key === adjustmentRow?.parentKey) {
      newData.quantity = 0;
      const bundleProducts = newData?.bundle_products;
      for (let j = 0; j < bundleProducts?.length; j++) {
        const rowBatches = bundleProducts[j]?.product_batches;
        for (let k = 0; k < rowBatches?.length; k++) {
          if (rowBatches[k]?.batch_id === record?.batch_id) {
            rowBatches[k].batch_in_use = !rowBatches[k]?.batch_in_use;
          }
          rowBatches[k].consumed_qty = 0;
        }
      }
    }
  }
  setState({ data: copyData });
};

export const handleDeleteHelper = (state, props, key, setState, record) => {
  const {
    data, cfInvoiceLine, isLineWiseDiscount, discountPercentage,
  } = state;
  if (record?.product_type === 'BUNDLE') {
    const bundleProducts = record?.bundle_products;
    for (let index = 0; index < bundleProducts.length; index++) {
      clearLineReservations(bundleProducts[index]?.key);
    }
  } else {
    clearLineReservations(key);
  }
  if (data?.length === 1) {
    setState({
      data: [{
        key: uuidv4(),
        asset1: '',
        product_name: '',
        quantity: '',
        unitPrice: '',
        lot: 0,
        taxId: '',
        discount: 0,
        child_taxes: [{
          tax_amount: 0,
          tax_type_name: '',
        }],
        lineCustomFields: cfInvoiceLine,
      }],
    });
  } else {
    const copyData = data.filter((item) => item.key !== key);

    if (!isLineWiseDiscount) {
      const totalValue = copyData?.reduce((acc, cur) => acc + (cur?.quantity * cur?.unitPrice), 0);

      copyData?.map((item) => {
        const discountValue = state.discountType === 'Percent' ? parseFloat(state.discountPercentage) : ((item?.quantity * item?.unitPrice) / parseFloat(totalValue)) * parseFloat(state.discountPercentage);
        const taxableValue = state.discountType === 'Percent' ? (item?.quantity * item?.unitPrice) * (1 - discountValue / 100) : Math.max(item?.quantity * item?.unitPrice - discountValue, 0);

        item.discount = discountValue;
        item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, props.user?.tenant_info?.state, state.billingAddress?.state)?.tax_info?.child_taxes;
      });
    }
    setState({ data: copyData });
  }
};

export function customInputChangeHelper(state, props, fieldValue, cfId, setState) {
  const { cfInvoiceDoc } = state;
  const newCustomField = cfInvoiceDoc.map((customField) => {
    if (customField?.cfId === cfId) {
      if (customField?.fieldType === 'ATTACHMENT') {
        return {
          ...customField,
          fieldValue: fieldValue?.map((attachment) => ({
            url: attachment?.response?.response?.location || attachment?.url,
            type: attachment.type,
            name: attachment.name,
            uid: attachment.uid,
          })),
        };
      }
      return {
        ...customField,
        fieldValue,
      };
    }
    return {
      ...customField,
    };
  });
  setState({
    cfInvoiceDoc: newCustomField,
  });
}
export function handleQuantityChange(record, value, freeValue, showFreebie, props, state) {
  const {
    updateData, data, billFromState, billToState,
  } = props;
  const { allStock } = state;
  const copyData = JSON.parse(JSON.stringify(data));
  const taxableValue = (value * record.unitPrice) * (record.discount ? Number(100 - record.discount) / 100 : 1);

  if (record?.bundle_products?.length) {
    const bundleProducts = JSON.parse(JSON.stringify(record?.bundle_products));
    for (let i = 0; i < bundleProducts?.length; i++) {
      bundleProducts[i].product_batches = getBatches(bundleProducts[i]?.product_batches, (Number(value) + Number(freeValue)) * bundleProducts[i]?.bundle_quantity, bundleProducts[i]?.uom_info, bundleProducts[i]?.product_sku_info?.uom_info, null, bundleProducts[i]?.key, allStock);
    }
    const updatedData = copyData.map((obj) => {
      if (obj.key === record.key) {
        return {
          ...obj,
          quantity: value || '',
          free_quantity: freeValue || '',
          so_free_quantity: freeValue || '',
          bundle_products: bundleProducts,
          child_taxes: Helpers.computeTaxation(taxableValue, record.taxInfo, billFromState, billToState)?.tax_info?.child_taxes,
        };
      }
      return obj;
    });
    updateData(updatedData);
  } else {
    const updatedData = copyData.map((obj) => {
      if (obj.key === record.key) {
        return {
          ...obj,
          quantity: value,
          free_quantity: freeValue || '',
          so_free_quantity: freeValue || '',
          product_batches: getBatches(record?.product_batches, (Number(value || 0) + Number(freeValue || 0)), record?.uom_info, record?.product_sku_info?.uom_info, null, record?.key, allStock),
          child_taxes: Helpers.computeTaxation(taxableValue, record.taxInfo, billFromState, billToState)?.tax_info?.child_taxes,
          showFreebie,
        };
      }
      return obj;
    });
    updateData(updatedData);
  }
}

function calculateWASP(data) {
  let totalValue = 0;
  let totalQuantity = 0;

  data.forEach((item) => {
    const sp = item.sp || 0; // Treat null as 0
    const { quantity } = item;

    totalValue += sp * quantity;
    totalQuantity += quantity;
  });

  return totalQuantity === 0 ? 0 : totalValue / totalQuantity;
}

// state, props, fieldValue, key, lineProps, setState
export function customLineInputChangeHelper(state, props, fieldValue, key, lineProps, setState, cBatchId, cBatchQty, rate) {
  const {
    data, allStock, isLineWiseDiscount, discountPercentage, discountType, billingAddress, pricesToApply,
  } = state;
  const {
    billFromState, billToState, fgRmQtyMap, jwQtyMap,
  } = lineProps;
  const copyData = JSON.parse(JSON.stringify(data));
  let record;
  for (let i = 0; i < copyData.length; i++) {
    if (copyData[i]?.key === key) {
      copyData[i].lineCustomFields = fieldValue;
      const rateCf = fieldValue?.find((j) => j?.fieldName === 'Rate');
      const qtyCf = fieldValue?.find((j) => j?.fieldName === 'Quantity');
      copyData[i].unitPrice = parseFloat(Number(rateCf?.fieldValue || 0));
      copyData[i].offerPrice = parseFloat(Number(rateCf?.fieldValue || 0));
      copyData[i].quantity = parseFloat(Number(qtyCf?.fieldValue || 0).toFixed(copyData[i].product_sku_info?.uom_info?.precision));
      if (fgRmQtyMap) {
        copyData[i].fg_wise_breakup = copyData[i].fg_wise_breakup?.map((item) => ({ ...item, remaining_quantity: fgRmQtyMap[item?.fg_tenant_product_id]?.usedQty, usedQty: fgRmQtyMap[item?.fg_tenant_product_id]?.usedQty }));
      }
      if (jwQtyMap) {
        copyData[i].jw_wise_breakup = copyData[i].jw_wise_breakup?.map((item) => ({ ...item, remaining_quantity: jwQtyMap[item?.production_route_line_id]?.usedQty, usedQty: jwQtyMap[item?.production_route_line_id]?.usedQty }));
      }
      record = copyData[i];
      let discountValue = Number.parseFloat(copyData[i].discount) || 0;
      if (copyData[i]?.showUnitDiscount) {
        discountValue = Number.parseFloat(Number(Number(copyData[i].unitDiscount) * Number(qtyCf?.fieldValue)).toFixed(DEFAULT_CUR_ROUND_OFF));
      }
      const taxableValue = copyData[i]?.lineDiscountType === 'Percent' ? (record?.quantity * record.unitPrice) * (record.discount ? Number(100 - record.discount) / 100 : 1) : Math.max(qtyCf?.fieldValue * copyData[i].unitPrice - discountValue, 0);
      copyData[i].child_taxes = Helpers.computeTaxation(taxableValue, record.taxInfo, billFromState, billToState)?.tax_info?.child_taxes;
      if (record?.bundle_products?.length) {
        const bundleProducts = JSON.parse(JSON.stringify(record?.bundle_products));
        for (let i = 0; i < bundleProducts?.length; i++) {
          bundleProducts[i].product_batches = getBatches(bundleProducts[i]?.product_batches, (Number(record?.quantity) + Number(record?.free_quantity || 0)) * bundleProducts[i]?.bundle_quantity, bundleProducts[i]?.uom_info, bundleProducts[i]?.product_sku_info?.uom_info, null, bundleProducts[i]?.key, true, allStock);
        }
        copyData[i].bundle_products = bundleProducts;
      } else {
        const pBatches = getBatches(record?.product_batches, (Number(record?.quantity || 0) + Number(record?.free_quantity || 0)), record?.uom_info, record?.product_sku_info?.uom_info, null, record?.key, true, allStock, cBatchId, cBatchQty);
        if (state?.calculateRateFromBatches) {
          const consumedBatches = pBatches?.filter((i) => i?.consumed_qty > 0 && i?.batch_in_use)?.map((i) => ({
            sp: i?.selling_price,
            quantity: i?.consumed_qty,
          }));
          const weightedAvgSp = Number(calculateWASP(consumedBatches));
          copyData[i].unitPrice = weightedAvgSp;
          copyData[i].offerPrice = weightedAvgSp;
        }
        copyData[i].product_batches = pBatches;
      }

      // for applying price list
      for (let j = 0; j < pricesToApply?.length; j++) {
        if (copyData?.[i]?.product_sku_id === pricesToApply?.[j]?.product_sku_id) {
          if (rate !== undefined && rate !== null) {
            copyData[i].unitPrice = rate;
          } else if (Number(copyData?.[i]?.quantity) >= pricesToApply?.[j]?.start_quantity && Number(copyData?.[i]?.quantity) <= pricesToApply?.[j]?.end_quantity) {
            copyData[i].unitPrice = pricesToApply?.[j]?.is_inclusive_of_tax ? pricesToApply?.[j]?.price_list_amount / ((100 + copyData?.[i]?.taxInfo?.tax_value) / 100) : pricesToApply?.[j]?.price_list_amount;
            copyData[i].discount = pricesToApply?.[j]?.discount_percentage;
            break;
          } else if ((pricesToApply?.[j]?.start_quantity === undefined && pricesToApply?.[j]?.end_quantity === undefined) || (pricesToApply?.[j]?.start_quantity === null && pricesToApply?.[j]?.end_quantity === null)) {
            copyData[i].unitPrice = Number(pricesToApply?.[j]?.price_list_amount) ? Number(pricesToApply?.[j]?.price_list_amount) : (copyData?.[i]?.default_selling_price || 0);
            copyData[i].discount = pricesToApply?.[j]?.discount_percentage;
          } else {
            copyData[i].unitPrice = pricesToApply?.[j]?.default_selling_price || 0;
          }
          copyData[i].lineCustomFields = copyData?.[i]?.lineCustomFields?.map((field) => ({
            ...field,
            fieldValue: field?.fieldName === 'Quantity' ? copyData?.[i]?.quantity : field?.fieldName === 'Rate' ? copyData?.[i]?.unitPrice : field?.fieldValue,
          }));
        }
      }
    }
  }

  if (!isLineWiseDiscount) {
    const totalValue = copyData?.reduce((acc, cur) => acc + (cur?.quantity * cur?.unitPrice), 0);

    copyData?.map((item) => {
      const discountValue = state.discountType === 'Percent' ? parseFloat(state.discountPercentage) : ((item?.quantity * item?.unitPrice) / parseFloat(totalValue)) * parseFloat(state.discountPercentage);
      const taxableValue = state.discountType === 'Percent' ? (item?.quantity * item?.unitPrice) * (1 - discountValue / 100) : Math.max(item?.quantity * item?.unitPrice - discountValue, 0);

      item.discount = discountValue;
      item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, props.user?.tenant_info?.state, state.billingAddress?.state)?.tax_info?.child_taxes;
    });
  }
  setState({ data: copyData });
}

export function roundToPrecision(value, precision) {
  const multiplier = Math.pow(10, precision);
  return Math.round(value * multiplier) / multiplier;
}

// To distribute qty to fg on basis of their respective ratio
export function handleQuantityForFgRm(inputValue, lineData, record) {
  const copyData = JSON.parse(JSON.stringify(lineData));
  const newRmQty = Number(inputValue || 0);

  const fgRmQtyMap = {};
  let fgLineWiseBreakUp = [];

  const recordData = copyData?.find((item) => item?.key === record?.key);
  if (recordData?.fgWiseBreakup) {
    fgLineWiseBreakUp = JSON.parse(JSON.stringify(recordData.fgWiseBreakup));
  }

  const totalQty = record?.remaining_quantity;
  let totalUsedQty = 0;
  const fgUsedQty = fgLineWiseBreakUp.map((fg) => {
    const usedQty = (fg.remaining_quantity / totalQty) * newRmQty;
    const roundedUsedQty = roundToPrecision(usedQty, record?.uom_info?.precision);
    totalUsedQty += roundedUsedQty;
    return { fg, roundedUsedQty };
  });

  // Adjust the last quantity to ensure the sum matches newRmQty
  if (fgUsedQty.length > 0) {
    const diff = Number((newRmQty - totalUsedQty).toFixed(record?.uom_info?.precision));
    fgUsedQty[fgUsedQty.length - 1].roundedUsedQty += diff;
  }

  fgUsedQty.forEach(({ fg, roundedUsedQty }) => {
    fg.usedQty = roundedUsedQty;
    fgRmQtyMap[fg.fg_tenant_product_id] = fg;
  });

  return { newRmQty, fgRmQtyMap };
}

export function handleQuantityForJw(inputValue, lineData, record) {
  const copyData = JSON.parse(JSON.stringify(lineData));
  const newJwQty = Number(inputValue || 0);

  const jwQtyMap = {};
  let jwLineWiseBreakUp = [];

  const recordData = copyData?.find((item) => item?.key === record?.key);
  if (recordData?.jwWiseBreakup) {
    jwLineWiseBreakUp = JSON.parse(JSON.stringify(recordData.jwWiseBreakup));
  }

  const totalQty = record?.remaining_quantity;
  let totalUsedQty = 0;
  const jwUsedQty = jwLineWiseBreakUp.map((jw) => {
    const usedQty = (jw.remaining_quantity / totalQty) * newJwQty;
    const roundedUsedQty = roundToPrecision(usedQty, record?.uom_info?.precision);
    totalUsedQty += roundedUsedQty;
    return { jw, roundedUsedQty };
  });

  // Adjust the last quantity to ensure the sum matches newJwQty
  if (jwUsedQty.length > 0) {
    const diff = Number((newJwQty - totalUsedQty).toFixed(record?.uom_info?.precision));
    jwUsedQty[jwUsedQty.length - 1].roundedUsedQty += diff;
  }

  jwUsedQty.forEach(({ jw, roundedUsedQty }) => {
    jw.usedQty = roundedUsedQty;
    jwQtyMap[jw.production_route_line_id] = jw;
  });

  return { newJwQty, jwQtyMap };
}

export function handleFgQty(inputValue, lineData, record, fgLine) {
  const copyData = JSON.parse(JSON.stringify(lineData));
  let totalRmQty;
  copyData.map((item) => {
    if (item.key === record.key) {
      item?.fg_wise_breakup?.map((itemFg) => {
        let fgWiseBreakUp = item?.fg_wise_breakup;
        if (itemFg?.key === fgLine?.key) {
          fgWiseBreakUp = fgWiseBreakUp?.filter((fgI) => fgI?.key !== fgLine?.key);
          totalRmQty = Number(inputValue || 0) + fgWiseBreakUp?.reduce((acc, curr) => acc + Number(curr?.remaining_quantity), 0);
          itemFg.usedQty = inputValue;
        }
        return itemFg;
      });
    }
    return item;
  });
  const fgRmQtyMap = {};
  const fgLineWiseBreakUp = JSON.parse(JSON.stringify(copyData?.find((item) => item?.key === record?.key)?.fg_wise_breakup));
  for (let i = 0; i < fgLineWiseBreakUp?.length; i++) {
    const fg = fgLineWiseBreakUp[i];
    fgRmQtyMap[fg?.fg_tenant_product_id] = fg;
  }

  return { totalRmQty, fgRmQtyMap };
}

export function handleJwQty(inputValue, lineData, record, jwLine) {
  const copyData = JSON.parse(JSON.stringify(lineData));
  let totalJwQty;
  copyData.map((item) => {
    if (item.key === record.key) {
      item?.jw_wise_breakup?.map((itemJw) => {
        let jwWiseBreakUp = item?.jw_wise_breakup;
        if (itemJw?.key === jwLine?.key) {
          jwWiseBreakUp = jwWiseBreakUp?.filter((fgI) => fgI?.key !== jwLine?.key);
          totalJwQty = Number(inputValue || 0) + jwWiseBreakUp?.reduce((acc, curr) => acc + Number(curr?.remaining_quantity), 0);
          itemJw.usedQty = inputValue;
        }
        return itemJw;
      });
    }
    return item;
  });
  const jwQtyMap = {};
  const jwLineWiseBreakUp = JSON.parse(JSON.stringify(copyData?.find((item) => item?.key === record?.key)?.jw_wise_breakup));
  for (let i = 0; i < jwLineWiseBreakUp?.length; i++) {
    const jw = jwLineWiseBreakUp[i];
    jwQtyMap[jw?.production_route_line_id] = jw;
  }

  return { totalJwQty, jwQtyMap };
}
