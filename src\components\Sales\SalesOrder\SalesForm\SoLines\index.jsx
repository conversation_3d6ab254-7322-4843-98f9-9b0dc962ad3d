import React, { Fragment } from 'react';
import { <PERSON>, withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { Select, Table } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import { v4 as uuidv4 } from 'uuid';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTag, faClose } from '@fortawesome/free-solid-svg-icons';

// custom components
import H3FormInput from '@Uilib/h3FormInput';
import H3Text from '@Uilib/h3Text';
import { DEFAULT_CUR_ROUND_OFF } from '@Apis/constants';
import ProductFilterV2 from '@Components/Common/ProductFilterV2';
import SelectTax from '../../../../Admin/Common/SelectTax';
import './style.scss';
import Helpers from '@Apis/helpers';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import QuickAddButton from '@Components/Common/UILib/QuickAddButton';
import ProductDescriptions from '@Components/Common/ProductDescriptions';
import RecentTransactions from '../../../../Inventory/ManageProducts/ViewProduct/RecentTransactions';
import ProductCategoryLabel from '../../../../Common/ProductCategoryLabel';
import HideValue from '../../../../Common/RestrictedAccess/HideValue';
import PRZSelect from '../../../../Common/UI/PRZSelect';

const SoLines = ({
  data, loading, title, pricesToApply, handleProductChangeValue, handleProductChange, formSubmitted, addNewRow, tenantDepartmentId, updateData, isLineWiseDiscount, handleDelete, MONEY, billFromState, billToState, selectedCurrencyName, user, tenantId, selectedCustomer, cfSalesOrderLine, visibleColumns, customLineInputChange, isEstimate, priceMasking, isCarryForward, handleMultiProductChange,
}) => {
  const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;

  const discountOptions = [
    { value: "Amount", label: `${selectedCurrencyName?.currency_symbol} ` },
    { value: "Percent", label: "%" },
  ];

  const getColumns = () => {
    const enableInternalSKUCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_sku_code;
    const enableInternalRefCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_ref_code;

    const productListColumns = [
      {
        title: 'Product',
        fixed: 'left',
        width: '250px',
        render: (text, record) => (
          <div style={{ minWidth: '250px', maxWidth: '250px' }}>
            {record?.product_sku_info
              ? (
                <div className="so-table__product-name">
                  <div className="flex-align-c" style={{ gap: '10px' }}>
                    <div>
                      {enableInternalSKUCode && (
                        <div>
                          <Link to={`/inventory/product/view/${record?.product_sku_info?.product_sku_id}`} target="_blank">
                            {`#${record?.product_sku_info?.internal_sku_code}`}
                          </Link>
                        </div>
                      )}
                      {enableInternalRefCode && record?.product_sku_info?.ref_product_code && (
                        <div>
                          <Link to={`/inventory/product/view/${record?.product_sku_info?.product_sku_id}`} target="_blank">
                            {`#${record?.product_sku_info?.ref_product_code}`}
                          </Link>
                        </div>
                      )}
                      <div>
                        {record?.product_sku_name}
                      </div>
                    </div>
                    <div className="margin-left-auto">
                      {Helpers.getPermission(Helpers.permissionEntities.SALES_ORDER, Helpers.permissionTypes.CREATE, user) && (
                        <RecentTransactions
                          tenantId={tenantId}
                          productSkuId={record?.product_sku_info?.product_sku_id}
                          internalSkuCode={record?.product_sku_info?.internal_sku_code}
                          refProductCode={record?.product_sku_info?.ref_product_code}
                          productSkuName={record?.product_sku_name}
                          costumerID={selectedCustomer}
                          TransactionType="SALES_ORDER"
                        />
                      )}
                    </div>
                  </div>
                  {record?.product_category_info?.category_path?.length > 0 && (
                    <ProductCategoryLabel
                      categoryPath={record?.product_category_info?.category_path}
                      categoryName={record?.product_category_info?.category_path?.at(-1)}
                      containerStyle={{
                        width: 'fit-content',
                      }}
                    />
                  )}
                  {
                    !record?.remarkRequired
                    && (
                      <QuickAddButton
                        label="Add Description"
                        onClick={() => {
                          const copyData = JSON.parse(JSON.stringify(data));
                          const newData = copyData.map((item) => {
                            if (item.key === record.key) {
                              return { ...item, remarkRequired: !record?.remarkRequired };
                            }
                            return item;
                          });
                          if (JSON.stringify(data) !== JSON.stringify(newData)) {
                            updateData(newData);
                          }
                        }}
                      />
                    )
                  }
                  {record?.remarkRequired ? (
                    <ProductDescriptions
                      descriptions={record?.remarks}
                      modules={{
                        toolbar: false,
                      }}
                      onChange={(e) => {
                        const copyData = JSON.parse(JSON.stringify(data));
                        copyData.map((item) => {
                          if (item.key === record.key) {
                            item.remarks = e;
                            item.remarkRequired = !!e?.replace(/<[^>]+>/g, '');
                          }
                          return data;
                        });
                        updateData(copyData);
                      }}
                      className="invoice-table__remarks"
                      placeholder="Add description of this item.."
                      key={record.key}
                    />
                  ) : ''}
                  <div style={{ display: 'flex' }}>
                    <div style={{ marginTop: '4px' }}>
                      {(record?.product_sku_info?.product_type === 'BUNDLE') && <H3Text text="Bundle" className="table-subscript table-subscript-tag" />}
                      {(record?.product_sku_info?.product_type === 'SERVICE') && <H3Text text="Service" className="table-subscript table-subscript-tag" />}
                    </div>
                    {record?.available_qty ? (
                      <div className="stock-value table-subscript table-subscript-tag" style={{ whiteSpace: 'nowrap' }}>
                        {`${record?.available_qty} ${record?.product_sku_info?.uom_info?.uqc?.toProperCase()}`}
                      </div>
                    ) : ''}
                  </div>
                  <div className="so-table__hsn">
                    <H3Text text={record?.product_sku_info?.product_type === 'SERVICE' ? 'SAC Code' : 'HSN Code'} className="so-table__hsn-label" />
                    <div className="so-table__hsn-value">
                      <input
                        value={record.hsn_code}
                        onChange={(e) => {
                          const copyData = JSON.parse(JSON.stringify(data));
                          copyData.map((item) => {
                            if (item.key === record.key) {
                              item.hsn_code = e.target.value;
                            }
                            return data;
                          });
                          updateData(copyData);
                        }}
                        className="so-table__remarks"
                        maxLength="10"
                      />
                    </div>
                  </div>
                </div>
              ) : (
                <React.Fragment>
                  <ProductFilterV2
                    record={record}
                    productTypes={['STORABLE', 'SERVICE', 'BUNDLE', 'NON_STORABLE']}
                    handleProductChangeValue={handleProductChangeValue}
                    handleProductChange={handleProductChange}
                    required
                    showError={formSubmitted && !record.product_sku_name}
                    isSalesProduct
                    onClear={(key) => {
                      const copyData = JSON.parse(JSON.stringify(data));
                      const newData = copyData.map((item) => {
                        if (item.key === key) {
                          return {
                            key: uuidv4(),
                            asset1: '',
                            product_sku_name: '',
                            quantity: '',
                            unitPrice: '',
                            taxId: '',
                            lot: '',
                            product_sku_id: null,
                            product_sku_info: null,
                          };
                        }
                        return {
                          ...item,
                        };
                      });
                      addNewRow();
                      updateData(newData);
                    }}
                    disabled={!tenantDepartmentId}
                    tenantDepartmentId={tenantDepartmentId}
                    showClear
                    filterReservedQuantity
                    selectedTenant={tenantId}
                    enableAdvanceSearch
                    handleMultiProductChange={handleMultiProductChange}
                    loading={loading}
                  />

                </React.Fragment>
              )}
          </div>
        ),
        visible: visibleColumns?.PRODUCT?.visible,
      },
      {
        title: 'Quantity',
        dataIndex: 'quantity',
        width: '100px',
        render: (text, record) => (
          <div style={{ width: '100px' }}>
            <H3FormInput
              value={record.quantity}
              type="number"
              containerClassName={`orgInputContainer ${(formSubmitted && Number(record.quantity) <= 0) ? 'create-pr__input' : ''}`}
              labelClassName="orgFormLabel"
              inputClassName="orgFormInput"
              onChange={(e) => {
                CustomFieldHelpers.updateCustomColumnValue(Number(e.target.value || 0), record.lineCustomFields?.find((i) => i?.fieldName === 'Quantity'), record.lineCustomFields, (value) => customLineInputChange(value, record?.key));
              }}
            // onBlur={(e) => {
            //   const copyData = JSON.parse(JSON.stringify(data));

            //   for (let i = 0; i < copyData?.length; i++) {
            //     const copyDataItem = copyData[i];
            //     if (copyDataItem?.key === record?.key) {
            //       for (let j = 0; j < pricesToApply?.length; j++) {
            //         if ((copyDataItem?.product_sku_id === pricesToApply[j]?.product_sku_id)) {
            //           if ((Number(e.target.value) >= pricesToApply[j]?.start_quantity && Number(e.target.value) <= pricesToApply[j]?.end_quantity)) {
            //             copyDataItem.unitPrice = pricesToApply[j]?.is_inclusive_of_tax ? pricesToApply[j]?.price_list_amount / ((100 + record?.taxInfo?.tax_value) / 100) : pricesToApply[j]?.price_list_amount;
            //             copyDataItem.discount = pricesToApply[j]?.discount_percentage;
            //             break;
            //           } else if ((pricesToApply[j]?.start_quantity === undefined && pricesToApply[j]?.end_quantity === undefined) || (pricesToApply[j]?.start_quantity === null && pricesToApply[j]?.end_quantity === null)) {
            //             copyDataItem.unitPrice = Number(pricesToApply[j]?.price_list_amount) ? Number(pricesToApply[j]?.price_list_amount) : (record?.default_selling_price || 0);
            //             copyDataItem.discount = pricesToApply[j]?.discount_percentage;
            //           } else {
            //             copyDataItem.unitPrice = record?.default_selling_price || 0;
            //           }
            //         }
            //       }
            //     }
            //   }
            //   updateData(copyData);
            // }}
            />
            {!isEstimate && record?.product_sku_info && !user?.tenant_info?.inventory_config?.settings?.enable_batch_consumed_qty_editable && (
              <div className="freebie-container">
                <div
                  className="freebie-cta"
                  onClick={(e) => {
                    const copyData = JSON.parse(JSON.stringify(data));
                    copyData.map((item) => {
                      if (item.key === record.key) {
                        item.showFreebie = true;
                      }
                      return data;
                    });
                    updateData(copyData);
                  }}
                >
                  + Add Free Qty
                </div>
                {record?.showFreebie && (
                  <div className="freebie-input">
                    <H3FormInput
                      value={record.free_quantity}
                      type="number"
                      containerClassName={`orgInputContainer ${(formSubmitted && Number(record.free_quantity) < 0) ? 'create-pr__input' : ''}`}
                      labelClassName="orgFormLabel"
                      inputClassName="orgFormInput"
                      onChange={(e) => {
                        const copyData = JSON.parse(JSON.stringify(data));
                        copyData.map((item) => {
                          if (item.key === record.key) {
                            item.free_quantity = e.target.value;
                          }
                          return data;
                        });
                        updateData(copyData);
                      }}
                    />
                    <CloseOutlined onClick={() => {
                      const copyData = JSON.parse(JSON.stringify(data));
                      copyData.map((item) => {
                        if (item.key === record.key) {
                          item.free_quantity = 0;
                          item.showFreebie = false;
                        }
                        return data;
                      });
                      updateData(copyData);
                    }}
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        ),
        visible: visibleColumns?.QUANTITY?.visible,
      },
      {
        title: 'Unit',
        width: '110px',
        render: (text, record) => (
          <div style={{ width: '110px' }}>
            <div className="orgInputContainer">
              <PRZSelect
                filterOption={false}
                className=""
                value={record?.uomId}
                onChange={(value) => {
                  const copyData = JSON.parse(JSON.stringify(data));
                  copyData.map((item) => {
                    if (item.key === record.key) {
                      item.uomId = value;
                      item.uom_info = item?.uom_list?.find((rec) => rec?.uom_id === value);
                    }
                    return item;
                  });
                  updateData(copyData);
                }}
                disabled={!record?.uom_list || record?.product_type === 'BUNDLE'}
                placeholder=""
              >
                {
                  record?.uom_list?.map((uom) => (
                    <Option key={uom?.uom_id} value={uom?.uom_id}>{`${uom?.uqc?.toProperCase()} (${uom?.uom_name?.toProperCase()})`}</Option>
                  ))
                }
              </PRZSelect>
            </div>
          </div>
        ),
        visible: visibleColumns?.UNIT?.visible,
      },
      {
        title: 'Rate',
        width: '100px',
        render: (text, record) => (
          <div style={{ width: '100px' }}>
            <H3FormInput
              value={record.unitPrice}
              type="number"
              containerClassName={`orgInputContainer ${(formSubmitted && Number(record.unitPrice) <= 0) ? 'create-pr__input' : ''}`}
              labelClassName="orgFormLabel"
              inputClassName="orgFormInput"
              onChange={(e) => {
                CustomFieldHelpers.updateCustomColumnValue(Number(e.target.value || 0), record.lineCustomFields?.find((i) => i?.fieldName === 'Rate'), record.lineCustomFields, (value) => customLineInputChange(value, record?.key, e.target.value));
              }}
              hideInput={isDataMaskingPolicyEnable && isHideSellingPrice}
              popOverMessage={"You don't have access to view rate"}
            />
          </div>
        ),
        visible: visibleColumns?.UNIT_PRICE?.visible,
      },
      {
        title: 'Tax',
        width: '110px',
        render: (text, record) => (
          <div style={{ width: '110px' }}>
            <div className="orgInputContainer">
              <SelectTax
                onChange={(value) => {
                  const copyData = JSON.parse(JSON.stringify(data));
                  const discountValue = Number(record.discount) || 0;
                  const taxableValue = record?.lineDiscountType === 'Percent' ? (record.quantity * record.unitPrice) * (1 - discountValue / 100) : Math.max(record.quantity * record.unitPrice - discountValue, 0);

                  copyData.map((item) => {
                    if (item.key === record.key) {
                      item.taxId = value?.tax_id;
                      item.taxInfo = value;
                      item.child_taxes = Helpers.computeTaxation(taxableValue, value, billFromState, billToState)?.tax_info?.child_taxes;
                    }
                    return item;
                  });
                  updateData(copyData);
                }}
                errorOutline={formSubmitted && !record.taxId}
                selectedGST={record.taxId}
              />
            </div>
          </div>
        ),
        visible: visibleColumns?.TAX?.visible,
      },
      {
        title: 'Discount',
        width: '150px',
        render: (text, record) => {
          const lineDiscountAmount = record?.lineDiscountType === 'Percent' ? (((record.quantity * record.unitPrice) / 100) * record.discount) : record?.discount;
          const currencySymbol = selectedCurrencyName?.currency_symbol || '';
          return (
            <Fragment>
              {isLineWiseDiscount ? (
                <Fragment>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <H3FormInput
                      value={record.discount}
                      type="number"
                      containerClassName="orgInputContainer"
                      labelClassName="orgFormLabel"
                      inputClassName="orgFormInput"
                      onChange={(e) => {
                        const copyData = JSON.parse(JSON.stringify(data));
                        const discountValue = Number(e.target.value) || 0;
                        const taxableValue = record?.lineDiscountType === 'Percent'
                          ? (record.quantity * record.unitPrice) * (1 - discountValue / 100)
                          : Math.max(record.quantity * record.unitPrice - discountValue, 0);
                        copyData.map((item) => {
                          if (item.key === record.key) {
                            item.discount = e.target.value || '';
                            item.unitDiscount = Number.parseFloat(Number(Number(e.target.value) / Number(record.quantity)).toFixed(DEFAULT_CUR_ROUND_OFF));
                            item.child_taxes = Helpers.computeTaxation(taxableValue, record.taxInfo, billFromState, billToState)?.tax_info?.child_taxes;
                          }
                          return data;
                        });
                        updateData(copyData);
                      }}
                      disabled={!isLineWiseDiscount}
                    />
                    <div className="orgInputContainer discount-type">
                      <PRZSelect
                        value={record?.lineDiscountType}
                        onChange={(value) => {
                          const copyData = JSON.parse(JSON.stringify(data));
                          const discountValue = Number(record.discount) || 0;
                          const taxableValue = value === 'Percent'
                            ? (record.quantity * record.unitPrice) * (1 - discountValue / 100)
                            : Math.max(record.quantity * record.unitPrice - discountValue, 0);
                          copyData.map((item) => {
                            if (item.key === record.key) {
                              item.lineDiscountType = value;
                              item.child_taxes = Helpers.computeTaxation(taxableValue, record.taxInfo, billFromState, billToState)?.tax_info?.child_taxes;
                            }
                            return item;
                          });
                          updateData(copyData);
                        }}
                        style={{
                          borderRadius: '2px',
                          width: '50px',
                        }}
                        disabled={!isLineWiseDiscount}
                        placeholder=""
                        options={discountOptions}
                      />
                    </div>
                  </div>
                  {(record.quantity > 0 && record.unitPrice > 0 && record.discount > 0) && (
                    <H3Text
                      text={(
                        <div>
                          {record?.lineDiscountType === 'Percent' ? (
                            (isDataMaskingPolicyEnable && isHideCostPrice) ? (
                              <HideValue showPopOver popOverMessage="You don't have access to view discount/unit" />
                            ) : (
                              `${currencySymbol}${(lineDiscountAmount / record?.quantity).toFixed(2)}/unit`
                            )
                          ) : (
                            `${((record?.discount / (record.quantity * record.unitPrice)) * 100).toFixed(2)}% | ${currencySymbol}${(lineDiscountAmount / record?.quantity).toFixed(2)}/unit`
                          )}
                        </div>

                      )}
                      className="table-subscript table-subscript__discount"
                    />
                  )}
                </Fragment>
              ) : (
                //  <div style={{ height: '32px', display: 'flex', alignItems: 'center' }}>{record?.discount || 0}%</div>
                <div>
                  <H3Text
                    text={
                      record?.discount ? (
                        record?.lineDiscountType === 'Percent' ? `${record?.discount?.toFixed(DEFAULT_CUR_ROUND_OFF)}%` : MONEY(record?.discount?.toFixed(DEFAULT_CUR_ROUND_OFF), selectedCurrencyName?.currency_code)
                      ) : (
                        record?.lineDiscountType === 'Percent' ? '0%' : MONEY(0, selectedCurrencyName?.currency_code)
                      )
                    }
                  />
                </div>
              )}
              {record?.quantity > 0 && record?.unitPrice > 0 && record?.discount > 0 && (
                <H3Text
                  text={(
                    <div>
                      {record?.lineDiscountType === 'Percent' ? (
                        (isDataMaskingPolicyEnable && isHideCostPrice) ? (
                          <HideValue showPopOver popOverMessage="You don't have access to view discount/unit" />
                        ) : (
                          `${currencySymbol}${(lineDiscountAmount / record?.quantity).toFixed(2)}/unit`
                        )
                      ) : (
                        `${((record?.discount / (record?.quantity * record?.unitPrice)) * 100).toFixed(2)}% | ${currencySymbol}${(lineDiscountAmount / record?.quantity).toFixed(2)}/unit`
                      )}
                    </div>
                  )}
                  className="table-subscript table-subscript__discount"
                />
              )}
              {record?.lineDiscountType !== 'Percent' && record?.showUnitDiscount && (
                <div className="small-add-button__blue small-add-button__blue-wrapper">
                  <FontAwesomeIcon icon={faTag} />
                  &nbsp;
                  <div className="doc-line__unit-discount">
                    <H3FormInput
                      value={record.unitDiscount}
                      type="number"
                      containerClassName="orgInputContainer"
                      labelClassName="orgFormLabel"
                      inputClassName="orgFormInput"
                      onChange={(e) => {
                        const copyData = JSON.parse(JSON.stringify(data));
                        const unitDiscountValue = Number.parseFloat(e.target.value) || 0;
                        const discountValue = Number.parseFloat(Number(Number(unitDiscountValue) * Number(record?.quantity)).toFixed(DEFAULT_CUR_ROUND_OFF));
                        const taxableValue = record?.lineDiscountType === 'Percent'
                          ? (record.quantity * record.unitPrice) * (1 - discountValue / 100)
                          : Math.max(record.quantity * record.unitPrice - discountValue, 0);

                        copyData.map((item) => {
                          if (item.key === record.key) {
                            item.discount = discountValue;
                            item.unitDiscount = unitDiscountValue || '';
                            item.child_taxes = Helpers.computeTaxation(taxableValue, record.taxInfo, billFromState, billToState)?.tax_info?.child_taxes;
                          }
                          return data;
                        });
                        updateData(copyData);
                      }}
                      disabled={!isLineWiseDiscount}
                    />
                    <H3Text text="/unit" className="doc-line__unit-discount-label" />
                    <FontAwesomeIcon
                      icon={faClose}
                      className="doc-line__unit-discount-close"
                      onClick={() => {
                        const copyData = JSON.parse(JSON.stringify(data));
                        copyData.map((item) => {
                          if (item.key === record.key) {
                            item.showUnitDiscount = !record?.showUnitDiscount;
                          }
                          return data;
                        });
                        updateData(copyData);
                      }}
                    />
                  </div>
                </div>
              )}
            </Fragment>
          )
        },
        visible: visibleColumns?.DISCOUNT?.visible,
      },
      {
        title: 'Total',
        width: '120px',
        fixed: 'right',
        render: (text, record) => {

          const discountValue = Number(record.discount) || 0;
          const taxableValue = record?.lineDiscountType === 'Percent' ? (record.quantity * record.unitPrice) * (1 - discountValue / 100) : Math.max(record.quantity * record.unitPrice - discountValue, 0);

          return (
            <H3Text
              text={record.taxInfo
                ? MONEY(Helpers.computeTaxation(taxableValue, record.taxInfo, billFromState, billToState)?.taxed_value, selectedCurrencyName?.currency_code) : 0}
              hideText={isDataMaskingPolicyEnable && isHideSellingPrice}
              popOverMessage={"You don't have access to view total amount"}
            />
          );
        },
        visible: visibleColumns?.LINE_TOTAL?.visible,
      },
      {
        title: '',
        width: '40px',
        fixed: 'right',
        render: (text, record) => (
          <Fragment>
            {data.length !== record.key && (
              <div className="update-so__delete-line-button" onClick={() => handleDelete(record.key)}>
                <CloseOutlined />
              </div>
            )}
          </Fragment>
        ),
        visible: true,
      },
    ];

    if (cfSalesOrderLine?.length) {
      productListColumns.splice(3, 0, ...CustomFieldHelpers.renderCustomLineColumns(false, cfSalesOrderLine, visibleColumns, customLineInputChange, formSubmitted, '', '', '', '', '', '', '', isCarryForward));
    }

    return productListColumns.filter((item) => item.visible);
  };

  const bundleListColumns = [
    {
      title: 'SKU#',
      render: (record) => (
        <Link to={`/inventory/product/view/${record?.product_info?.product_sku_id}`} target="_blank">
          {record?.product_info?.internal_sku_code}
        </Link>
      ),
    },
    {
      title: 'PRODUCT',
      width: '300px',
      render: (text, record) => (
        <div className="so-table__product-name">
          {record?.product_info?.product_sku_name}
        </div>
      ),
    },
    {
      title: 'QUANTITY',
      dataIndex: 'quantity',
      width: '120px',
      render: (text, record) => (
        <div className="so-qty-input">
          {`${record?.bundle_quantity} ${record?.uom_info?.uqc?.toProperCase()}`}
        </div>
      ),
    },
    {
      title: 'UNIT PRICE',
      render: (text, record) => (
        <div>
          -
        </div>
      ),
    },

    {
      title: 'TAX',
      render: (text, record) => (
        <div style={{ width: '140px' }}>
          {`${record?.tax_info?.tax_value}%`}
        </div>
      ),
    },
  ];
  return (
    <div className="so-lines__wrapper">
      <Table
        title={title}
        bordered
        loading={loading}
        showHeader
        size="small"
        columns={getColumns()}
        dataSource={data}
        pagination={false}
        expandable={{
          rowExpandable: (record) => record?.bundle_products?.length,
          defaultExpandAllRows: true,
          expandedRowRender: (record) => (
            <Table
              bordered
              loading={loading}
              showHeader
              size="small"
              columns={bundleListColumns}
              dataSource={record?.bundle_products?.map((item) => ({
                ...item,
                bundle_quantity: parseFloat(item?.bundle_quantity || '0') * parseFloat(record?.quantity || '0'),
              }))}
              pagination={false}
            />
          ),
        }}
        scroll={{ x: 'max-content' }}
      />
    </div>
  );
};

/**
 *
 * @param UserReducers
 * @param PurchaseOrderReducers
 * @return {{updateCustomPurchaseOrderLoading: *, selectedPurchaseOrder, user}}
 */
const mapStateToProps = ({
  UserReducers, PurchaseOrderReducers, OfferReducers, CFV2Reducers,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  updateCustomPurchaseOrderLoading: PurchaseOrderReducers.updateCustomPurchaseOrderLoading,
  selectedPurchaseOrder: PurchaseOrderReducers.selectedPurchaseOrder,
  getOfferByTenantSkuLoading: OfferReducers.getOfferByTenantSkuLoading,
  priceMasking: UserReducers.priceMasking,
});

const mapDispatchToProps = () => ({

});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(SoLines));
