/* eslint-disable jsx-a11y/alt-text */
import React, { Component, Fragment } from 'react';
import { connect } from 'react-redux';
import { withRout<PERSON>, <PERSON> } from 'react-router-dom';
import PropTypes from 'prop-types';
import { Drawer, Progress, Table, Tooltip, Dropdown, Menu } from 'antd';
import { v4 as uuidv4 } from 'uuid';
import { QuestionCircleFilled } from '@ant-design/icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCaretDown, faCaretRight } from '@fortawesome/free-solid-svg-icons';
import { QUANTITY } from '@Apis/constants';
import Helpers from '@Apis/helpers';
import H3Text from '@Uilib/h3Text';
import H3Image from '@Uilib/h3Image';
import closeIcon from '@Images/icons/icon-close-blue.png';
import AddBatchV2 from '@Components/Production/ManufacturingOrders/ViewManufacturingOrder/Drawer/AddBatchV2';
import ActivityLogActions from '@Actions/activityLogActions';
import MOActions from '@Actions/moActions';
import H3Progress from '@Components/Common/H3Progress';
import BatchSelector from '../../BatchSelector';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import ProductCategoryLabel from '../../../../../Common/ProductCategoryLabel';
import HideValue from '../../../../../Common/RestrictedAccess/HideValue';
import PRZModal from '../../../../../Common/UI/PRZModal';
import ProductCodesAndName from '@Components/Common/ProductCodesAndName';
import './style.scss';

class FinishedProductsLines extends Component {

  constructor(props) {
    super(props);
    this.state = {
    };
  }

  getColumns = () => {
    const {
      cfFGLine, visibleColumns, issueContractWorkOrder, user, priceMasking,
    } = this.props;
    const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;
    const enableInternalRefCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_ref_code;
    const enableInternalSKUCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_sku_code;
    const finishedProductColumns = [
      {
        title: '',
        render: (record) => (
          <div className="mobile-list__item">
            <div style={{ display: 'flex' }}>
              {enableInternalSKUCode && (<Link to={`/inventory/product/view/${record?.tenant_product_info?.product_sku_id}`}>
                <H3Text text={`#${record?.tenant_product_info?.internal_sku_code}`} className="mobile-list__item-number" />
              </Link>)}
            </div>
            <div className="mobile-list__item-info">
              {`${record?.tenant_product_info?.ref_product_code ? `${record?.tenant_product_info?.ref_product_code} - ` : ''}${record?.tenant_product_info?.product_sku_name}`?.trim()}
            </div>
            <div className="mobile-list__item-completion">
              <div style={{ fontSize: '11px', width: 'calc(100% - 100px)' }}>
                <Progress
                  percent={parseInt((record?.completed_quantity / record?.quantity) * 100)}
                  status="active"
                  size="small"
                  strokeColor={{ from: '#108ee9', to: '#87d068' }}
                  trailColor="#2d7df71a"
                />
              </div>
              <H3Text text="Completed" className="mobile-list__item-completion-text" />
            </div>
          </div>
        ),
        responsive: ['xs'],
      },
      {
        title: 'Product',
        width: '180px',
        render: (record) => (
          <Fragment>
            <div onClick={() => this.setState({ showPRZModal: true, selectedDocumentId: record?.tenant_product_info?.product_sku_id })}>
              <ProductCodesAndName
                skuCode={record?.tenant_product_info?.internal_sku_code}
                refCode={record?.tenant_product_info?.ref_product_code}
                name={record?.tenant_product_info?.product_sku_name}
                showSku={enableInternalSKUCode}
                showRefCode={enableInternalRefCode}
              />
            </div>
            {record?.product_category_info?.category_path?.length > 0 && (
              <ProductCategoryLabel
                categoryPath={record?.product_category_info?.category_path}
                categoryName={record?.product_category_info?.category_path?.at(-1)}
                containerStyle={{
                  width: 'fit-content',
                }}
              />
            )}
          </Fragment>
        ),
        visible: true,
        responsive: ['sm', 'md', 'lg', 'xxl'],
        fixed: 'left',
      },
      {
        title: 'Production',
        render: (text, record) => (
          <div>
            <div style={{ lineHeight: '24px' }}>
              <H3Text text={`Target - ${record?.quantity} ${record?.uom_info?.uqc?.toProperCase() || ''}`} className="" />
            </div>
            <div style={{ lineHeight: '24px' }}>
              <H3Text text={`Completed - ${QUANTITY(record?.completed_quantity, record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase() || ''}`} className="" />
            </div>
            <div style={{
              paddingRight: '5px', width: '150px', cursor: 'pointer', marginTop: '5px',
            }}
            >
              <Tooltip title={(
                <div>
                  <H3Text text={`Target Production - ${record?.quantity} ${record?.uom_info?.uqc?.toProperCase() || ''}`} />
                  <H3Text text={`Completed - ${record?.completed_quantity} ${record?.uom_info?.uqc?.toProperCase() || ''}`} />
                </div>
              )}
              >
                <H3Progress percent={parseInt((record?.completed_quantity / record?.quantity) * 100)} barWidth="150px" />
              </Tooltip>
            </div>
          </div>
        ),
        visible: true,
        responsive: ['sm', 'md', 'lg', 'xxl'],
      },
      {
        title: 'Expected Unit Cost',
        render: (text, record) => (
          <Fragment>
            {(isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver popOverMessage={"You don't have access to view unit price"} /> : (
              <Tooltip
                title={(
                  <Fragment>
                    <H3Text text={`RM Cost - ${this.props.MONEY((record?.rm_cost_per_unit || 0))}`} />
                    <H3Text text={`Other Charges - ${this.props.MONEY(record?.charges_per_unit || 0)}`} />
                  </Fragment>
                )}
                style={{ cursor: 'pointer' }}
              >
                <div style={{ display: 'flex', cursor: 'pointer' }}>
                  <QuestionCircleFilled style={{ marginTop: '3px', marginRight: '4px', color: '#2D7DF7' }} />
                  {(record?.bom_id ? `${this.props.MONEY((record?.rm_cost_per_unit || 0) + (record?.charges_per_unit || 0))}` : '-')}
                </div>
              </Tooltip>
            )}
          </Fragment>
        ),
        visible: true,
        responsive: ['sm', 'md', 'lg', 'xxl'],
        hidden: !(Helpers.getPermission(Helpers.permissionEntities.MO,
          Helpers.permissionTypes.COSTING, this.props.user)),
      },
      {
        title: 'Expected Cost',
        render: (text, record) => (record?.bom_id ? ((isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver popOverMessage={"You don't have access to view expected cost"} /> : `${this.props.MONEY(((record?.rm_cost_per_unit || 0) + (record?.charges_per_unit)) * record?.quantity)}`) : '-'),
        responsive: ['sm', 'md', 'lg', 'xxl'],
        visible: true,
        hidden: !(Helpers.getPermission(Helpers.permissionEntities.MO,
          Helpers.permissionTypes.COSTING, this.props.user)),
      },
      {
        title: 'Incurred Cost',
        render: (record) => (
          <Fragment>
            {(isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver popOverMessage={"You don't have access to view incurred cost"} /> : (
              <Tooltip
                title={(
                  <Fragment>
                    <H3Text text={`RM Cost - ${this.props.MONEY((record?.total_fg_rm_incurred_cost || 0))}`} />
                    <H3Text text={`Other Charges - ${this.props.MONEY(record?.total_fg_extra_charges || 0)}`} />
                  </Fragment>
                )}
                style={{ cursor: 'pointer' }}
              >
                <div style={{ display: 'flex', cursor: 'pointer' }}>
                  <QuestionCircleFilled style={{ marginTop: '3px', marginRight: '4px', color: '#2D7DF7' }} />
                  {this.props.MONEY(record?.total_fg_incurred_cost || 0)}
                </div>
              </Tooltip>
            )}
          </Fragment>
        ),
        responsive: ['sm', 'md', 'lg', 'xxl'],
        visible: true,
        hidden: !(Helpers.getPermission(Helpers.permissionEntities.MO,
          Helpers.permissionTypes.COSTING, this.props.user)),
      },
      {
        title: 'Job Work Qty',
        render: (text, record) => (
          <div>
            <div style={{ lineHeight: '24px' }}>
              <H3Text text={`${record?.total_job_work_quantity || 0} ${record?.uom_info?.uqc?.toProperCase() || ''}`} className="" />
            </div>
          </div>
        ),
        visible: true,
        responsive: ['sm', 'md', 'lg', 'xxl'],
      },
      {
        title: '',
        render: (record) => {
          const { selectedMO } = this.props;
          return (
            <Dropdown
              overlay={(
                <Menu>
                  <Menu.Item>
                    <H3Text
                      text={(
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <div>
                            Produce
                          </div>
                        </div>
                      )}
                      onClick={() => {
                        if (selectedMO?.status === 'IN_PROGRESS') {
                          this.setState({ showAddFgBatchDrawer: true, selectedFgTenantProductId: record?.tenant_product_id });
                        }
                      }}
                      className="hide__in-mobile"
                    />
                  </Menu.Item>
                  <Menu.Item>
                    <H3Text
                      text={(
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <div>
                            Outsource
                          </div>
                        </div>
                      )}
                      onClick={() => {
                        if (selectedMO?.status === 'IN_PROGRESS') {
                          issueContractWorkOrder(record?.tenant_product_id)
                        }
                      }}
                      className="hide__in-mobile"
                    />
                  </Menu.Item>
                </Menu>
              )}
              trigger={['click']}
              placement="bottomRight"
            >
              <div
                className={`view-fg__action-button-inactive ${selectedMO?.status === 'IN_PROGRESS' ? 'view-fg__action-button' : ''}`}
              >
                <div style={{ fontWeight: '500px' }}>+ Add Batch </div>
              </div>
            </Dropdown>
          );
        },
        responsive: ['sm', 'md', 'lg', 'xxl'],
        fixed: 'right',
        className:  !Helpers.getPermission(Helpers.permissionEntities.INVENTORY_INDENT, Helpers.permissionTypes.CREATE, this.props.user) ? 'display-none' : '',
        hidden: this.props.isIssueSfgEnabled,
        visible: true,
      },
    ].filter((item) => item.hidden !== true);

    if (cfFGLine?.length) {
      finishedProductColumns.splice(6, 0, ...CustomFieldHelpers.renderCustomLineColumns(false, cfFGLine, visibleColumns, null));
    }

    return finishedProductColumns.filter((item) => item.visible);
  }

  render() {
    const {
      selectedMO, getMOById, getMOByIdLoading, multipleMo, user, getActivityLog, callback,
    } = this.props;
    const { finishedProductColumns, showAddFgBatchDrawer, selectedFgTenantProductId, showPRZModal, selectedDocumentId } = this.state;
    return (
      <Fragment>
        <div className="fp-table-wrapper">
          <Table
            showHeader
            bordered
            title={selectedMO?.mo_finished_goods?.length > 1 ? () => (
              <div className="fp-table-header">
                <div className="fp-table-header__title">{`${selectedMO?.mo_finished_goods?.length} Finished Goods`}</div>
              </div>
            ) : false}
            size="small"
            columns={multipleMo ? this.getColumns()?.filter((item) => item.title !== 'MO COST/UNIT' && item.title !== 'ESTIMATED MO COST' && item.title !== 'INCURRED MO COST') : this.getColumns()}
            dataSource={selectedMO?.mo_finished_goods?.map((item) => ({
              ...item,
              key: uuidv4(),
              lineCustomFields: item?.custom_fields,
              product_category_info: item?.tenant_product_info?.product_category_info,
            })) || []}
            loading={getMOByIdLoading}
            expandable={!['DRAFT', 'CONFIRMED', 'VOID'].includes(selectedMO?.status) ? {
              rowExpandable: (record) => (!!record?.finished_good_adjustments?.length),
              expandedRowRender: (record) => (
                <div className="view-mo__fg-batches">
                  <BatchSelector
                    title="Completed Batches"
                    batches={record.finished_good_adjustments}
                    unicommerceInfo={{
                      facilityCode: selectedMO?.tenant_info?.unicommerce_facility_code,
                      ucSkuCode: record?.tenant_product_info?.unicommerce_sku_code,
                      entityNumber: selectedMO?.mo_number,
                      entityName: 'MANUFACTURING_ORDER',
                    }}
                    uomInfo={record?.tenant_product_info?.uom_info}
                    expiryDays={record?.tenant_product_info?.expiry_days}
                    showCancel
                    isFinishedGood
                    status={selectedMO?.status}
                    isContractOrder={selectedMO?.tenant_seller_id}
                    callback={callback}
                  />
                </div>
              ),
              // expandIcon: ({ expanded, onExpand, record }) => (expanded ? (
              //   <FontAwesomeIcon icon={faCaretDown} className="table-expand-icon" onClick={(e) => onExpand(record, e)} />
              // ) : (
              //   <FontAwesomeIcon icon={faCaretRight} className="table-expand-icon" onClick={(e) => onExpand(record, e)} />
              // )),
            } : false}
            pagination={false}
            scroll={{ x: 'max-content' }}
          />
        </div>
        <Drawer
          onClose={() => this.setState({ showAddFgBatchDrawer: false })}
          open={showAddFgBatchDrawer}
          width="1132px"
          destroyOnClose
          getContainer={() => document.getElementById('add_batch_wrapper')}
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header" style={{ width: '1081px' }}>
              <H3Text text="Create New Batch of Finished Product" className="custom-drawer__title" />
              <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ showAddFgBatchDrawer: false })} />
            </div>
          </div>
          <AddBatchV2
            finishedGood={selectedMO?.mo_finished_goods?.find((item) => item.tenant_product_id == selectedFgTenantProductId)}
            rawMaterials={selectedMO?.mo_lines_last_level_view?.filter((item) => item?.fg_tenant_product_id == selectedFgTenantProductId)?.map((item) => ({
              ...item,
              tenant_product_info: {
                ...item?.tenant_product_info,
                product_batches: item.tenant_product_info.product_batches?.map((i) => ({ ...i, batch_in_use: true, available_qty: (i?.available_qty / item?.uom_info?.ratio) * item.tenant_product_info?.uom_info?.ratio })),
              },
            }))}
            byProducts={selectedMO?.by_product_lines?.filter((item) => item?.fg_tenant_product_id == selectedFgTenantProductId)}
            extraCharges={selectedMO?.extra_charges?.filter((item) => item?.fg_tenant_product_id == selectedFgTenantProductId)}
            selectedMO={selectedMO}
            callback={() => {
              this.setState({ showAddFgBatchDrawer: false });
              getMOById(selectedMO?.tenant_id, selectedMO?.mo_id);
              getActivityLog(selectedMO?.mo_id, 'manufacturing_order', 1, 10, () => { });
              if (callback) {
                callback();
              }
            }}
            isFlexible={!!(selectedMO?.bom_info?.flexible_consumption === 'ALLOWED')}
            isFgStep
          />

        </Drawer>
        <PRZModal
          isOpen={showPRZModal}
          onClose={() => {
            this.setState({ showPRZModal: false, selectedDocumentId: null });
          }}
          entityType="product"
          documentId={selectedDocumentId}
        />
      </Fragment>
    );
  }
}

const mapStateToProps = ({ UserReducers, MOReducers }) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  selectedMO: MOReducers.selectedMO,
  getMOByIdLoading: MOReducers.getMOByIdLoading,
  priceMasking: UserReducers.priceMasking,
});

const mapDispatchToProps = (dispatch) => ({
  getActivityLog: (entityId, entityName, page, limit, callback) => dispatch(ActivityLogActions.getActivityLog(entityId, entityName, page, limit, callback)),
  getActivityLogSuccess: (activityLog) => dispatch(ActivityLogActions.getActivityLogSuccess(activityLog)),
  getMOById: (tenantId, moId) => dispatch(MOActions.getMOById(tenantId, moId)),
});

FinishedProductsLines.propTypes = {
  selectedMO: PropTypes.any,
  getMOByIdLoading: PropTypes.bool,
  multipleMo: PropTypes.bool,
  MONEY: PropTypes.func,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(FinishedProductsLines));
