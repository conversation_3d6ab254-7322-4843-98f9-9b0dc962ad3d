import React, { useEffect, useState } from 'react';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { connect } from 'react-redux';
import './style.scss';
import PRZSelect from '@Components/Common/UI/PRZSelect';
import CMActions from '@Actions/settings/cmActions';

const TnCSelector = ({ entityName, tenantId, updateTnC, getCM, disabled, updateTncId, tncId, isCarryForwardTnC }) => {
  const [currentTnCValue, setCurrentTnCValue] = useState(null);
  const [tncData, setTncData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch Terms and Conditions data when the component mounts or tenantId changes and update case.
  useEffect(() => {
    if (tenantId && entityName) {
      setIsLoading(true);
      getCM('', '', entityName, '', '', tenantId, 'TERMS_AND_CONDITIONS', true, true, (t_and_c_Data) => {
        if (t_and_c_Data) {
          setIsLoading(false);
          const tempTnCData = t_and_c_Data?.filter(item => {
            return (item?.is_active && !item?.is_archived) || item?.entity_level_message_id === tncId;
          }); setTncData(tempTnCData);
          if (tempTnCData?.length > 0 && !tncId && !isCarryForwardTnC) {
            const defaultTnC = tempTnCData?.find(item => item.is_default);
            if (defaultTnC) {
              setCurrentTnCValue(defaultTnC?.entity_level_message_id);
              updateTnC(defaultTnC?.message);
              updateTncId(defaultTnC?.entity_level_message_id);
            }
          } else if (tncId && !isCarryForwardTnC) {
            setCurrentTnCValue(tncId);
          }
        }
      });
    }
  }, [tenantId, entityName, tncId]);

  // Handle selection change
  const handleSelectChange = (value) => {
    if (value) {
      const selectedTnC = tncData?.find(item => item?.entity_level_message_id === value);
      if (selectedTnC) {
        setCurrentTnCValue(selectedTnC?.entity_level_message_id);
        updateTnC(selectedTnC?.message);
        updateTncId(selectedTnC?.entity_level_message_id);
      }
    }
  };
  return (
    <React.Fragment>
      <div className='ant-col-md-6'>
        <PRZSelect
          onChange={handleSelectChange}
          value={currentTnCValue}
          options={tncData?.map((item) => ({
            label: `${item.title}`,
            value: item.entity_level_message_id || '-',
          })) || []}
          style={{
            height: '30px',
          }}
          placeholder='Select Terms and Conditions'
          disabled={disabled}
          loading={isLoading}
        >
        </PRZSelect>
      </div>
    </React.Fragment>
  );
};

const mapStateToProps = ({
}) => ({
});

const mapDispatchToProps = (dispatch) => ({
  getCM: (orgId, entityId, entityType, page, limit, tenantId, actionType, isActive, isArchived, callback) => dispatch(CMActions.getCM(orgId, entityId, entityType, page, limit, tenantId, actionType, isActive, isArchived, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(TnCSelector));
