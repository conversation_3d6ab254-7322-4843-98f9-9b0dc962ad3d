import React, { useEffect, useMemo, useState } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { Menu, Tabs } from 'antd';
import './style.scss';
import documentUINamesConstant from '@Apis/documentUINamesConstant';
import { getDocumentMenuItems, getTabItems } from './entityHealper';

const { TabPane } = Tabs;

const AllEntityListV2 = ({ user, history, location }) => {
  const urlParams = useMemo(() => new URLSearchParams(location?.search), [location?.search]);

  const documentUINames = useMemo(() => documentUINamesConstant(user), [user]);

  // Get left menu items based on UI names
  const menuItems = useMemo(() => getDocumentMenuItems(documentUINames));

  // Extract initial main tab from URL or default to 'PURCHASE_ORDER'
  const initialDocTab = urlParams.get('docTab') || 'PURCHASE_ORDER';
  const [currentDocTab, setCurrentDocTab] = useState(initialDocTab);

  // Get the right-hand tab list based on selected left menu item
  const tabItems = useMemo(() => getTabItems(currentDocTab, menuItems) || [], [currentDocTab, menuItems]);

  // Set inner tab from URL (if valid), else use first available tab
  const [activeInnerTab, setActiveInnerTab] = useState(() => {
    const innerFromURL = urlParams.get('innerTab');
    return innerFromURL && tabItems?.find(tab => tab?.key === innerFromURL)
      ? innerFromURL
      : tabItems?.[0]?.key || null;
  });

  // If currentDocTab changes → tabItems will change → update activeInnerTab accordingly
  useEffect(() => {
    const innerFromURL = urlParams.get('innerTab');
    const validTab = tabItems?.find(tab => tab?.key === innerFromURL);
    setActiveInnerTab(validTab?.key || tabItems?.[0]?.key || null);
  }, [tabItems, urlParams]);

  /**
   * Updates URL query params without removing existing ones.
   * Used for syncing `docTab` and `innerTab` values into the browser address bar.
   */
  const updateURLParams = (updatedParams) => {
    const params = new URLSearchParams(location?.search);
    Object.entries(updatedParams)?.forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.set(key, value);
      }
    });
    history.replace({ search: params.toString() });
  };

  /**
   * When a new document type is selected from the left menu.
   * Automatically switches to the first inner tab of that document.
   */
  const handleMenuClick = ({ key }) => {
    const firstInnerTab = getTabItems(key, menuItems)?.[0]?.key || '';
    setCurrentDocTab(key);
    setActiveInnerTab(firstInnerTab);
    updateURLParams({ docTab: key, innerTab: firstInnerTab });
  };

  /**
   * When user clicks on any inner tab from the right section.
   * Keeps URL in sync.
   */
  const handleTabChange = (key) => {
    setActiveInnerTab(key);
    updateURLParams({ docTab: currentDocTab, innerTab: key });
  };

  return (
    <div className="entity__wrapper-outer" style={{ display: 'flex' }}>
      {/* Left menu: list of document categories (Purchase Order, MO, etc.) */}
      <div className="entity__menu">
        <Menu
          onClick={handleMenuClick}
          selectedKeys={[currentDocTab]}
          mode="inline"
          items={menuItems}
          style={{
            width: 226,
            overflowY: 'auto',
            maxHeight: 'calc(100dvh - 140px)',
            overflowX: 'hidden',
          }}
        />
      </div>

      {/* Right tabs: varies depending on selected document in left menu */}
      <div className="entity__tabs" style={{ flex: 1, paddingLeft: 10 }}>
        <Tabs activeKey={activeInnerTab} onChange={handleTabChange}>
          {tabItems?.map(({ key, tabLabel, component }) => (
            <TabPane key={key} tab={tabLabel}>
              {component}
            </TabPane>
          ))}
        </Tabs>
      </div>
    </div>
  );
};

// Get user object from Redux store
const mapStateToProps = ({ UserReducers }) => ({
  user: UserReducers?.user,
});

export default connect(mapStateToProps)(withRouter(AllEntityListV2));
