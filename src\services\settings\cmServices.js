import Helpers from '@Apis/helpers';
import Constants from '@Apis/constants';

/**
 * @param tenantId
 * @param cmId
 * @param poId
 */
class CMServices {
  /**
   *
   * @param {*} payload
   * @return
   */
  createCM = async (payload) => {
    const cm = await Helpers.post({
      url: Constants.CUSTOM_REASON,
      data: payload,
    });
    return cm;
  }

  /**
   *
   * @param {*} payload
   * @return
   */
  updateCM = async (payload) => {
    const cm = await Helpers.put({
      url: Constants.CUSTOM_REASON,
      data: payload,
    });
    return cm;
  }

  /**
   *
   * @param {*} payload
   * @return
   */
  updateCMStatus = async (payload) => {
    const po = await Helpers.post({
      url: Constants.CUSTOM_REASON_STATUS,
      data: payload,
    });
    return po;
  }

  /**
   *
   * @param {*} orgId
   * @param {*} entityId
   * @param {*} entityType
   * @param {*} cmId
   * @param {*} page
   * @param {*} limit
   * @return
   */
  getCM = async (orgId, entityId, entityType, page, limit, tenantId,actionType,isActive,isArchived) => {
    let url = `${Constants.CUSTOM_REASON}?`;
    if (orgId) url += `org_id=${orgId}&`;
    if (entityId) url += `entity_id=${entityId}&`;
    if (entityType) url += `entity_name=${entityType}&`;
    if (limit) url += `limit=${limit}&`;
    if (page) url += `page=${page}&`;
    if (tenantId) url += `tenant_id=${tenantId}&`;
    if (actionType) url += `type=${actionType}&`;
    if (isActive) url += `is_active=${isActive}&`;
    if (isArchived) url += `is_archived=${isArchived}&`;
    const cm = await Helpers.get({ url });
    return cm;
  }

  /**
   * @param payload
   * @return {Promise<*>}
   */
  deleteCM = async (payload) => {
    const deleteCM = await Helpers.delete({
      url: Constants.CUSTOM_REASON,
      data: payload,
    });
    return deleteCM;
  }
}
export default new CMServices();
