import { useSelector } from 'react-redux';
import { useMemo } from 'react';

export function useProductCodes() {
  const user = useSelector(state => state?.UserReducers?.user);

  const enableInternalSKUCode = useMemo(() => {
    return user?.tenant_info?.inventory_config?.settings?.enable_internal_sku_code;
  }, [user]);

  const enableInternalRefCode = useMemo(() => {
    return user?.tenant_info?.inventory_config?.settings?.enable_internal_ref_code;
  }, [user]);

  return {
    enableInternalSKUCode,
    enableInternalRefCode,
  };
}