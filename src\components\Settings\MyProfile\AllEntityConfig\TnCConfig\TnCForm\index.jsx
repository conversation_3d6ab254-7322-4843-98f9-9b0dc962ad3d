import React, { Fragment, useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import H3FormInput from '@Uilib/h3FormInput';
import CMActions from '@Actions/settings/cmActions';
import { defaultButtonTypes } from '@Uilib/h3Button';
import './style.scss';
import PRZButton from '@Components/Common/UI/PRZButton';
import RichTextEditor from '@Components/Common/RichTextEditor';
import { Checkbox, notification } from 'antd';

const initialFormState = {
  tncTitle: '',
  tncContent: '',
  markAsDefault: false,
  formSubmitted: false,
};

const TnCForm = ({
  selectedTnC,
  entityName,
  createCMLoading,
  updateCMLoading,
  createCM,
  updateCM,
  callback,
  user,
}) => {
  const [formData, setFormData] = useState(initialFormState);

  const { tncTitle, tncContent, markAsDefault, formSubmitted } = formData;

  const updateFormData = (key, value) => {
    setFormData((prev) => ({ ...prev, [key]: value }));
  };

  useEffect(() => {
    if (selectedTnC) {
      setFormData({
        tncTitle: selectedTnC.title || '',
        tncContent: selectedTnC.message || '',
        markAsDefault: selectedTnC.is_default || false,
      });
    }
  }, [selectedTnC]);

  const handleSubmitTnC = () => {
    updateFormData('formSubmitted', true);

    if (!tncTitle || !tncContent) {
      notification.error({
        message: `${!tncTitle ? 'Title' : 'Terms and Conditions'} is required.`,
        duration: 4,
        placement: 'top',
      });
      return;
    }

    const payload = {
      org_id: user?.org_id,
      tenant_id: user?.tenant_info?.tenant_id,
      entity_name: entityName,
      type: 'TERMS_AND_CONDITIONS',
      title: tncTitle,
      message: tncContent,
      is_default: markAsDefault,
    };
    if (!selectedTnC) {
      createCM(payload, (response) => {
        if (response?.success) {
          callback();
        }
      });
    } else {
      updateCM({ ...payload, entity_level_message_id: selectedTnC?.entity_level_message_id }, (response) => {
        if (response?.success) {
          callback();
        }
      });
    }
  };

  return (
    <div>
      <Fragment>
        <div className="ant-row new-vendor-form__wrapper">
          <div className="ant-col-md-24">
            <H3FormInput
              name={'Terms and Conditions Title'}
              type="text"
              containerClassName="orgInputContainer"
              labelClassName="orgFormLabel"
              inputClassName="orgFormInput"
              placeholder="Enter Title"
              onChange={(e) => updateFormData('tncTitle', e.target.value)}
              disabled={createCMLoading || updateCMLoading}
              value={tncTitle}
              label="Terms and Conditions Title."
              maxlength="50"
              required
              showError={formSubmitted && !tncTitle}
            />
          </div>
          <div className="ant-col-md-24" style={{ marginBottom: '10px' }}>
            <label className="orgFormLabel">
              Terms and Conditions.
              <span style={{ color: 'red', marginLeft: '5px' }}>*</span>
            </label>
            <RichTextEditor
              value={tncContent}
              disabled={createCMLoading || updateCMLoading}
              placeholder="Enter Terms and Conditions here..."
              onChange={(val) => updateFormData('tncContent', val)}
            />
            {formSubmitted && !tncContent && (
              <div className="input-error">* Please enter Terms and Conditions.</div>
            )}
          </div>
          <div className="ant-col-md-24">
            <div className="orgInputContainer">
              <Checkbox
                checked={markAsDefault}
                disabled={createCMLoading || updateCMLoading}
                onChange={() =>
                  updateFormData('markAsDefault', !markAsDefault)
                }
              >
                <label className="orgFormLabel">Mark as Default Terms and Conditions.</label>
              </Checkbox>
            </div>
          </div>
          <div className="custom-drawer__footer">
            <div className="ant-col-md-6">
              <PRZButton
                buttonType={defaultButtonTypes.BLUE_ROUNDED}
                onClick={handleSubmitTnC}
                isLoading={createCMLoading || updateCMLoading}
                disabled={createCMLoading || updateCMLoading}
              >
                {!selectedTnC ? 'Add Terms and Conditions' : 'Update Terms and Conditions'}
              </PRZButton>
            </div>
          </div>
        </div>
      </Fragment>
    </div>
  );
};

const mapStateToProps = ({ UserReducers, CMReducers }) => ({
  user: UserReducers.user,
  createCMLoading: CMReducers.createCMLoading,
  updateCMLoading: CMReducers.updateCMLoading,
});

const mapDispatchToProps = (dispatch) => ({
  createCM: (payload, callback) =>
    dispatch(CMActions.createCM(payload, callback)),
  updateCM: (payload, callback) =>
    dispatch(CMActions.updateCM(payload, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(TnCForm));
