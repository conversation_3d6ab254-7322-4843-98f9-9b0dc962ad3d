import React, { Component, Fragment } from 'react';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import {
  Tabs, Drawer, Popconfirm, Popover, Image, Tooltip,
} from 'antd';
import {
  CopyOutlined, EditOutlined, LoadingOutlined, SearchOutlined,
} from '@ant-design/icons';
import ProductActions from '@Actions/productActions';
import H3Text from '@Uilib/h3Text';
import defaultImage from '@Images/icons/imageDefault.png';
import box from '@Images/icons/box.png';
import exchange from '@Images/icons/exchange.png';
import Helpers from '@Apis/helpers';
import tick from '@Images/icons/tick.png';
import ProductForm from '@Components/Common/ProductForm';
import closeIcon from '@Images/icons/icon-close-blue.png';
import H3Image from '@Uilib/h3Image';
import ShopifyIcon from '@Images/admin/integrations/shopify-icon.png';
import ShopifyIntegrationActions from '@Actions/integrations/shopifyIntegrationActions';
import OtherDepartmentsStock from '@Components/Inventory/ManageProducts/ProductsList/OtherDepartmentsStock';
import { QUANTITY, toISTDate } from '@Apis/constants';
import ucIcon from '@Images/icons/unicommerce-icon.png';
import ProductInformation from './ProductInformation';
import VendorPrices from './VendorPrices';
import InventoryLedger from './InventoryLedger';
import ProductBOMList from './ProductBOMList';
import AvailableBatches from './AvailableBatches';
import BundledProducts from './BundledProducts';
import QCRule from './QCRule';
import RecentTransactions from '@Components/Inventory/ManageProducts/ViewProduct/RecentTransactions';
import UnicommerceIntegrationActions from '@Actions/integrations/unicommerceIntegrationActions';
import BatchReservations from '../BatchReservations';
import ActivityLog from '@Components/Common/ActivityLog';
import ActivityLogActions from '@Actions/activityLogActions';
import './style.scss';

const { TabPane } = Tabs;

/**
 *
 */
class ViewProduct extends Component {
  /**
   * Constructor
   *
   * @param props
   */
  constructor(props) {
    super(props);
    this.state = {
      currentTab: '/general-information',
      showUpdateProductModal: false,
      isCloneProduct: false,
    };
  }

  /**
   *
   */
  componentDidMount() {
    const {
      getProductById, user, match, location, getActivityLogSuccess,
    } = this.props;
    getActivityLogSuccess(null);
    getProductById(user?.tenant_info?.tenant_id, (match.params.productSkuId), Helpers.getTenantDepartmentId(user));
    const tab = new URLSearchParams(location.search).get('tab');
    if (tab) {
      this.setState({
        currentTab: tab || '/general-information',
      });
    }
  }

  /**
   *
   * @param {*} props
   * @param {*} state
   * @return
   */
  static getDerivedStateFromProps(props, state) {
    const tab = new URLSearchParams(props.location.search).get('tab');
    if (props.productSkuId && props.przModalView) {
      props.match.params.productSkuId = props.productSkuId;
    }
    return {
      ...state,
      currentTab: tab || '/general-information',
    };
  }

  /**
   *
   */
  componentWillUnmount() {
    const { getProductByIdSuccess } = this.props;
    getProductByIdSuccess(null);
  }

  /**
   *
   * @param {*} product
   * @returns
   */
  getProductAvailableQuantity(product) {
    const storeStock = product?.bundle_products?.map((item) => Number(item?.store_stock || 0) / Number(item?.bundle_quantity || 1)) || [];
    const otherStock = product?.bundle_products?.map((item) => Number((item?.total_available_qty - item?.store_stock) || 0) / Number(item?.bundle_quantity || 1)) || [];
    const departmentStock = product?.bundle_products?.map((item) => Number(item?.available_qty || 0) / Number(item?.bundle_quantity || 1)) || [];
    if (product?.bundle_products?.length) {
      return {
        store: Math.min(...storeStock) || 0,
        other: Math.min(...otherStock) || 0,
        department: Math.min(...departmentStock) || 0,
      };
    }
    return {
      store: 0,
      other: 0,
      department: 0,
    };
  }

  /**
   *
   * @return {*}
   */
  render() {
    const {
      history, selectedProduct, getProductByIdLoading, user, match, getProductById, syncShopifyInventory,
      syncShopifyInventoryLoading, adjustUnicommerceInventoryLoading, adjustUnicommerceInventory, przModalView,
    } = this.props;
    const {
      currentTab, showUpdateProductModal, showOtherDepartmentStock, isCloneProduct, uomType,
    } = this.state;
    const isSecondaryUomEnabled = user?.tenant_info?.global_config?.settings?.enable_secondary_uom;
    const inventoryConfig = user?.tenant_info?.inventory_config;
    const departmentLevelStock = inventoryConfig?.settings?.department_level_stock;
    const showStockToOtherDepartment = inventoryConfig?.settings?.show_stock_to_other_department;
    const uomInfo = selectedProduct?.uom_info;
    const uomList = selectedProduct?.uom_list;
    const value = selectedProduct?.store_stock || 0;

    const enableInternalSKUCode = inventoryConfig?.settings?.enable_internal_sku_code;
    const quantityInDiffUoms = (
      <div>
        {
          uomList?.map((uom, i) => (
            <div className="other-uom-stock" key={i}>
              <div>
                {QUANTITY((value * uomInfo?.ratio) / uom?.ratio, uom?.precision)}
              </div>
              <div>
                {uom?.uqc?.toProperCase()}
              </div>
            </div>
          ))
        }
      </div>
    );
    return (
      <Fragment>
        <div
          className="view-product__wrapper"
          style={{
            marginTop: przModalView ? '20px' : '85px',
          }}
        >
          <Drawer
            open={showUpdateProductModal}
            width="720px"
            mask
            onClose={() => this.setState({ showUpdateProductModal: false })}
            destroyOnClose
          >
            <div className="custom-drawer__header-wrapper">
              <div className="custom-drawer__header">
                <H3Text text={isCloneProduct ? 'Add New Product' : 'Update Product'} className="custom-drawer__title" />
                <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ showUpdateProductModal: false })} />
              </div>
            </div>
            <ProductForm
              callback={
                () => {
                  this.setState({ showUpdateProductModal: false });
                  getProductById(user?.tenant_info?.tenant_id, (match.params.productSkuId), Helpers.getTenantDepartmentId(user));
                }
              }
              selectedSkuProduct={selectedProduct}
              isCloneProduct={isCloneProduct}
            />
          </Drawer>
          {getProductByIdLoading ? (
            <div className="view-product__loader">
              <div className="view-product-header__loader">
                <div className="loadingBlock view-product-header__loader-left" />
                <div className="view-product-header__loader-right">
                  <div className="loadingBlock view-product-header__loader-right-top" />
                  <div className="view-product-header__loader-right-mid">
                    <div className="loadingBlock view-product-header__loader-right-mid-item" />
                    <div className="loadingBlock view-product-header__loader-right-mid-item" />
                  </div>
                  <div className="loadingBlock view-product-header__loader-right-bottom" />
                </div>
              </div>
              <div className="view-product-mid__loader">
                {[1, 2, 3, 4].map((item) => (
                  <div key={item} className="view-product-mid__loader-box">
                    <div className="loadingBlock view-product-mid__loader-box-left" />
                    <div className="view-product-mid__loader-box-right">
                      <div className="loadingBlock view-product-mid__loader-box-right-item" />
                      <div className="loadingBlock view-product-mid__loader-box-right-item" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <React.Fragment>
              <div className="view-product__header">
                <div className="view-product__header-left">
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      {selectedProduct?.product_info?.assets?.length > 0 ? (
                        <Image.PreviewGroup
                          items={selectedProduct?.product_info?.assets?.map((item) => item?.url)}
                        >
                          {selectedProduct?.product_info?.assets?.map((item, i) => (
                            i === 0 ? (
                              <Image
                                width={90}
                                height={90}
                                src={item?.url}
                                className="product__image"
                              />
                            ) : (
                              <Image
                                width={0}
                                height={0}
                                src={item?.url}
                                className="product__image product__image-none"
                              />
                            )
                          ))}
                        </Image.PreviewGroup>
                      ) : <img alt="product-default" className="product__default" src={defaultImage} />}
                      <div className="mobile-action-buttons" style={{ fontSize: '18px', fontWeight: '500' }}>
                        {`${enableInternalSKUCode ? `${selectedProduct?.product_info?.internal_sku_code} - ` : ''}`} {`${selectedProduct?.product_info?.product_sku_name}`}
                      </div>
                    </div>

                    {Helpers.getPermission(Helpers.permissionEntities.PRODUCT, Helpers.permissionTypes.UPDATE, user) && !przModalView && (
                      <div className="mobile-action-buttons">
                        <div className="action-buttons">
                          <div
                            className="action-button"
                            onClick={() => {
                              this.setState({
                                showUpdateProductModal: true,
                              });
                            }}
                          >
                            <EditOutlined />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                <div className="view-product__header-right">
                  <div className="view-product__header-right-heading">
                    <div style={{ minWidth: '350px' }}>
                      {`${enableInternalSKUCode ? `${selectedProduct?.product_info?.internal_sku_code} - ` : ''}`} {`${selectedProduct?.product_info?.product_sku_name}`}
                      &nbsp;
                    </div>
                    {Helpers.getPermission(Helpers.permissionEntities.PRODUCT, Helpers.permissionTypes.UPDATE, user) && !przModalView && (
                      <div className="action-buttons">
                        <div
                          className="action-button"
                          onClick={() => {
                            this.setState({
                              showUpdateProductModal: true,
                              isCloneProduct: false,
                            });
                          }}
                        >
                          <EditOutlined />
                        </div>
                      </div>
                    )}
                    {Helpers.getPermission(Helpers.permissionEntities.PRODUCT, Helpers.permissionTypes.UPDATE, user) && !przModalView && (
                      <Tooltip
                        placement="topLeft"
                        title="Make a Copy"
                      >
                        <div className="action-buttons">
                          <div
                            className="action-button"
                            onClick={() => {
                              this.setState({
                                showUpdateProductModal: true,
                                isCloneProduct: true,
                              });
                            }}
                          >
                            <CopyOutlined />
                          </div>
                        </div>
                      </Tooltip>
                    )}
                    {!selectedProduct?.is_active && <H3Text text="INACTIVE" className="inactive-product-flag inactive-chip" />}
                  </div>
                  <div className="view-product__header-right-sub-heading__wrapper">
                    {selectedProduct?.is_sales_product && (
                      <div className="view-product__header-right-sub-heading">
                        <img src={tick} alt="tick" />
                        <div className="view-product__header-right-sub-heading-text">
                          Enabled for Sales
                        </div>
                      </div>
                    )}
                    {selectedProduct?.is_purchase_product && (
                      <div className="view-product__header-right-sub-heading">
                        <img src={tick} alt="tick" />
                        <div className="view-product__header-right-sub-heading-text">
                          Enabled for Purchase
                        </div>
                      </div>
                    )}
                    {Helpers.getPermission(Helpers.permissionEntities.PURCHASE_ORDER, Helpers.permissionTypes.CREATE, user) && (
                      <RecentTransactions
                        tenantId={user?.tenant_info?.tenant_id}
                        productSkuId={selectedProduct?.product_sku_id}
                        internalSkuCode={selectedProduct?.internal_sku_code}
                        refProductCode={selectedProduct?.ref_product_code}
                        productSkuName={selectedProduct?.product_info?.product_sku_name}
                        fullTextButton
                      />
                    )}

                  </div>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div className="view-product__header-right-sub-heading-tag" style={{ background: Helpers.getProductType(selectedProduct?.product_type).color }}>
                      {Helpers.getProductType(selectedProduct?.product_type).text}
                    </div>
                    {(selectedProduct?.is_marketplace_product) && (
                      <div className="view-product__header-right-sub-heading-tag" style={{ backgroundColor: '#f58428', marginLeft: '5px' }}>
                        Marketplace
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div>
                {isSecondaryUomEnabled && (
                  <div className="view-product__secondary-uom-heading">
                    Based on Primary UOM
                  </div>
                )}
                <div
                  className="view-product__info"
                  style={{
                    marginTop: isSecondaryUomEnabled ? '4px' : '20px',
                  }}
                >
                  {(Helpers.getTenantDepartmentId(user) === user?.tenant_info?.default_store_id
                    || (showStockToOtherDepartment && Helpers.getTenantDepartmentId(user) !== user?.tenant_info?.default_store_id)
                  ) && (
                    <div className="view-product__box">
                      <div className="view-product__box-left">
                        <img src={box} alt="exchange" />
                      </div>
                      <div className="view-product__box-right">
                        <Popover placement="bottom" content={quantityInDiffUoms} title="Quantity in other units" trigger="hover">
                          <div className="view-product__box-right-heading">
                            {selectedProduct?.product_type === 'BUNDLE'
                              ? QUANTITY(parseInt(this.getProductAvailableQuantity(selectedProduct)?.store, 10) || '0', selectedProduct?.uom_info?.precision)
                              : QUANTITY(selectedProduct?.store_stock || '0', selectedProduct?.uom_info?.precision)}
                            {' '}
                            {selectedProduct?.uom_info?.uqc?.toProperCase() || ''}
                          </div>
                        </Popover>
                        <div className="view-product__box-right-sub-heading">
                          Stock in Default Department
                        </div>
                      </div>
                    </div>
                  )}
                  {Number(selectedProduct?.pending_in_qc_qty) > 0 && (
                    <div className="view-product__box">
                      <div className="view-product__box-left">
                        {/* <img src={exchange} alt="exchange" /> */}
                        <SearchOutlined />
                      </div>
                      <div className="view-product__box-right">
                        <div className="view-product__box-right-heading">
                          {QUANTITY(selectedProduct?.pending_in_qc_qty || '0', selectedProduct?.uom_info?.precision)}
                          &nbsp;
                          {selectedProduct?.uom_info?.uqc?.toProperCase() || ''}
                        </div>
                        <div className="view-product__box-right-sub-heading">
                          Pending Quality Check
                        </div>
                      </div>
                    </div>
                  )}
                  {(Helpers.getTenantDepartmentId(user) === user?.tenant_info?.default_store_id
                    || (showStockToOtherDepartment && Helpers.getTenantDepartmentId(user) !== user?.tenant_info?.default_store_id)
                  ) && selectedProduct?.product_type !== 'BUNDLE' && (
                    <div className="view-product__box">
                      <div className="view-product__box-left">
                        <img src={box} alt="exchange" />
                      </div>
                      <div className="view-product__box-right">
                        <div className="view-product__box-right-heading">
                          {QUANTITY(selectedProduct?.store_rejected_stock || '0', selectedProduct?.uom_info?.precision)}
                          {' '}
                          {selectedProduct?.uom_info?.uqc?.toProperCase() || ''}
                        </div>
                        <div className="view-product__box-right-sub-heading">
                          Rejected Stock
                        </div>
                      </div>
                    </div>
                  )}
                  {departmentLevelStock && Helpers.getTenantDepartmentId(user) !== user?.tenant_info?.default_store_id && (
                    <div className="view-product__box">
                      <div className="view-product__box-left">
                        <img src={box} alt="exchange" />
                      </div>
                      <div className="view-product__box-right">
                        <div className="view-product__box-right-heading">
                          {selectedProduct?.product_type === 'BUNDLE'
                            ? QUANTITY(this.getProductAvailableQuantity(selectedProduct)?.department || '0', selectedProduct?.uom_info?.precision)
                            : QUANTITY((selectedProduct?.available_qty) || '0', selectedProduct?.uom_info?.precision)}
                          {' '}
                          {selectedProduct?.uom_info?.uqc?.toProperCase() || ''}
                        </div>
                        <div className="view-product__box-right-sub-heading">
                          Stock in
                          {' '}
                          {Helpers.getTenantDepartment(user)}
                        </div>
                      </div>
                    </div>
                  )}
                  {Helpers.getTenantDepartmentId(user) === user?.tenant_info?.default_store_id && (
                    <div className="view-product__box" onClick={() => this.setState({ showOtherDepartmentStock: true, uomType: 'primary' })} style={{ cursor: 'pointer' }}>
                      <div className="view-product__box-left">
                        <img src={box} alt="exchange" />
                      </div>
                      <div className="view-product__box-right">
                        <div className="view-product__box-right-heading">
                          {selectedProduct?.product_type === 'BUNDLE'
                            ? QUANTITY(this.getProductAvailableQuantity(selectedProduct)?.other || '0', selectedProduct?.uom_info?.precision)
                            : QUANTITY((selectedProduct?.total_available_qty - selectedProduct?.store_stock) || '0', selectedProduct?.uom_info?.precision)}
                          {' '}
                          {selectedProduct?.uom_info?.uqc?.toProperCase() || ''}
                        </div>
                        <div className="view-product__box-right-sub-heading">
                          In Other Departments
                        </div>
                      </div>
                    </div>
                  )}
                  {(Helpers.getTenantDepartmentId(user) === user?.tenant_info?.default_store_id
                    || (showStockToOtherDepartment && Helpers.getTenantDepartmentId(user) !== user?.tenant_info?.default_store_id)
                  ) && selectedProduct?.product_type !== 'BUNDLE' && (
                    <div className="view-product__box">
                      <div className="view-product__box-left">
                        <img src={exchange} alt="exchange" />
                      </div>
                      <div className="view-product__box-right">
                        <div className="view-product__box-right-heading">
                          {QUANTITY(selectedProduct?.total_reserved_qty_in_store || '0', selectedProduct?.uom_info?.precision)}
                            &nbsp;
                          {selectedProduct?.uom_info?.uqc?.toProperCase() || ''}
                        </div>
                        <div className="view-product__box-right-sub-heading">
                          Reserved
                        </div>
                      </div>
                    </div>
                  )}
                  {(Helpers.getTenantDepartmentId(user) === user?.tenant_info?.default_store_id
                    || (showStockToOtherDepartment && Helpers.getTenantDepartmentId(user) !== user?.tenant_info?.default_store_id)
                  ) && selectedProduct?.product_type !== 'BUNDLE' && (
                    <div className="view-product__box">
                      <div className="view-product__box-left">
                        <img src={exchange} alt="exchange" />
                      </div>
                      <div className="view-product__box-right">
                        <div className="view-product__box-right-heading">
                          {QUANTITY(selectedProduct?.total_reserved_qty_in_other_depts || '0', selectedProduct?.uom_info?.precision)}
                            &nbsp;
                          {selectedProduct?.uom_info?.uqc?.toProperCase() || ''}
                        </div>
                        <div className="view-product__box-right-sub-heading">
                          Reserved in Other Departments
                        </div>
                      </div>
                    </div>
                  )}
                  {Helpers.getTenantDepartmentId(user) === user?.tenant_info?.default_store_id && selectedProduct?.product_info?.shopify_variant_id && (
                    <Popconfirm
                      placement="topRight"
                      title={(
                        <div style={{ width: '300px' }}>
                          Last sync&nbsp;
                          {toISTDate(selectedProduct?.product_info?.shopify_updated_at).format('DD/MM/YYYY hh:mm A')}
                          <br />
                          Stock of your default store will be set as Available Quantity in Shopify.
                        </div>
                      )}
                      onConfirm={() => {
                        syncShopifyInventory(
                          { tenant_product_id: selectedProduct?.tenant_product_id },
                          () => getProductById(user?.tenant_info?.tenant_id, match.params.productSkuId, Helpers.getTenantDepartmentId(user)),
                        );
                      }}
                      okText="Sync again"
                      okButtonProps={{
                        loading: syncShopifyInventoryLoading,
                      }}
                      cancelText="Cancel"
                    >
                      <div className="view-product__box2" style={{ cursor: 'pointer' }}>
                        <div className="view-product__box-top">
                          {syncShopifyInventoryLoading ? <LoadingOutlined /> : <img src={ShopifyIcon} alt="exchange" />}
                        </div>
                        <div className="view-product__box-bottom">
                          <div className="view-product__box-right-sub-heading">
                            Sync
                          </div>
                        </div>
                      </div>
                    </Popconfirm>
                  )}

                  {Helpers.getTenantDepartmentId(user) === user?.tenant_info?.default_store_id && selectedProduct?.product_info?.unicommerce_sku_code && (
                    <Popconfirm
                      placement="topRight"
                      title={(
                        <div style={{ width: '300px' }}>
                          {selectedProduct?.unicommerce_pushed_at && (
                            <Fragment>
                              Last pushed at&nbsp;
                              {toISTDate(selectedProduct?.unicommerce_pushed_at).format('DD/MM/YYYY hh:mm A')}
                              <br />
                            </Fragment>
                          )}
                          Stock of your default store will be set as available quantity in Unicommerce.
                        </div>
                      )}
                      onConfirm={() => {
                        adjustUnicommerceInventory(
                          [{
                            tenant_product_id: selectedProduct?.tenant_product_id,
                            unicommerce_sku_code: selectedProduct?.product_info?.unicommerce_sku_code,
                            quantity: selectedProduct?.product_type === 'BUNDLE'
                              ? parseInt(this.getProductAvailableQuantity(selectedProduct)?.store || 0, 10)
                              : (selectedProduct?.store_stock || 0),
                            inventory_type: 'GOOD_INVENTORY',
                            adjustment_type: 'REPLACE',
                            facility_code: user?.tenant_info?.unicommerce_facility_code,
                          }],
                          () => getProductById(user?.tenant_info?.tenant_id, match.params.productSkuId, Helpers.getTenantDepartmentId(user)),
                        );
                      }}
                      okText="Sync again"
                      okButtonProps={{
                        loading: adjustUnicommerceInventoryLoading,
                      }}
                      cancelText="Cancel"
                    >
                      <div className="view-product__box3" style={{ cursor: 'pointer' }}>
                        <div className="view-product__box-top">
                          {adjustUnicommerceInventoryLoading ? <LoadingOutlined /> : <img src={ucIcon} alt="exchange" />}
                        </div>
                        <div className="view-product__box-bottom">
                          <div className="view-product__box-right-sub-heading">
                            Sync
                          </div>
                        </div>
                      </div>
                    </Popconfirm>
                  )}
                </div>
              </div>

              {isSecondaryUomEnabled && selectedProduct?.secondary_uom_info?.uqc &&  (
                <div>
                  <div className="view-product__secondary-uom-heading">
                    Based on Secondary UOM
                  </div>
                  <div
                    className="view-product__info"
                    style={{
                      marginTop: isSecondaryUomEnabled ? '4px' : '20px',
                    }}
                  >
                    {(Helpers.getTenantDepartmentId(user) === user?.tenant_info?.default_store_id
                      || (showStockToOtherDepartment && Helpers.getTenantDepartmentId(user) !== user?.tenant_info?.default_store_id)
                    ) && (
                      <div className="view-product__box">
                        <div className="view-product__box-left">
                          <img src={box} alt="exchange" />
                        </div>
                        <div className="view-product__box-right">
                          <div className="view-product__box-right-heading">
                            {QUANTITY(selectedProduct?.secondary_all_department_quantities[user?.tenant_info?.default_store_id] || '0', selectedProduct?.secondary_uom_info?.precision)}
                            {' '}
                            {selectedProduct?.secondary_uom_info?.uqc?.toProperCase() || ''}
                          </div>
                          <div className="view-product__box-right-sub-heading">
                            Stock in Default Department
                          </div>
                        </div>
                      </div>
                    )}
                    {Helpers.getTenantDepartmentId(user) === user?.tenant_info?.default_store_id && (
                      <div className="view-product__box" onClick={() => this.setState({ showOtherDepartmentStock: true, uomType: 'secondary' })} style={{ cursor: 'pointer' }}>
                        <div className="view-product__box-left">
                          <img src={box} alt="exchange" />
                        </div>
                        <div className="view-product__box-right">
                          <div className="view-product__box-right-heading">
                            {QUANTITY((Number(selectedProduct?.total_secondary_available_qty) - Number(selectedProduct?.secondary_all_department_quantities[user?.tenant_info?.default_store_id])) || '0', selectedProduct?.secondary_uom_info?.precision)}
                            {' '}
                            {selectedProduct?.secondary_uom_info?.uqc?.toProperCase() || ''}

                          </div>
                          <div className="view-product__box-right-sub-heading">
                            In Other Departments
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}

              <div className="view-product__tables">
                <Tabs
                  activeKey={currentTab}
                  onChange={(key) => {
                    this.setState({ currentTab: key });
                    history.push(`?tab=${key}`);
                  }}
                  mode="horizontal"
                >
                  <TabPane tab="General Information" key="/general-information" />
                  {selectedProduct?.product_type === 'BUNDLE' && <TabPane tab="Components" key="/bundle-components" />}
                  {selectedProduct?.product_type !== 'BUNDLE' && Helpers.getPermission(Helpers.permissionEntities.VENDOR_PRICE,
                    Helpers.permissionTypes.READ, user) && <TabPane tab="Vendor Prices" key="/vendor-prices" />}
                  {selectedProduct?.product_type !== 'BUNDLE' && <TabPane tab="Available Batches" key="/available-batches" />}
                  {<TabPane tab="Reserved Batches" key="/reserved-batches" />}
                  {selectedProduct?.product_type !== 'BUNDLE' && <TabPane tab="Inventory Ledger" key="/inventory-ledger" />}
                  {selectedProduct?.product_type !== 'BUNDLE' && user?.tenant_info?.production_config?.sub_modules?.bill_of_material?.is_active && Helpers.getPermission(Helpers.permissionEntities.BOM,
                    Helpers.permissionTypes.READ, user) && selectedProduct?.product_type !== 'SERVICE' && <TabPane tab="Bill of Materials" key="/bill-of-material" />}
                  {selectedProduct?.product_type !== 'BUNDLE' && user?.tenant_info?.quality_control_config?.sub_modules?.quality_control_rule?.is_active && Helpers.getPermission(Helpers.permissionEntities.QUALITY_CHECKS,
                    Helpers.permissionTypes.READ, user) && selectedProduct?.product_type !== 'SERVICE' && <TabPane tab="Quality Check" key="/product-quality-rules" />}
                  {<TabPane tab="Activity Log" key="/activity-log" />}
                </Tabs>
                {currentTab === '/general-information' && (
                  <ProductInformation
                    selectedProduct={selectedProduct}
                    przModalView={przModalView}
                    callback={() => {
                      getProductById(user?.tenant_info?.tenant_id, match.params.productSkuId, Helpers.getTenantDepartmentId(user));
                    }}
                  />
                )}
                {currentTab === '/vendor-prices' && <VendorPrices />}
                {/* {currentTab === 3 && <ProductBOM />} */}
                {currentTab === '/bill-of-material' && <ProductBOMList />}
                {currentTab === '/inventory-ledger' && <InventoryLedger />}
                {currentTab === '/available-batches' && <AvailableBatches expiryDays={selectedProduct?.product_info?.expiry_days} />}
                {currentTab === '/reserved-batches' && (
                  <BatchReservations
                    selectedSkuProduct={selectedProduct}
                    expiryDays={selectedProduct?.product_info?.expiry_days}
                    isViewProduct
                  />
                )}
                {currentTab === '/bundle-components' && (
                  <BundledProducts
                    callback={() => {
                      getProductById(user?.tenant_info?.tenant_id, match.params.productSkuId, Helpers.getTenantDepartmentId(user));
                    }}
                  />
                )}
                {currentTab === '/product-quality-rules' && (
                  <QCRule
                    callback={() => {
                      getProductById(user?.tenant_info?.tenant_id, match.params.productSkuId, Helpers.getTenantDepartmentId(user));
                    }}
                  />
                )}
                {currentTab === '/activity-log' && (
                  <ActivityLog
                    entityId={selectedProduct?.product_sku_id}
                    entityType="product"
                    entityName="Product"
                    subEntityId={selectedProduct?.tenant_product_id}
                    disableComments
                  />
                )}
              </div>
            </React.Fragment>
          )}

        </div>
        <Drawer
          placement="right"
          onClose={() => this.setState({ showOtherDepartmentStock: false, uomType: '' })}
          open={showOtherDepartmentStock}
          width="360px"
          destroyOnClose
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header" style={{ width: '310px' }}>
              <H3Text text="Stock in Other Departments" className="custom-drawer__title" />
              <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ showOtherDepartmentStock: false, uomType: '' })} />
            </div>
          </div>
          <OtherDepartmentsStock selectedProductSkuId={selectedProduct?.product_info?.product_sku_id} tenantId={user?.tenant_info?.tenant_id} uomType={uomType} />
        </Drawer>
      </Fragment>
    );
  }
}

const mapStateToProps = ({
  UserReducers, ProductReducers, ShopifyIntegrationReducers, UnicommerceIntegrationReducers,
}) => ({
  user: UserReducers.user,
  selectedProduct: ProductReducers.selectedProduct,
  getProductByIdLoading: ProductReducers.getProductByIdLoading,
  syncShopifyInventoryLoading: ShopifyIntegrationReducers.syncShopifyInventoryLoading,
  adjustUnicommerceInventoryLoading: UnicommerceIntegrationReducers.adjustUnicommerceInventoryLoading,
});

const mapDispatchToProps = (dispatch) => ({
  getProductById: (tenantId, productSkuId, tenantDepartmentId) => dispatch(ProductActions.getProductById(tenantId, productSkuId, tenantDepartmentId)),
  adjustUnicommerceInventory: (payload, callback) => dispatch(UnicommerceIntegrationActions.adjustUnicommerceInventory(payload, callback)),
  getProductByIdSuccess: (selectedProduct) => dispatch(ProductActions.getProductByIdSuccess(selectedProduct)),
  syncShopifyInventory: (payload, callback) => dispatch(ShopifyIntegrationActions.syncShopifyInventory(payload, callback)),
  getActivityLogSuccess: (activityLog) => dispatch(ActivityLogActions.getActivityLogSuccess(activityLog)),
});

ViewProduct.propTypes = {
  user: PropTypes.any,
  getProductById: PropTypes.func,
  match: PropTypes.func,
  history: PropTypes.func,
  location: PropTypes.func,
  selectedProduct: PropTypes.any,
  getProductByIdLoading: PropTypes.any,
  getProductByIdSuccess: PropTypes.func,
  syncShopifyInventory: PropTypes.func,
  syncShopifyInventoryLoading: PropTypes.bool,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(ViewProduct));
