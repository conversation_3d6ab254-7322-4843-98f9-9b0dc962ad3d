/* eslint-disable no-nested-ternary */
/* eslint-disable new-cap */
import React, { Component, Fragment } from 'react';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import {
  DatePicker, Table, Progress,
} from 'antd';
import {
  INFINITE_EXPIRY_DATE, QUANTITY,
} from '@Apis/constants';
import H3FormInput from '@Uilib/h3FormInput';
import dayjs from 'dayjs';
import H3Text from '@Uilib/h3Text';
import ProductCategoryLabel from '../../../../../../Common/ProductCategoryLabel';
import CustomFieldLine from '@Components/Common/CustomFieldLine';
import CustomFieldHelpers from '../../../../../../../helpers/CustomFieldHelpers';
import PRZModal from '../../../../../../Common/UI/PRZModal';
import ProductCodesAndName from '@Components/Common/ProductCodesAndName';
import './style.scss';
import BatchCustomFields from '@Components/Common/BatchCustomFields';
import WarehouseLocationSelector from '@Components/Common/Selector/WarehouseLocationSelector';

/**
 *
 */
class ByProduct extends Component {
  /**
   *
   * @param props
   */
  constructor(props) {
    super(props);
    this.state = {
      columns: [
        {
          title: 'Product',
          render: (item) => {
            const enableInternalRefCode = this.props?.user?.tenant_info?.inventory_config?.settings?.enable_internal_ref_code;
            const enableInternalSKUCode = this.props?.user?.tenant_info?.inventory_config?.settings?.enable_internal_sku_code;
            return (
              <Fragment>
                <div onClick={() => this.setState({ showPRZModal: true, selectedDocumentId: item?.tenant_product_info?.product_sku_id })}>
                  <ProductCodesAndName
                    skuCode={item?.tenant_product_info?.internal_sku_code}
                    refCode={item?.tenant_product_info?.ref_product_code}
                    name={item?.tenant_product_info?.product_sku_name}
                    showSku={enableInternalSKUCode}
                    showRefCode={enableInternalRefCode}
                  />
                  {item?.product_category_info?.category_path?.length > 0 && (
                    <ProductCategoryLabel
                      categoryPath={item?.product_category_info?.category_path}
                      categoryName={item?.product_category_info?.category_path?.at(-1)}
                      containerStyle={{
                        width: 'fit-content',
                      }}
                    />
                  )}
                </div>
              </Fragment>
            );
          },
        },
        {
          title: 'Completion',
          width: '100px',
          render: (record) => (
            <div style={{ width: '90px' }}>
              <Progress size="small" percent={parseInt((record?.completed_quantity / record?.quantity) * 100)} strokeColor={{ from: '#108ee9', to: '#87d068' }} trailColor="#2d7df71a" />
            </div>
          ),
        },
        {
          title: 'Pending',
          width: '65px',
          render: (record) => `${QUANTITY(record?.quantity - record?.completed_quantity, record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase()}`,
        },
        {
          title: 'Quantity',
          width: '90px',
          render: (item) => {
            const { createMOAdjustmentLoading, handleTableChange } = this.props;
            const { formSubmitted } = this.state;
            return (
              <Fragment>
                <H3FormInput
                  value={item.batch_quantity}
                  onChange={(event) => handleTableChange(item.key, 'batch_quantity', event.target.value)}
                  type="number"
                  name=" produced quantity"
                  labelClassName="add-batch__label"
                  disabled={createMOAdjustmentLoading}
                  inputClassName="orgFormInput fg-batch-input"
                  required
                  showError={formSubmitted && !Number(item.batch_quantity)}
                />
                {item?.batch_quantity < 0 && <H3Text text="*Please enter a valid quantity" className="input-error" />}
              </Fragment>
            );
          },
        },
        {
          title: 'Batch#',
          width: '165px',
          render: (item) => {
            const { createMOAdjustmentLoading, handleTableChange, user } = this.props;
            const { formSubmitted, batchNumber } = this.state;
            const isBatchNumberEditable = user?.tenant_info?.inventory_config?.settings?.batch_management?.is_batch_number_editable;
            return (
              <H3FormInput
                value={item?.batch_number}
                onChange={(event) => handleTableChange(item.key, 'batch_number', event.target.value)}
                type="text"
                name=" produced quantity"
                labelClassName="add-batch__label"
                disabled={createMOAdjustmentLoading || !isBatchNumberEditable}
                inputClassName="orgFormInput fg-batch-input"
                required
                showError={formSubmitted && !batchNumber}
              />

            );
          },
        },
        {
          title: 'Shelf/Rack',
          width: '130px',
          render: (item) => (
            <WarehouseLocationSelector
              containerClassName="warehouse-location-selector"
              hideTitle
              selectedInventoryLocation={item?.inventoryLocationInfo?.inventory_location_path}
              onChange={(id, name) => props?.handleTableChange(item.key, 'inventoryLocationInfo', { inventory_location_id: id, inventory_location_path: name })}
              destDepartmentId={props?.selectedMO?.tenant_department_id}
            />
          ),
        },
        {
          title: 'Lot#',
          width: '110px',
          render: (item) => {
            const { createMOAdjustmentLoading, handleTableChange } = this.props;
            return (
              <H3FormInput
                value={item?.lot_number}
                onChange={(event) => handleTableChange(item.key, 'lot_number', event.target.value)}
                type="text"
                name=" produced quantity"
                labelClassName="add-batch__label"
                disabled={createMOAdjustmentLoading}
                inputClassName="orgFormInput fg-batch-input"
                required
              />
            );
          },
        },
        {
          title: 'Expiry',
          width: '170px',
          render: (item) => {
            const { createMOAdjustmentLoading, handleTableChange } = this.props;
            return (
              <div className="fg-batch-input">
                {dayjs(item?.expiry_date).format('YYYY-MM-DD') !== INFINITE_EXPIRY_DATE ? (
                  <DatePicker
                    value={dayjs(item?.expiry_date)}
                    onChange={(value) => handleTableChange(item.key, 'expiry_date', value)}
                    disabled={createMOAdjustmentLoading}
                    style={{
                      height: '28px',
                      border: '1px solid rgba(68, 130, 218, 0.2)',
                      borderRadius: '3px',
                      padding: '1px 10px',
                      width: '100%',
                      background: 'white',
                    }}
                    disabledDate={(current) => current.isBefore(dayjs().subtract(1, 'day'))}
                  />
                ) : '-'}
              </div>

            );
          },
        },
      ],
    };
  }

  /**
   *
   */
  componentWillUnmount() {
  }

  getColumns () {
    const { columns, } = this.state;

    const copyColumns = columns;
    const batchCustomFields = CustomFieldHelpers.getCfStructure(cfV2DocManufacturingOrder?.data?.batch_custom_fields, true);

    batchCustomFields?.map((field) => {

      copyColumns.push({

        title: field?.fieldName,
        width: '150px',
        render: (item) => {

          const { handleTableChangeCf, formSubmitted, } = this.props;

          return (
            <CustomFieldLine
              customFields={item?.custom_fields}
              cfId={field?.cfId}
              labelClassName="orgFormLabel"
              inputClassName="orgFormInput"
              formSubmitted={formSubmitted}
              customInputChange={(value, cfId) => handleTableChangeCf(value, cfId, item?.by_product_id)}
              isHorizontalUi
              hideTitle
            />
        );
      }
    });

    return copyColumns;
  }

  /**
  *
  * @return {JSX.Element}
  */
  render() {
    const {
      data, getInventoryLocationsLoading,
    } = this.props;
    const {
      columns, showPRZModal, selectedDocumentId
    } = this.state;

    return (
      <div className="ant-row">
        <div className="ant-col-md-24">
          <div className="rm-table-wrapper">
            <Table
              title={() => `By Products (${data?.length})`}
              columns={this.getColumns()}
              dataSource={data || []}
              size="small"
              scroll="scroll"
              pagination={false}
              loading={getInventoryLocationsLoading}
            />
          </div>
        </div>
        <PRZModal
          isOpen={showPRZModal}
          onClose={() => {
            this.setState({ showPRZModal: false, selectedDocumentId: null });
          }}
          entityType="product"
          documentId={selectedDocumentId}
        />
      </div>
    )}
}

const mapStateToProps = ({
  UserReducers, MOReducers, CFV2Reducers,
}) => ({
  user: UserReducers.user,
  createMOAdjustmentLoading: MOReducers.createMOAdjustmentLoading,
  selectedMO: MOReducers.selectedMO,
  cfV2DocManufacturingOrder: CFV2Reducers.cfV2DocManufacturingOrder,
});

const mapDispatchToProps = () => ({

});

ByProduct.propTypes = {
  data: PropTypes.any,
  user: PropTypes.any,
  selectedMO: PropTypes.any,
  createMOAdjustmentLoading: PropTypes.bool,
  callback: PropTypes.any,
  isFlexible: PropTypes.bool,
  handleTableChange: PropTypes.func,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(ByProduct));
