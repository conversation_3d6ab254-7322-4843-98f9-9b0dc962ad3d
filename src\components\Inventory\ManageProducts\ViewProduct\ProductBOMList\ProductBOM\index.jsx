/* eslint-disable react/sort-comp */
import React, { Component, Fragment } from 'react';
import { connect } from 'react-redux';
import { Link, withRouter } from 'react-router-dom';
import {
  Table, Badge, Tabs, Dropdown, Menu, Image, Avatar, Tooltip, Popconfirm, Drawer, Popover,
  Select,
} from 'antd';
import { v4 as uuidv4 } from 'uuid';
import {
  faPrint, faLock, faUnlock, faTrashCan,
} from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { CopyOutlined, EditOutlined } from '@ant-design/icons';
import H3Text from '@Uilib/h3Text';
import BOMActions from '@Actions/bomActions';
import Constants, { QUANTITY } from '@Apis/constants';
import Helpers from '@Apis/helpers';
import ViewCustomFields from '@Components/Common/CustomField/ViewCustomFields';
import TemplateRouteActions from '@Actions/production/templateRouteActions';
import Attachment from '@Components/Common/Attachment';
import AttachmentActions from '@Actions/attachmentActions';
import AnalyticsActions from '@Actions/application/analyticsActions';
import closeIcon from '@Images/icons/icon-close-blue.png';
import TemplateRouteLines from '@Components/Inventory/ManageProducts/BOM/FormBOM/TemplateRouteLines';
import H3FormInput from '@Uilib/h3FormInput';
import H3Image from '@Uilib/h3Image';
import FormBOM from '@Components/Inventory/ManageProducts/BOM/FormBOM';
import defaultImage from '@Images/icons/imageDefault.png';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import CustomDocumentColumns from '@Components/Common/CustomDocumentColumns';
import TemplateRouteLinesV2 from './TemplateRouteLinesV2';
import { getTotalRmUsage } from './helpers';
import ProductCategoryLabel from '@Components/Common/ProductCategoryLabel';
import HideValue from '../../../../../Common/RestrictedAccess/HideValue';
import HideComponent from '../../../../../Common/RestrictedAccess/HideComponent';
import PRZModal from '../../../../../Common/UI/PRZModal';
import './style.scss';
import PRZSelect from '../../../../../Common/UI/PRZSelect';
import ProductCodesAndName from '@Components/Common/ProductCodesAndName';

const { TabPane } = Tabs;

class ProductBOM extends Component {
  constructor(props) {
    super(props);
    this.state = {
      currentTab: '/raw-materials',
      templateRouteId: '',
      quantity: '',
      visibleColumns: {
        COMPONENT: {
          label: 'Component',
          visible: true,
          disabled: true,
        },
        ON_HAND: {
          label: 'On Hand',
          visible: true,
          disabled: true,
        },
        AVAILABILITY: {
          label: 'Availability',
          visible: true,
          disabled: true,
        },
        CONSUMPTION: {
          label: 'Consumption',
          visible: true,
          disabled: true,
        },
        PRODUCTION: {
          label: 'Production',
          visible: true,
          disabled: true,
        },
        WASTAGE: {
          label: 'Overage %',
          visible: true,
          disabled: true,
        },
        COST_PER_UNIT: {
          label: 'Cost Per Unit',
          visible: true,
          disabled: true,
        },
        COST: {
          label: 'Cost',
          visible: true,
          disabled: true,
        },
      },
      isUserReady: false,
      costingMethod: 'NORMAL',
    };
  }

  getColumns(isChild) {
    const {
      selectedBom, visibleColumns, cfBillOfMaterialLine, currentTab, costingMethod,
    } = this.state;
    const { user, MONEY, priceMasking } = this.props;
    const byProductLines = selectedBom?.bom_by_products?.[0]?.bom_by_product_id && currentTab === '/by-products';
    const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;
    const enableInternalRefCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_ref_code;
    const enableInternalSKUCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_sku_code;
    const columns = [
      {
        title: '',
        responsive: ['xs'],
        render: (record) => {
          const { quantity, originalQuantity } = this.state;
          const perUnitCost = (record?.tenant_product_info?.cost_price / record?.product_info?.uom_info?.ratio) * record?.uom_info?.ratio;
          return (
            <div className="mobile-list__item">
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>

                <Link to={`/inventory/product/view/${record?.tenant_product_info?.product_sku_id}`} target="_blank">
                  <H3Text text={record?.product_info?.product_sku_name} className="prod-table__text" />
                </Link>

                <div className="status">
                  <div className={((record?.on_hand_quantity * record?.product_info?.uom_info?.ratio) / record?.uom_info?.ratio) - (Number(quantity / originalQuantity || 0) * record?.quantity)?.toFixed(record?.uom_info?.precision) >= 0 ? 'active-chip' : 'inactive-chip'}>
                    {((record?.on_hand_quantity * record?.product_info?.uom_info?.ratio) / record?.uom_info?.ratio) - (Number(quantity / originalQuantity || 0) * record?.quantity)?.toFixed(record?.uom_info?.precision) >= 0 ? 'Available' : 'Not Available'}
                  </div>
                </div>
              </div>
              <div style={{ whiteSpace: 'nowrap' }}>
                {`QUANTITY: ${QUANTITY((Number(quantity / originalQuantity || 0) * record?.quantity), record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase() || ''}`}
              </div>
              <div style={{ display: 'flex', gap: '2%' }}>
                <div>
                  {'ON HAND: '}
                </div>
                <div style={{ whiteSpace: 'nowrap' }}>
                  {`${QUANTITY(((record?.on_hand_quantity * record?.product_info?.uom_info?.ratio) / record?.uom_info?.ratio) || '0', record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase() || ''}`}
                </div>
              </div>
              <div style={{ display: 'flex', gap: '2%' }}>
                <div>
                  {'BOM COST/UNIT: '}
                </div>
                <div>
                  {`${MONEY((record?.tenant_product_info?.cost_price / record?.product_info?.uom_info?.ratio) * record?.uom_info?.ratio)} / ${record?.uom_info?.uqc?.toProperCase()}`}
                </div>
              </div>
              <div style={{ display: 'flex', gap: '2%' }}>
                <div>
                  {'BOM COST: '}
                </div>
                <div>
                  {MONEY((quantity / originalQuantity || 0) * record?.quantity * perUnitCost)}
                </div>
              </div>

            </div>
          );
        },
      },
      {
        title: '',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        width: '50px',
        className: 'prod-table__image-column',
        fixed: 'left',
        render: (record) => (
          <div style={{ width: '40px' }}>
            {record?.product_info?.assets?.length > 0 ? (
              <Image.PreviewGroup
                items={record?.product_info?.assets?.map((item) => item?.url)}
              >
                {record?.product_info?.assets?.map((item, i) => (
                  i === 0 ? (
                    <Image
                      width={40}
                      height={40}
                      src={item?.url}
                      className="product__image"
                    />
                  ) : (
                    <Image
                      width={0}
                      height={0}
                      src={item?.url}
                      className="product__image product__image-none"
                    />
                  )
                ))}
              </Image.PreviewGroup>
            ) : <img className="prod-table__image__default" src={defaultImage} />}
          </div>
        ),
        visible: true,
      },
      {
        title: 'Component',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        width: '240px',
        fixed: 'left',
        render: (text, record) => (
          <div style={{ width: isChild ? '440px' : '250px' }}>
            <div className="flex-display">
              <div onClick={() => this.setState({ showPRZModal: true, selectedDocumentId: record?.product_info?.product_sku_id })}>
                <ProductCodesAndName
                  skuCode={record?.product_info?.internal_sku_code}
                  refCode={record?.product_info?.ref_product_code}
                  name={record?.product_info?.product_sku_name}
                  showSku={enableInternalSKUCode}
                  showRefCode={enableInternalRefCode}
                />
              </div>
              <div
                className="status-tag"
                style={{
                  backgroundColor: record?.child_bom_id ? 'rgba(50,180,13,0.2)' : 'rgba(199,199,199,0.5)', marginLeft: '5px', color: '#3f3f3f', padding: '0px 3px', height: '16px',
                }}
              >
                {record?.child_bom_id ? 'SFG' : 'RM'}
              </div>
            </div>
            {enableInternalRefCode && record?.product_info?.ref_product_code && (
              <Tooltip title={record?.product_info?.ref_product_code}>
                <div className="prod-table__text" style={{ display: 'inline-block', maxWidth: '200px', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                  {record?.product_info?.ref_product_code}
                </div>
              </Tooltip>
            )}
            <H3Text text={record?.product_info?.product_sku_name?.trim()} className="prod-table__text" />
            {record?.product_info?.product_category_info?.category_path?.length > 0 && (
              <ProductCategoryLabel
                categoryPath={record?.product_info?.product_category_info?.category_path}
                categoryName={record?.product_info?.product_category_info?.category_path?.at(-1)}
                containerStyle={{
                  width: 'fit-content',
                }}
              />
            )}
            <div className="table-subscript-grey" dangerouslySetInnerHTML={{ __html: record?.product_info?.description }} />
          </div>
        ),
        visible: visibleColumns?.COMPONENT?.visible,
      },
      {
        title: 'On Hand',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (item) => (
          <div style={{ whiteSpace: 'nowrap' }}>
            {`${QUANTITY(((item?.on_hand_quantity * item?.product_info?.uom_info?.ratio) / item?.uom_info?.ratio) || '0', item?.uom_info?.precision)} ${item?.uom_info?.uqc?.toProperCase() || ''}`}
          </div>
        ),
        className: isChild ? 'display-none' : '',
        visible: visibleColumns?.ON_HAND?.visible,
      },
      {
        title: 'Availability',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (text, record) => {
          const { quantity, originalQuantity } = this.state;
          return (
            <div className="status">
              <div className={((record?.on_hand_quantity * record?.product_info?.uom_info?.ratio) / record?.uom_info?.ratio) - (Number(quantity / originalQuantity || 0) * (record?.quantity * (1 + (Number(record?.wastage_percentage || 0) / 100))))?.toFixed(record?.uom_info?.precision) >= 0 ? 'active-chip' : 'inactive-chip'}>
                {((record?.on_hand_quantity * record?.product_info?.uom_info?.ratio) / record?.uom_info?.ratio) - (Number(quantity / originalQuantity || 0) * (record?.quantity * (1 + (Number(record?.wastage_percentage || 0) / 100))))?.toFixed(record?.uom_info?.precision) >= 0 ? 'Available' : 'Not Available'}
              </div>
            </div>
          );
        },
        className: isChild ? 'display-none' : '',
        visible: visibleColumns?.AVAILABILITY?.visible,
      },
      {
        title: 'Usage',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        width: '110px',
        render: (item) => {
          const { quantity, originalQuantity, rmUsage } = this.state;
          return (
            <div style={{ whiteSpace: 'nowrap', width: '100px' }}>
              {`${QUANTITY((Number(quantity / originalQuantity || 0) * item?.quantity), item?.uom_info?.precision)} ${item?.uom_info?.uqc?.toProperCase() || ''}`}
            </div>
          );
        },
        visible: visibleColumns?.CONSUMPTION?.visible && !byProductLines,
      },
    ];
    if (selectedBom?.enable_wastage_tracking && !byProductLines) {
      columns.push({
        title: 'Overage %',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (item) => (
          <Popover content="Overage/Wastage">
            {item?.wastage_percentage}
          </Popover>
        ),
        className: isChild ? 'display-none' : '',
        visible: visibleColumns?.WASTAGE?.visible,
      });
    }
    columns.push({
      title: 'Total Usage',
      responsive: ['sm', 'md', 'lg', 'xxl'],
      width: '110px',
      render: (item) => {
        const { quantity, originalQuantity, rmUsage } = this.state;
        return (
          <div style={{ whiteSpace: 'nowrap', width: '100px' }}>
            {`${QUANTITY((Number(quantity / originalQuantity || 0) * rmUsage?.find((rmLine) => item?.bom_line_id === rmLine?.bom_line_id)?.quantity), item?.uom_info?.precision)} ${item?.uom_info?.uqc?.toProperCase() || ''}`}
          </div>
        );
      },
      visible: visibleColumns?.CONSUMPTION?.visible && !byProductLines,
    })
    columns.push({
      title: 'Production',
      responsive: ['sm', 'md', 'lg', 'xxl'],
      render: (item) => {
        const { quantity, originalQuantity } = this.state;
        return (
          <div style={{ whiteSpace: 'nowrap' }}>
            {`${QUANTITY((Number(quantity / originalQuantity || 0) * item?.quantity), item?.uom_info?.precision)} ${item?.uom_info?.uqc?.toProperCase() || ''}`}
          </div>
        );
      },
      visible: visibleColumns?.PRODUCTION?.visible && byProductLines,
    });
    if (Helpers.getPermission(Helpers.permissionEntities.BOM,
      Helpers.permissionTypes.COSTING, user) && !byProductLines) {
      columns.push(...[
        {
          title: 'Cost Per Unit',
          responsive: ['sm', 'md', 'lg', 'xxl'],
          render: (text, record) => {

            const costPerUnit = costingMethod === '3_MONTH_WEIGHTED_AVERAGE'
              ? (record?.item_unit_cost_for_3months || 0) : costingMethod === '6_MONTH_WEIGHTED_AVERAGE'
                ? (record?.item_unit_cost_for_6months || 0) : (record?.item_unit_cost || 0);

            return (
              <React.Fragment>
                {isChild ? '-' : (
                  <div>
                    {(isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver popOverMessage="You don;t have access to view cost per unit" /> : `${this.props.MONEY(costPerUnit)} / ${record?.uom_info?.uqc?.toProperCase()}`}
                  </div>
                )}
              </React.Fragment>
            )
          },
          visible: visibleColumns?.COST_PER_UNIT?.visible,
        },
        {
          title: 'Cost',
          responsive: ['sm', 'md', 'lg', 'xxl'],
          render: (text, record) => {
            const totalCostWithWastage = costingMethod === '3_MONTH_WEIGHTED_AVERAGE'
              ? (record?.item_total_cost_with_wastage_for_3months || 0) : costingMethod === '6_MONTH_WEIGHTED_AVERAGE'
                ? (record?.item_total_cost_with_wastage_for_6months || 0) : (record?.item_total_cost_with_wastage || 0);
            return (
              <div style={{ width: '80px' }}>
                {isChild ? '-' : (isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver popOverMessage="You don;t have access to view cost per unit" /> : this.props.MONEY((totalCostWithWastage))}
              </div>
            );
          },
          visible: visibleColumns?.COST?.visible,
        }]);
    }

    if (cfBillOfMaterialLine?.length) {
      columns.splice(5, 0, ...CustomFieldHelpers.renderCustomLineColumns(isChild, cfBillOfMaterialLine, visibleColumns, null));
    }
    return columns.filter((item) => item.visible);
  }

  componentDidMount() {
    const {
      getBOMById, getBOMByIdSuccess, user, match, getBomJobWork,
    } = this.props;
    const bomId = this.props?.bomId || match.params?.bomId;
    const isV2Enabled = user?.tenant_info?.global_config?.settings?.enable_jw_v2;
    getBOMByIdSuccess(null);
    if (bomId) {
      getBOMById(user?.tenant_info?.org_id, user?.tenant_info?.tenant_id, bomId, Helpers.getTenantDepartmentId(user), 'first_level', () => {
        this.setState({ isUserReady: false });
      });
      if (isV2Enabled) {
        getBomJobWork({
          bomId,
        }, (bomJobWorks) => {
          this.setState({ bomJobWorks })
        })
      }
    }
  }

  static getDerivedStateFromProps(props, state) {
    if (props?.selectedBOM && !state.isUserReady) {
      const bomRawMaterialLines = props?.selectedBOM?.bom_lines_first_level_exploded_view?.map((item) => ({
        ...item,
        key: uuidv4(),
        on_hand_quantity: item?.tenant_product_info?.on_hand_qty,
        quantity: item?.bom_line_quantity,
        lineCustomFields: item?.bom_line_custom_fields,
      }));
      const rmUsage = getTotalRmUsage(bomRawMaterialLines, props?.selectedBOM?.bom_quantity);
      const bomRMLinesLastLevel = props.selectedBOM?.bom_lines?.filter((item) => !item?.child_bom_lines?.length)?.map((item) => ({
        ...item,
        key: uuidv4(),
      }));
      const lineCf = props?.selectedBOM?.bom_lines?.[0]?.bom_line_custom_fields;
      const onHandRatioArray = [];
      for (let i = 0; i < bomRawMaterialLines?.length; i++) {
        onHandRatioArray.push(((Number(((bomRawMaterialLines[i]?.on_hand_quantity * bomRawMaterialLines[i]?.product_info?.uom_info?.ratio) / bomRawMaterialLines[i]?.uom_info?.ratio) || 0) * Number(props?.selectedBOM?.bom_quantity || 0)) / (Number(bomRawMaterialLines[i]?.quantity || 0) * (1 + (Number(bomRawMaterialLines[i]?.wastage_percentage || 0) / 100)))));
      }
      return {
        ...state,
        fileList: props?.selectedBOM?.attachments,
        isUserReady: true,
        bomProductLines: props?.selectedBOM?.bom_by_products?.map((item) => ({
          ...item, quantity: item?.bom_bp_quantity, on_hand_quantity: item?.tenant_product_info?.on_hand_qty, bom_cost: item?.tenant_product_info?.cost_price,
        })),
        rmUsage,
        bomRawMaterialLines,
        bomRMLinesLastLevel,
        selectedBom: props?.selectedBOM,
        bomName: props?.selectedBOM?.bom_alias_name,
        quantity: props?.selectedBOM?.bom_quantity,
        originalQuantity: props?.selectedBOM?.bom_quantity,
        dataTemplateRoutes: {
          template_production_route_id: props?.selectedBOM?.bom_production_route?.template_production_route_id,
          bom_route_name: props?.selectedBOM?.bom_production_route?.template_route_name,
          is_sequential: props?.selectedBOM?.bom_production_route?.is_sequential,
          is_time_tracked: props?.selectedBOM?.bom_production_route?.is_time_tracked,
          br_lines: props?.selectedBOM?.bom_production_route?.br_lines?.map((tprLine) => ({
            ...tprLine,
            key: uuidv4(),
            is_subcontractor: !!tprLine?.subcontractor_seller_id,
            machine_resource_group: tprLine?.machine_resource_group?.resource_group_id,
            equipments_resource_groups: tprLine?.equipments_resource_groups?.map((i) => i?.resource_group_id),
            other_resource_groups: tprLine?.other_resource_groups?.map((i) => i?.resource_group_id),
            rm_lines: tprLine?.rm_lines?.map((i) => i?.product_sku_id),
            fixed_charge: tprLine?.route_line_charges?.find((i) => i?.charge_type === 'fixed')?.charge_amount,
            unit_charge: tprLine?.route_line_charges?.find((i) => i?.charge_type === 'unit')?.charge_per_unit,
          })),
        },
        totalReadyToProduce: onHandRatioArray?.sort((a, b) => a - b)[0],
        cfBillOfMaterialLine: lineCf,
        visibleColumns: CustomFieldHelpers.updateVisibleColumns(lineCf, state.visibleColumns),
      };
    }
    return state;
  }

  getDataSource(data) {
    if (data) {
      const copyData = JSON.parse(JSON.stringify(data));
      return copyData;
    }
    return [];
  }

  onSelectTemplateRouteLines(routeId) {
    const { user, getTemplateRouteById } = this.props;
    getTemplateRouteById(user?.tenant_info?.org_id, routeId, (selectedTemplateRoute) => {
      const tprLines = selectedTemplateRoute?.tpr_lines?.map((tprLine) => ({
        ...tprLine,
        key: uuidv4(),
        machine_resource_group: tprLine?.machine_resource_group?.resource_group_id,
        equipments_resource_groups: tprLine?.equipments_resource_groups?.map((i) => i?.resource_group_id),
        other_resource_groups: tprLine?.other_resource_groups?.map((i) => i?.resource_group_id),
      }));
      const templateRoute = {
        template_production_route_id: selectedTemplateRoute?.template_production_route_id,
        bom_route_name: selectedTemplateRoute?.template_route_name,
        is_sequential: selectedTemplateRoute?.is_sequential,
        is_time_tracked: selectedTemplateRoute?.is_time_tracked,
        br_lines: tprLines,
      };
      this.setState({
        dataTemplateRoutes: templateRoute,
        templateRouteId: routeId,
      });
    });
  }

  handleFileChange(fileListData) {
    const { updateAttachment, selectedBOM, getAttachmentById } = this.props;

    const fileList = fileListData?.fileList?.map((item) => ({
      ...item,
      url: item?.response?.response?.location || item?.url,
    }));
    const attachments = fileList?.map((attachment) => ({
      url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
    }));
    if (fileListData?.file.status === 'done') {
      const payload = {
        entity_id: selectedBOM?.bom_id,
        entity_name: 'bill_of_material',
        attachments,
      };
      updateAttachment(payload, () => {
        getAttachmentById(selectedBOM?.bom_id, 'bill_of_material', () => {
          this.setState({
            isGetAttachment: true,
          });
        });
      });
    }
    if (fileListData?.file.status === 'removed') {
      const payload = {
        entity_id: selectedBOM?.bom_id,
        entity_name: 'bill_of_material',
        attachments,
      };
      updateAttachment(payload, () => {
        getAttachmentById(selectedBOM?.bom_id, 'bill_of_material', () => {
          this.setState({
            isGetAttachment: true,
          });
        });
      });
    }

    this.setState({
      fileList: attachments.length > 0 ? attachments : [],
    });
  }

  renderChildRawMaterials(bomLines) {
    return (
      <Table
        showHeader={false}
        size="small"
        scroll="scroll"
        columns={this.getColumns(true)?.filter((item) => !['PRODUCTION', 'TOTAL PRODUCTION'].includes(item.title))}
        dataSource={this.getDataSource(bomLines)}
        pagination={false}
        expandable={{
          rowExpandable: (record) => record?.child_bom_id,
          expandedRowRender: (record) => this.renderChildRawMaterials(record?.child_bom_lines?.map((item) => ({
            ...item,
            key: uuidv4(),
            on_hand_quantity: item?.tenant_product_info?.on_hand_qty,
            quantity: item?.bom_line_quantity,
          }))),
        }}
      />
    );
  }

  render() {
    const {
      selectedProduct, downloadDocument, user, MONEY, createBOMLoading, updateBOMLoading, deleteBOM, callback, match, getBomJobWork, getBOMByIdLoading, history, selectedBOM, priceMasking,
    } = this.props;
    const {
      quantity, selectedBom, bomProductLines, bomRawMaterialLines, totalReadyToProduce, cloneBomDrawer, visibleColumns,
      originalQuantity, bomName, currentTab, templateRouteId, fileList, dataTemplateRoutes, bomRMLinesLastLevel, cfBillOfMaterialLine, costingMethod, showPRZModal, selectedDocumentId,
    } = this.state;
    const bomId = this.props?.bomId || match.params?.bomId;
    const isV2Enabled = user?.tenant_info?.global_config?.settings?.enable_jw_v2;
    const dataSourceForCharges = this.state.selectedBom?.extra_charges?.map((charges) => ({
      entity_level_message_id: charges?.entity_level_message_id,
      charge_name: charges?.charge_name,
      charge_amount: charges?.charge_amount,
    }));

    const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;

    const columnsForCharges = [
      {
        title: 'Charge Name',
        dataIndex: 'charge_name',
        key: 'charge_name',
        width: '400px',
        render: (text, record) => (
          <H3Text text={record.charge_name} className="prod-table__text" />
        ),
      },
      {
        title: 'Charge Amount',
        dataIndex: 'charge_amount',
        key: 'charge_amount',
        width: '170px',
        render: (text, record) => (
          <H3Text text={MONEY(record?.charge_amount)} className="prod-table__text" hideText={isDataMaskingPolicyEnable && isHideCostPrice} popOverMessage={`You don't have access to view ${record.charge_name} charge`} />
        ),
      },
    ];

    const itemUnitCostWithWastage = costingMethod === '3_MONTH_WEIGHTED_AVERAGE'
      ? (selectedBom?.item_unit_cost_with_wastage_for_3months || 0) : costingMethod === '6_MONTH_WEIGHTED_AVERAGE'
        ? (selectedBom?.item_unit_cost_with_wastage_for_6months || 0) : (selectedBom?.item_unit_cost_with_wastage || 0);

    const pricingOptions = [
      { label: 'Normal', value: 'NORMAL' },
      { label: 'Past 3 months Weighted Average', value: '3_MONTH_WEIGHTED_AVERAGE' },
      { label: 'Past 6 months Weighted Average', value: '6_MONTH_WEIGHTED_AVERAGE' },
    ];

    return (
      <Fragment>
        <div className="product-bom__wrapper" style={{ marginTop: window.location.pathname.includes('production/bom/view/') ? '85px' : '0px', paddingRight: "15px" }}>
          {
            getBOMByIdLoading ? (
              <div className="view-bom__loading">
                <div className="loadingBlock view-bom__loading-l1" />
                <div className="loadingBlock view-bom__loading-l2" />
                <div className="loadingBlock view-bom__loading-l3" />
                <div className="loadingBlock view-bom__loading-l4" />
                <div className="loadingBlock view-bom__loading-l5" />
              </div>
            ) : (
              <Fragment>
                <Drawer
                  open={cloneBomDrawer}
                  width="980px"
                  mask
                  onClose={() => this.setState({ cloneBomDrawer: false })}
                  destroyOnClose
                >
                  <div className="custom-drawer__header-wrapper">
                    <div className="custom-drawer__header" style={{ width: '925px' }}>
                      <H3Text text="Create New Bill of Materials" className="custom-drawer__title" />
                      <H3Image src={closeIcon} className="custom-drawer__close-icon" onClick={() => this.setState({ cloneBomDrawer: false })} />
                    </div>
                  </div>
                  <FormBOM
                    bomId={selectedBom?.bom_id}
                    callback={() => {
                      history.push(`/production/bom`);
                    }}
                  />
                </Drawer>
                <div className="product-bom__header">
                  <div className="ant-row">
                    <div className="ant-col-md-12 ant-col-xs-12">
                      <div className="product-bom__input-row">
                        <H3Text text="BOM Name" className="product-bom__input-row__label" />
                        <div className="orgInputContainer product-bom__input-row__input">
                          <H3Text text={bomName} />
                        </div>
                      </div>
                      <div className="product-bom__input-row">
                        <H3Text text="Quantity" className="product-bom__input-row__label" />
                        <div className="product-bom__input-row__input" style={{ width: '100px' }}>
                          <H3FormInput
                            name="Quantity"
                            type="number"
                            labelClassName="orgFormLabel"
                            inputClassName="orgFormInput input"
                            placeholder=""
                            onChange={(e) => this.setState({ quantity: e.target.value })}
                            value={quantity}
                          />
                        </div>
                        &nbsp;
                        <span>{selectedBom?.product_info?.uom_info?.uqc?.toProperCase()}</span>
                      </div>
                      <div className="product-bom__input-row">
                        <H3Text text="Default Yield Percentage" className="product-bom__input-row__label" />
                        <div className="orgInputContainer product-bom__input-row__input">
                          {selectedBom?.default_yield_percentage}%
                        </div>
                      </div>
                      <div className="product-bom__input-row">
                        <H3Text text="Ready to Produce" className="product-bom__input-row__label" />
                        <div className="orgInputContainer product-bom__input-row__input">
                          {QUANTITY(totalReadyToProduce, selectedProduct?.uom_info?.precision)}
                          {' '}
                          {selectedBom?.product_info?.uom_info?.uqc?.toProperCase()}
                        </div>
                      </div>
                      {selectedBom?.remarks && <div className="product-bom__input-row">
                        <H3Text text="Internal Remarks" className="product-bom__input-row__label" />
                        <div className="orgInputContainer product-bom__input-row__input">
                          {selectedBom?.remarks}
                        </div>
                      </div>}
                      {currentTab === '/raw-materials' && (
                        <div className="product-bom__input-row">
                          <H3Text text="Costing Method" className="product-bom__input-row__label" />
                          <div className="product-bom__input-row__input">
                            <PRZSelect
                              value={costingMethod}
                              onChange={(value) => {
                                this.setState({ costingMethod: value });
                              }}
                              options={pricingOptions}
                            />
                          </div>
                        </div>
                      )}
                      <ViewCustomFields
                        customFields={selectedBom?.custom_fields}
                        wrapperClass="product-bom__input-row"
                        labelClass="product-bom__input-row__label"
                        valueClass="orgInputContainer product-bom__input-row__input"
                      />
                    </div>
                    <div className="ant-col-md-12 ant-col-xs-12">
                      <div className="product-bom__header-right">
                        {user?.tenant_info?.production_config?.addons?.access_restriction?.is_active && (
                          <Fragment>
                            {(selectedBom?.is_restricted && (selectedBom?.bom_users_info?.length > 0)) ? (
                              <div className="product-bom__lock">
                                <FontAwesomeIcon icon={faLock} style={{ fontSize: '16px', width: '35px', textAlign: 'center' }} />
                              </div>
                            ) : (
                              <div className="product-bom__unlock">
                                <FontAwesomeIcon icon={faUnlock} style={{ fontSize: '16px', width: '35px', textAlign: 'center' }} />
                              </div>
                            )}
                            <div>
                              {selectedBom?.is_restricted && (selectedBom?.bom_users_info?.length > 0) && (
                                <Avatar.Group maxCount={3}>
                                  {selectedBom?.bom_users_info?.map((item) => (
                                    <Tooltip title={item?.first_name}>
                                      <Avatar
                                        style={{
                                          backgroundColor: Helpers.getAvatarColors(item?.first_name?.[0])?.color1,
                                        }}
                                      >
                                        {item?.first_name.substring(0, 2).toUpperCase()}
                                      </Avatar>
                                    </Tooltip>))}
                                </Avatar.Group>
                              )}
                            </div>
                          </Fragment>
                        )}
                        <div className="action-buttons">
                          {Helpers.getPermission(Helpers.permissionEntities.BOM,
                            Helpers.permissionTypes.UPDATE, user) && (
                              <EditOutlined
                                className="action-button"
                                style={{
                                  justifyContent: 'center',
                                  fontSize: '16px',
                                }}
                                onClick={() => {
                                  history.push(`/production/bom/update/${selectedBom?.bom_id}`);
                                }}
                              />
                            )}
                          <Tooltip
                            placement="topLeft"
                            title="Make a Copy"
                          >
                            <CopyOutlined
                              className="action-button"
                              style={{
                                justifyContent: 'center',
                                fontSize: '16px',
                              }}
                              onClick={() => {
                                history.push({
                                  pathname: `/production/bom/create`,
                                  state: {
                                    cloneBom: true,
                                    bomId: selectedBom?.bom_id,
                                  },
                                });
                              }}
                            />
                          </Tooltip>
                          <Dropdown
                            getPopupContainer={(trigger) => trigger.parentNode}
                            overlay={(
                              <Menu style={{ width: '200px' }}>
                                {<Menu.Item>
                                  <H3Text
                                    text="BOM Overview without Cost"
                                    onClick={() => downloadDocument({
                                      url: `${Constants.BOM_V2}/download?bom_id=${selectedBom?.bom_id}&org_id=${user?.tenant_info?.org_id}&tenant_department_id=${Helpers.getTenantDepartmentId(user)}&bom_qty=${quantity}&tenant_id=${user?.tenant_info?.tenant_id}&bom_with_cost=${false}&type=pdf`,
                                      document_type: 'BILL_OF_MATERIAL',
                                      document_number: selectedBom?.bom_number,
                                      key: uuidv4(),
                                    })}
                                    className="action-button action-button-big"

                                  />
                                </Menu.Item>}
                                {Helpers.getPermission(Helpers.permissionEntities.BOM,
                                  Helpers.permissionTypes.COSTING, user) && (
                                    <Menu.Item>
                                      <H3Text
                                        text="BOM Overview with Cost"
                                        onClick={() => downloadDocument({
                                          url: `${Constants.BOM}/download?bom_id=${selectedBom?.bom_id}&org_id=${user?.tenant_info?.org_id}&tenant_department_id=${Helpers.getTenantDepartmentId(user)}&bom_qty=${quantity}&tenant_id=${user?.tenant_info?.tenant_id}&bom_with_cost=${true}&type=pdf&costing_method=${costingMethod}`,
                                          document_type: 'BILL_OF_MATERIAL',
                                          document_number: selectedBom?.bom_number,
                                          key: uuidv4(),
                                        })}
                                        className="action-button action-button-big"
                                      />
                                    </Menu.Item>
                                  )}
                              </Menu>
                            )}
                            trigger={['click']}
                            placement="topLeft"
                          >
                            <div
                              className="action-button "
                              onClick={(e) => e.preventDefault()}
                            >
                              <FontAwesomeIcon icon={faPrint} style={{ fontSize: '16px', width: '35px', textAlign: 'center' }} />
                            </div>
                          </Dropdown>
                          {Helpers.getPermission(Helpers.permissionEntities.BOM,
                            Helpers.permissionTypes.DELETE, user) && (
                              <Popconfirm
                                title="Are you sure you want to delete this BOM?"
                                onConfirm={() => {
                                  deleteBOM({
                                    org_id: selectedBom?.org_id,
                                    bom_id: selectedBom?.bom_id,
                                    is_archived: true,
                                  }, () => {
                                    history.push('/production/bom')
                                  });
                                }}
                              >
                                <div
                                  className="action-button "
                                  onClick={(e) => e.preventDefault()}
                                >
                                  <FontAwesomeIcon icon={faTrashCan} style={{ fontSize: '16px', width: '35px', textAlign: 'center' }} />
                                </div>
                              </Popconfirm>
                            )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bom-body__wrapper">
                  <Tabs
                    activeKey={currentTab?.toString()}
                    onChange={(key) => {
                      this.setState({ currentTab: key });
                    }}
                    mode="horizontal"
                    type="card"
                  >
                    <TabPane
                      tab={(
                        <span>
                          Raw Materials / Packaging Materials &nbsp;
                          <Badge count={this.getDataSource(bomRawMaterialLines)?.length} />
                        </span>
                      )}
                      key="/raw-materials"
                    />
                    {this.getDataSource(bomProductLines)?.length > 0 && (
                      <TabPane
                        tab={(
                          <span>
                            By Products &nbsp;
                            <Badge count={this.getDataSource(bomProductLines)?.length} />
                          </span>
                        )}
                        key="/by-products"
                      />
                    )}
                    {user?.tenant_info?.production_config?.addons?.factory_setup?.is_active && (
                      <TabPane
                        tab={(
                          <span>
                            Production Process &nbsp;
                            <Badge count={selectedBom?.bom_production_route?.br_lines?.length} />
                          </span>
                        )}
                        key="/production-process"
                      />
                    )}
                    {(selectedBOM?.extra_charges?.length > 0) && (<TabPane
                      tab={(
                        <span>
                          Charges &nbsp;
                          <Badge count={selectedBOM?.extra_charges?.length} />
                        </span>
                      )}
                      key="/charges"
                    />)}
                  </Tabs>
                  {/* {currentTab === '/raw-materials' && <div>
                    <CustomDocumentColumns
                      visibleColumns={visibleColumns}
                      setVisibleColumns={(data) => this.setState({ visibleColumns: data })}
                      customColumns={cfBillOfMaterialLine}
                    />
                  </div>} */}
                </div>

                {currentTab === '/raw-materials' && (
                  <div className="product-bom__table generic-table-container">
                    <Table
                      showHeader
                      size="small"
                      scroll={{ x: 'max-content' }}
                      columns={this.getColumns(false)?.filter((item) => !['PRODUCTION', 'TOTAL PRODUCTION'].includes(item.title))}
                      dataSource={this.getDataSource(bomRawMaterialLines)}
                      pagination={false}
                      expandable={{
                        rowExpandable: (record) => record?.child_bom_id,
                        expandedRowRender: (record) => (
                          <div style={{ width: 'calc(100% - 30px)', marginLeft: '30px' }}>
                            {this.renderChildRawMaterials(record?.child_bom_lines?.map((item) => ({
                              ...item,
                              key: uuidv4(),
                              on_hand_quantity: item?.tenant_product_info?.on_hand_qty,
                              quantity: item?.bom_line_quantity,
                            })))}
                          </div>
                        ),
                      }}
                    />
                  </div>
                )}
                {currentTab === '/by-products' && (
                  <div className="product-bom__table generic-table-container">
                    <Table
                      showHeader
                      size="small"
                      scroll="scroll"
                      columns={this.getColumns(false)?.filter((item) => !['AVAILABILITY', 'COST', 'COST PER UNIT', 'OVERAGE %', 'TOTAL CONSUMPTION', 'CONSUMPTION'].includes(item.title))}
                      dataSource={this.getDataSource(bomProductLines)}
                      pagination={false}
                    />
                  </div>
                )}
                {currentTab === '/production-process' && (
                  <Fragment>
                    {isV2Enabled ? <TemplateRouteLinesV2
                      callback={() => {
                        getBomJobWork({
                          bomId,
                        }, (bomJw) => {
                          this.setState({ bomJobWorks: bomJw })
                        })
                      }}
                      bomRawMaterialLines={selectedBom?.bom_lines_first_level_exploded_view}
                      quantity={quantity}
                      bomId={bomId}
                    /> :
                      <TemplateRouteLines
                        onSelectTemplateRoute={(routeId) => this.onSelectTemplateRouteLines(routeId)}
                        templateRoute={dataTemplateRoutes}
                        templateRouteId={templateRouteId}
                        updateBomTemplateRoute={(val) => this.setState({ dataTemplateRoutes: val })}
                        updateTemplateRouteId={(val) => this.setState({ templateRouteId: val })}
                        rawMaterialData={this.getDataSource(bomRMLinesLastLevel)}
                        formSubmitted={false}
                        selectedBom={selectedBom}
                        quantity={quantity}
                        readonly
                      />
                    }
                  </Fragment>
                )}
                {currentTab === '/charges' && (
                  <Table
                    dataSource={dataSourceForCharges}
                    columns={columnsForCharges}
                    pagination={false}
                    rowKey="entity_level_message_id"
                    size="small"
                    style={{ width: '620px' }}
                    onRow={(record) => ({
                      onMouseEnter: () => this.setState({ hoveredRowReason: record.entity_level_message_id }),
                      onMouseLeave: () => this.setState({ hoveredRowReason: null }),
                    })}
                  />
                )}
                {currentTab === '/raw-materials' && <div className="product-bom__footer">
                  <div className="ant-row">
                    <div className="ant-col-md-10">
                      <div className="product-bom__footer-left">
                        <br />
                        <Attachment
                          fileList={fileList}
                          disableCase={createBOMLoading || updateBOMLoading}
                          labelClassName="orgFormLabel"
                          inputClassName=""
                          containerClassName="view__grn-attachment"
                          handleFileChange={(value) => this.handleFileChange(value)}
                          updateEnable={Helpers.getPermission(Helpers.permissionEntities.BOM, Helpers.permissionTypes.UPDATE, user)}
                        />

                      </div>
                    </div>
                    {Helpers.getPermission(Helpers.permissionEntities.BOM,
                      Helpers.permissionTypes.COSTING, user)
                      && (
                        <div className="ant-col-md-14">
                          <div className="product-bom__footer-right">
                            {(isDataMaskingPolicyEnable && isHideCostPrice) ? <div><HideComponent style={{
                              width: 'fit-content'
                            }} /></div> : <div className="product-bom__totals">
                              {selectedBom?.extra_charges?.map((charge) => (
                                <div key={charge.charge_name} className="product-bom__totals-field">
                                  <H3Text text={charge.charge_name} className="product-bom__totals-field-name" />
                                  <H3Text
                                    text={MONEY((charge.charge_amount * quantity) / originalQuantity || 0)}
                                    className="product-bom__totals-field-value"
                                  />
                                </div>
                              ))}
                              <div className="product-bom__totals-field-total" />
                              <div className="product-bom__totals-field">
                                <H3Text text="Production Cost/Unit" className="product-bom__totals-field-name" />
                                <H3Text
                                  text={MONEY(itemUnitCostWithWastage)}
                                  className="product-bom__totals-field-value"
                                />
                              </div>
                              <div className="product-bom__totals-field">
                                <H3Text
                                  text={`Total Production Cost (${quantity || 0} ${selectedProduct?.uom_info?.uqc?.toProperCase() || ''})`}
                                  className="product-bom__totals-field-name"
                                />
                                <H3Text
                                  text={MONEY(itemUnitCostWithWastage * Number(quantity))}
                                  className="product-bom__totals-field-value"
                                />
                              </div>
                            </div>}
                          </div>
                        </div>
                      )}
                  </div>
                </div>}
                <PRZModal
                  isOpen={showPRZModal}
                  onClose={() => {
                    this.setState({ showPRZModal: false, selectedDocumentId: null });
                  }}
                  entityType="product"
                  documentId={selectedDocumentId}
                />
              </Fragment>
            )
          }
        </div>
      </Fragment>
    );
  }
}

const mapStateToProps = ({ UserReducers, BOMReducers, ProductReducers }) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  billOfMaterials: BOMReducers.billOfMaterials,
  getBOMLoading: BOMReducers.getBOMLoading,
  selectedProduct: ProductReducers.selectedProduct,
  getBOMByIdLoading: BOMReducers.getBOMByIdLoading,
  selectedBOM: BOMReducers.selectedBOM,
  createBOMLoading: BOMReducers.createBOMLoading,
  updateBOMLoading: BOMReducers.updateBOMLoading,
  deleteBOMLoading: BOMReducers.deleteBOMLoading,
  getBomJobWorkLoading: BOMReducers.getBomJobWorkLoading,
  priceMasking: UserReducers.priceMasking,
});

const mapDispatchToProps = (dispatch) => ({
  getBOM: (orgId, tenantId, bomId, productSkuId, status, page, limit, searchKeyword, tenantDepartmentId, type, callback) => dispatch(
    BOMActions.getBOM(orgId, tenantId, bomId, productSkuId, status, page, limit, searchKeyword, tenantDepartmentId, type, callback),
  ),
  getBOMById: (orgId, tenantId, bomId, tenantDepartmentId, bomViewType, callback) => dispatch(BOMActions.getBOMById(orgId, tenantId, bomId, tenantDepartmentId, bomViewType, callback)),
  getBOMByIdSuccess: (selectedBOM) => dispatch(BOMActions.getBOMByIdSuccess(selectedBOM)),
  getBOMSuccess: (billOfMaterials) => dispatch(BOMActions.getBOMSuccess(billOfMaterials)),
  getTemplateRouteById: (orgId, templateRouteId, callback) => dispatch(TemplateRouteActions.getTemplateRouteById(orgId, templateRouteId, callback)),
  updateAttachment: (payload, callback) => dispatch(
    AttachmentActions.updateAttachment(payload, callback),
  ),
  getAttachmentById: (entityId, entityName, callback) => dispatch(
    AttachmentActions.getAttachmentById(entityId, entityName, callback),
  ),
  downloadDocument: (payload, document) => dispatch(AnalyticsActions.downloadDocument(payload, document)),
  deleteBOM: (payload, callback) => dispatch(BOMActions.deleteBOM(payload, callback)),
  getBomJobWork: (payload, callback) => dispatch(BOMActions.getBomJobWork(payload, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(ProductBOM));
