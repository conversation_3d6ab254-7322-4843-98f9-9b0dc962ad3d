/* eslint-disable jsx-a11y/alt-text */
import React, { Component, Fragment } from 'react';
import { connect } from 'react-redux';
import { withRout<PERSON>, <PERSON> } from 'react-router-dom';
import PropTypes from 'prop-types';
import {
  Drawer, Popconfirm, Progress, Table, Tooltip,
} from 'antd';
import { QUANTITY } from '@Apis/constants';
import { v4 as uuidv4 } from 'uuid';
import { CaretRightFilled } from '@ant-design/icons';
import H3Text from '@Uilib/h3Text';
import H3Image from '@Uilib/h3Image';
import closeIcon from '@Images/icons/icon-close-blue.png';
import H3Progress from '@Components/Common/H3Progress';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import ProductCategoryLabel from '@Components/Common/ProductCategoryLabel';
import Helpers from '@Apis/helpers';
import { faXmark } from '@fortawesome/free-solid-svg-icons';
import BatchSelector from '../../BatchSelector';
import AddRMForm from '../RawMaterialsLines/AddRMForm';
import { RemoveByProductMO as RemoveByProductMOModule } from '../../../../../../modules/byProductMO';
import PRZModal from '../../../../../Common/UI/PRZModal';
import ProductCodesAndName from '@Components/Common/ProductCodesAndName';
import './style.scss';

/**
 * MOLines component
 *
 * @param record
 * @param file
 */
class ByProductsLines extends Component {
  /**
   * Constructor
   *
   * @param props
   */
  constructor(props) {
    super(props);
    this.state = {
      openAddRMDrawer: false,
      parentData: null,
      excludedProducts: [],
      byProductColumns: [
        {
          title: '',
          render: (record) => (
            <div className="mobile-list__item" style={{ backgroundColor: record?.fg_product ? '#ABE09835' : '#f1f9ffcc' }}>
              {record?.fg_product ? (
                <div className="bp-table__fg-name">
                  {record?.fg_product?.tenant_product_info?.product_sku_name}
                  {' '}
                  <CaretRightFilled />
                </div>
              ) : (
                <React.Fragment>
                  <div style={{ display: 'flex' }}>
                    <Link to={`/inventory/product/view/${record?.tenant_product_info?.product_sku_id}`}>
                      <H3Text text={`#${record?.tenant_product_info?.internal_sku_code}`} className="mobile-list__item-number" />
                    </Link>
                  </div>
                </React.Fragment>
              )}
              <div className="mobile-list__item-info">
                {record?.tenant_product_info?.product_sku_name}
              </div>
              <div className="mobile-list__item-completion">
                <div style={{ fontSize: '11px', width: 'calc(100% - 100px)' }}>
                  <Progress
                    percent={Number((record?.completed_quantity / record?.quantity) * 100)}
                    status="active"
                    size="small"
                    strokeColor={{ from: '#108ee9', to: '#87d068' }}
                    trailColor="#2d7df71a"
                  />
                </div>
                <H3Text text="Completed" className="mobile-list__item-completion-text" />
              </div>
            </div>
          ),
          responsive: ['xs'],
        },
        {
          title: '#',
          responsive: ['sm', 'md', 'lg', 'xxl'],
          width: '40px',
          fixed: 'left',
          visible: true,
          render: (record) => {
            const { selectedMO } = props;
            const rmProductIds = selectedMO?.by_product_lines?.filter((item) => item?.fg_tenant_product_id === record?.fg_product?.tenant_product_id)?.map((line) => line?.tenant_product_info?.product_sku_id) || [];

            return ({
              props: {
                style: { backgroundColor: record?.fg_product ? '#ABE09835' : 'white' },
              },
              children: (
                <React.Fragment>
                  {
                    !record?.fg_product ? (
                      <div style={{ whiteSpace: 'nowrap', minWidth: '40px', paddingLeft: '8px' }}>
                        {`${record?.sqn ? record?.sqn : ''}`}
                      </div>
                    ) : (
                      <div className="bp-table__fg-name">
                        <div style={{
                          display: 'flex', alignItems: 'center', gap: '10px', paddingLeft: '10px',
                        }}
                        >
                          <div>
                            {record?.fg_product?.tenant_product_info?.product_sku_name}
                            {' '}
                            <CaretRightFilled />
                          </div>
                          {Helpers.getPermission(Helpers.permissionEntities.MO, Helpers.permissionTypes.UPDATE, props.user) && (
                            <div>
                              <div
                                className={`view-rm__button-inactive ${!['VOID', 'COMPLETED'].includes(props.selectedMO?.status) ? 'view-rm__button' : ''}`}
                                style={{ marginRight: '7px', width: '100%' }}
                                onClick={() => {
                                  if (!['VOID', 'COMPLETED'].includes(props.selectedMO?.status)) {
                                    this.setState({
                                      openAddRMDrawer: true,
                                      parentData: record?.fg_product,
                                      excludedProducts: [record?.fg_product?.tenant_product_info?.product_sku_id, ...rmProductIds],
                                    });
                                  }
                                }}
                              >
                                + Add By Product
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  }
                </React.Fragment>
              ),
            });
          },
          onCell: (item) => {
            if (item?.fg_product) {
              return { colSpan: 3 };
            }
            return {};
          },
        },
        {
          title: 'PRODUCT',
          responsive: ['sm', 'md', 'lg', 'xxl'],
          render: (record) => {
            const { selectedMO, removeByProductMO, callback, user } = props;
            const handleRemoveBP = (data) => {
              const payload = {
                body: {
                  mo_id: data?.mo_id,
                  by_product_id: data?.by_product_id,
                },
              };
              removeByProductMO(payload, () => {
                callback();
              });
            };
            const enableInternalRefCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_ref_code;
            const enableInternalSKUCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_sku_code;
            return ({
              props: {
                style: { backgroundColor: record?.fg_product ? '#ABE09835' : 'white' },
              },
              children: (
                <React.Fragment>
                  <div className="bp-product-container">
                    <div className="bp-product">
                      <div onClick={() => this.setState({ showPRZModal: true, selectedDocumentId: record?.tenant_product_info?.product_sku_id })}>
                        <ProductCodesAndName
                          skuCode={record?.tenant_product_info?.internal_sku_code}
                          refCode={record?.tenant_product_info?.ref_product_code}
                          name={record?.tenant_product_info?.product_sku_name}
                          showSku={enableInternalSKUCode}
                          showRefCode={enableInternalRefCode}
                        />
                      </div>
                    </div>
                    <div className="bp-adhoc-action-buttons">
                      {(!['VOID', 'COMPLETED'].includes(selectedMO?.status) && selectedMO?.status === 'IN_PROGRESS' ? selectedMO?.by_product_lines?.filter((bpLine) => bpLine?.fg_tenant_product_id == record?.fg_tenant_product_id)?.length > 1 : true) && !record?.by_product_adjustments?.length && (
                        <Tooltip title="Remove By Product">
                          <Popconfirm
                            placement="topLeft"
                            title="Are you sure you want to remove by product?"
                            onConfirm={() => {
                              if (!['VOID', 'COMPLETED'].includes(selectedMO?.status)) {
                                handleRemoveBP(record);
                              }
                            }}
                            okText="Yes"
                            cancelText="No"
                          >
                            <div className="bp-adhoc-action-button-remove">
                              <FontAwesomeIcon icon={faXmark} />
                            </div>
                          </Popconfirm>
                        </Tooltip>
                      )}
                    </div>
                  </div>
                  <div>
                    {!record?.bom_id && <H3Text text="ADHOC" className="view-bp-selection__new-product" />}
                    {record?.product_category_info?.category_path?.length > 0 && (
                      <ProductCategoryLabel
                        categoryPath={record?.product_category_info?.category_path}
                        categoryName={record?.product_category_info?.category_path?.at(-1)}
                        containerStyle={{
                          width: 'fit-content',
                        }}
                      />
                    )}
                  </div>
                </React.Fragment>
              ),
            });
          },
          onCell: (item) => {
            if (item?.fg_product) {
              return { colSpan: 0 };
            }
            return {};
          },
        },
        {
          title: 'COMPLETION',
          responsive: ['sm', 'md', 'lg', 'xxl'],
          render: (item) => (
            <Fragment>
              <div style={{ lineHeight: '24px' }}>
                <span>
                  Estimated -&nbsp;
                  {QUANTITY(item?.quantity, item?.uom_info?.precision) || 0}
                  {' '}
                  {item?.uom_info?.uqc?.toProperCase()}
                </span>
              </div>
              <div style={{ lineHeight: '24px' }}>
                {`Completed - ${QUANTITY(item?.completed_quantity, item?.uom_info?.precision)} ${item?.uom_info?.uqc?.toProperCase() || ''}`}
              </div>
              <div style={{
                paddingRight: '5px', width: '150px', cursor: 'pointer', marginTop: '5px',
              }}
              >
                <Tooltip title={(
                  <div>
                    <H3Text text={`Estimated - ${QUANTITY(item?.quantity, item?.uom_info?.precision) || 0} ${item?.uom_info?.uqc?.toProperCase()}`} />
                    <H3Text text={`Used - ${QUANTITY(item?.completed_quantity, item?.uom_info?.precision)} ${item?.uom_info?.uqc?.toProperCase() || ''}`} />
                  </div>
                )}
                >
                  <H3Progress
                    percent={parseInt((Math.abs(item?.completed_quantity) / item?.quantity) * 100)}
                    barWidth="150px"
                  />
                </Tooltip>
              </div>
            </Fragment>
          ),
          onCell: (item) => {
            if (item?.fg_product) {
              return { colSpan: 0 };
            }
            return {};
          },
        },
        {
          title: '',
          width: '105px',
          responsive: ['sm', 'md', 'lg', 'xxl'],
          render: (record) => ({
            props: {
              style: { backgroundColor: record?.fg_product ? '#ABE09835' : 'white' },
            },
            children: (
              <Fragment>
                {!record.fg_product ? <Fragment />
                  : (
                    <div
                      className={`view-fg__action-button-inactive ${this.props.selectedMO?.status === 'IN_PROGRESS' ? 'view-fg__action-button' : ''}`}
                      onClick={() => {
                        if (this.props.selectedMO?.status === 'IN_PROGRESS') {
                          this.props.handleDrawerState(record?.fg_product?.tenant_product_id);
                        }
                      }}
                    >
                      <div style={{ fontWeight: '500px' }}>+ Add Batch </div>
                    </div>
                  )}
              </Fragment>
            ),
          }),
        },
      ],
    };
  }

  /**
   *
   * @param {*} data
   * @return
   */
  getDataSource(data) {
    const { selectedMO } = this.props;
    const fgRmMapping = {};
    for (let i = 0; i < data?.length; i++) {
      if (fgRmMapping.hasOwnProperty(data[i]?.fg_tenant_product_id)) {
        fgRmMapping[data[i]?.fg_tenant_product_id].push({
          ...data[i],
        });
      } else {
        fgRmMapping[data[i]?.fg_tenant_product_id] = [{
          ...data[i],
        }];
      }
    }
    const finalFgRmList = [];
    for (let i = 0; i < Object.keys(fgRmMapping)?.length; i++) {
      const fgTenantProductId = Object.keys(fgRmMapping)[i];
      // if (Object.keys(fgRmMapping)?.length > 1) {
      finalFgRmList.push({
        key: uuidv4(),
        fg_product: selectedMO?.mo_finished_goods?.find((item) => item?.tenant_product_id == fgTenantProductId),
      });
      // }
      const fgRmProductList = fgRmMapping[fgTenantProductId];
      for (let j = 0; j < fgRmProductList?.length; j++) {
        finalFgRmList.push({
          ...fgRmProductList[j],
          key: uuidv4(),
          product_category_info: fgRmProductList[j]?.tenant_product_info?.product_category_info,
          sqn: j + 1,
        });
      }
    }

    for (let i = 0; i < selectedMO?.mo_finished_goods?.length; i++) {
      const fgTenantProductId = selectedMO?.mo_finished_goods[i]?.tenant_product_id || selectedMO?.mo_finished_goods[i]?.tenant_product_info?.tenant_product_id;
      if (!Object.keys(fgRmMapping).includes(fgTenantProductId.toString())) {
        finalFgRmList.push({
          key: uuidv4(),
          fg_product: selectedMO?.mo_finished_goods?.find((item) => item?.tenant_product_id == fgTenantProductId),
        });
      }
    }
    return finalFgRmList;
  }

  /**
   *
   * @return {*}
   */
  render() {
    const {
      getMOByIdLoading, selectedMO, handleDrawerState, callback, removeByProductMOLoading,
    } = this.props;
    const {
      byProductColumns, openAddRMDrawer,
      parentData,
      excludedProducts,
      showPRZModal,
      selectedDocumentId,
    } = this.state;
    return (
      <Fragment>
        <div className="bp-table-wrapper">
          <Table
            showHeader
            title={() => (
              <div className="bp-table-header">
                <div className="bp-table-header-title">{`${selectedMO?.by_product_lines?.length || 0} By Products`}</div>
                {/* {window.screen.width > 425 && selectedMO?.mo_finished_goods?.length === 1 && !selectedMO?.tenant_seller_id && (
                  <div style={{ marginLeft: 'auto' }}>
                    <div
                      className={`view-fg__action-button-inactive ${selectedMO?.status === 'IN_PROGRESS' ? 'view-fg__action-button' : ''}`}
                      onClick={() => {
                        if (selectedMO?.status === 'IN_PROGRESS') {
                          handleDrawerState(selectedMO?.mo_finished_goods[0]?.tenant_product_id);
                        }
                      }}
                    >
                      <div style={{ fontWeight: '500px' }}>+ Add Batch</div>
                    </div>
                  </div>
                )} */}
              </div>
            )}
            bordered
            size="small"
            columns={byProductColumns}
            dataSource={this.getDataSource(selectedMO?.by_product_lines)}
            loading={getMOByIdLoading || removeByProductMOLoading}
            pagination={false}
            scroll={{ x: true }}
            expandable={!['DRAFT', 'CONFIRMED', 'VOID'].includes(selectedMO?.status) ? {
              rowExpandable: (record) => record?.by_product_adjustments?.length,
              expandedRowRender: (record) => (
                <BatchSelector
                  title="Issued Stock"
                  batches={record?.by_product_adjustments}
                  uomInfo={record?.uom_info}
                  expiryDays={record?.tenant_product_info?.expiry_days}
                  showCancel
                  status={selectedMO?.status}
                  isContractOrder={selectedMO?.tenant_seller_id}
                />
              ),
              // expandIcon: ({ expanded, onExpand, record }) => (expanded ? (
              //   <FontAwesomeIcon icon={faCaretDown} className="table-expand-icon" onClick={(e) => onExpand(record, e)} />
              // ) : (
              //   <FontAwesomeIcon icon={faCaretRight} className="table-expand-icon" onClick={(e) => onExpand(record, e)} />
              // )),
            } : false}
            rowClassName="bp-table-row"
          />
        </div>

        <Drawer
          open={openAddRMDrawer}
          onClose={() => {
            this.setState({
              openAddRMDrawer: false,
              parentData: null,
              excludedProducts: [],
            });
          }}
          width="420px"
          destroyOnClose
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header" style={{ width: '370px' }}>
              <H3Text text="Add New By Product" className="custom-drawer__title" />
              <H3Image
                src={closeIcon}
                className="custom-drawer__close-icon"
                onClick={() => {
                  this.setState({
                    openAddRMDrawer: false,
                    parentData: null,
                    excludedProducts: [],
                  });
                }}
              />
            </div>
          </div>
          <AddRMForm
            callback={() => {
              this.setState({
                openAddRMDrawer: false,
                parentData: null,
                excludedProducts: [],
              });
              callback();
            }}
            parentData={parentData}
            selectedMO={selectedMO}
            excludedProducts={excludedProducts}
            enableAdvanceSearch
            isByProduct
          />
        </Drawer>
        <PRZModal
          isOpen={showPRZModal}
          onClose={() => {
            this.setState({ showPRZModal: false, selectedDocumentId: null });
          }}
          entityType="product"
          documentId={selectedDocumentId}
        />
      </Fragment>
    );
  }
}

const mapStateToProps = ({ UserReducers, MOReducers, RemoveByProductMO }) => ({
  selectedMO: MOReducers.selectedMO,
  getMOByIdLoading: MOReducers.getMOByIdLoading,
  user: UserReducers.user,
  removeByProductMOLoading: RemoveByProductMO.loading,
});

const mapDispatchToProps = (dispatch) => ({
  removeByProductMO: (payload, callback) => dispatch(RemoveByProductMOModule.actions.request(payload, callback)),
});

ByProductsLines.propTypes = {
  handleDrawerState: PropTypes.func,
  selectedMO: PropTypes.any,
  getMOByIdLoading: PropTypes.bool,
  user: PropTypes.any,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(ByProductsLines));
