// Packages
import React, { Component, Fragment } from 'react';
import { with<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { connect } from 'react-redux';
import Decimal from 'decimal.js';
import {
  Table, Select, DatePicker, notification, Radio, Upload, Checkbox, Popconfirm, Popover, Input,
} from 'antd';
import {
  CloseOutlined, InfoCircleOutlined, PlusCircleFilled, PlusOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';

// Helpers
import Constants, {
  QUANTITY, toISTDate, INFINITE_EXPIRY_DATE, isEqualWithTolerance,
} from '@Apis/constants';
import Helpers from '@Apis/helpers';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import FormHelpers from '@Helpers/FormHelpers';

// PRCZ Components
import H3Text from '@Uilib/h3Text';
import H3Button, { defaultButtonTypes } from '@Uilib/h3Button';

// Actions
import STActions from '@Actions/stActions';
import TenantActions from '@Actions/tenantActions';
import DepartmentActions from '@Actions/departmentActions';
import CFV2Actions from '@Actions/configurations/cfV2Actions';
import InventoryLocationActions from '@Actions/settings/inventoryLocationActions';
import ProductActions from '@Actions/productActions';

// Components
import CustomFieldV2 from '@Components/Common/CustomFieldV2';
import BulkUpload from '@Components/Common/BulkUpload';
import SelectDepartment from '@Components/Common/SelectDepartment';
import BatchSelector from '@Components/Inventory/Indent/CreateIndent/BatchSelector';
import ProductFilterV2 from '@Components/Common/ProductFilterV2';
import BarcodeReader from '@Components/Common/BarcodeReader';
import Crown from '@Images/crown2.png';
import CustomDocumentInputs from '@Components/Common/CustomDocumentInputs';
import CustomDocumentColumns from '@Components/Common/CustomDocumentColumns';
import DocConfigActions from '@Actions/docConfigActions';

import CustomFieldV3 from '@Components/Common/CustomFieldV3';
import RichTextEditor from '../../../Common/RichTextEditor';
import ProductCategoryLabel from '../../../Common/ProductCategoryLabel';
import { clearReservationsByLine, getBatchesWithReservation } from './helper';
import PRZSelect from '../../../Common/UI/PRZSelect';
import DocumentNumberSeqInput from '../../../Admin/Common/DocumentNumberSeqInput';
import ProductCodesAndName from '@Components/Common/ProductCodesAndName';
import './style.scss';

const { Option } = Select;
const uploadButton = (
  <div>
    <PlusOutlined />
    <div style={{ marginTop: 8 }}>Upload</div>
  </div>
);

class StockTransferForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      transferTypes: {
        1: 'SAME BUSINESS',
        2: 'OTHER BUSINESS',
      },
      transferType: 'SAME BUSINESS',
      transferTypeValue: 1,
      data: [
        {
          key: uuidv4(), asset: '', product_sku_name: '', availableQuantity: '', thresholdQuantity: '', quantity: null, lineCustomFields: [],
        },
      ],
      toRecipients: [],
      invalidRecipientAddress: false,
      searchKeyword: '',
      allStock: true,
      visibleColumns: {
        PRODUCT: {
          label: 'Product',
          visible: true,
          disabled: true,
        },
        AVAILABLE_QTY: {
          label: 'Available Qty',
          visible: true,
          disabled: true,
        },
        TRANSFER_QTY: {
          label: 'Transfer Qty',
          visible: true,
          disabled: true,
        },
        ON_HAND_QTY: {
          label: 'New On Hand Qty',
          visible: true,
          disabled: true,
        },
        LOCATION: {
          label: 'Location',
          visible: true,
          disabled: true,
        },
      },
    };
    this.customLineInputChange = this.customLineInputChange.bind(this);
  }

  componentDidMount() {
    const {
      user, getTenants, getStById, match, getDocCFV2, getInventoryLocations, getDocConfig,
    } = this.props;
    const { transferTypeValue } = this.state;
    const { stockTransferId } = match.params;
    if (stockTransferId) {
      getStById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.STOCK_TRANSFER, Helpers.permissionTypes.UPDATE).join(','), stockTransferId);
    } else {
      getInventoryLocations(Number(Helpers.getTenantDepartmentId(user)), '', null, true);
    }
    getTenants(1, '', true, user?.tenant_info?.org_id, 100, () => { });
    this.setState({ selectedDate: dayjs() });
    const payload = {
      orgId: user?.tenant_info?.org_id,
      entityName: 'STOCK_TRANSFER',
    };
    getDocCFV2(payload);
    if (transferTypeValue === 1) {
      getDocConfig(user?.tenant_info?.tenant_id, 'STOCK_TRANSFER');
    }
    sessionStorage.setItem('staticReservations', JSON.stringify({}));
  }

  componentWillUnmount() {
    const { getStByIdSuccess } = this.props;
    getStByIdSuccess(null);
  }

  static getDerivedStateFromProps(props, state) {
    if (props.cfV2DocStockTransfer && !state.isUserReadyOne) {
      return {
        ...state,
        cfStockTransferDoc: CustomFieldHelpers.getCfStructure(props.cfV2DocStockTransfer?.data?.document_custom_fields, true) || [],
        cfStockTransferLine: CustomFieldHelpers.getCfStructure(props.cfV2DocStockTransfer?.data?.document_line_custom_fields, true) || [],
        visibleColumns: CustomFieldHelpers.updateVisibleColumns(props.cfV2DocStockTransfer?.data?.document_line_custom_fields, state.visibleColumns),
        data: state.data?.map((item) => ({ ...item, lineCustomFields: CustomFieldHelpers.mergeCustomFields(props.cfV2DocStockTransfer?.data?.document_line_custom_fields, []) })),
        isUserReadyOne: true,
      };
    }
    const {
      selectedSt,
    } = props;

    //  stock transfer update case
    if (selectedSt && props.cfV2DocStockTransfer && props?.match?.params?.stockTransferId && !state.isUserReady) {
      // Already Used Custom  Fields
      const oldCustomField = selectedSt?.custom_fields || [];
      const oldLineCustomField = props?.selectedPurchaseOrder?.stock_transfer_lines[0]?.st_line_custom_fields ?? [];

      props.getInventoryLocations(Number(selectedSt?.destination_tenant_department_id), '', null, true);
      const stockTransferLines = [];
      for (let i = 0; i < selectedSt?.stock_transfer_lines?.length; i++) {
        const stockTransferLine = selectedSt?.stock_transfer_lines[i];
        const key = uuidv4();

        const convertQtyToInventoryUomQty = (qty, line) => (Number(qty) * Number(line?.uom_info?.ratio)) / Number(line?.product_sku_info?.uom_info?.ratio);
        stockTransferLines.push({
          key,
          secondaryUomUqc: stockTransferLine?.product_sku_info?.secondary_uom_info?.uqc,
          secondaryAvailableQty: stockTransferLine?.product_sku_info?.secondary_available_qty,
          secondaryUomId: stockTransferLine?.product_sku_info?.secondary_uom_id,
          secondary_uom_qty: stockTransferLine?.secondary_uom_qty,
          product_sku_name: stockTransferLine?.product_sku_info?.product_sku_name,
          ref_product_code: stockTransferLine?.product_sku_info?.ref_product_code,
          thresholdQuantity: stockTransferLine?.product_sku_info?.threshold_qty,
          unit: stockTransferLine?.product_sku_info?.unit,
          quantity: convertQtyToInventoryUomQty(stockTransferLine?.quantity, stockTransferLine),
          tenantProductId: stockTransferLine?.tenant_product_id,
          stock_transfer_lines: stockTransferLine?.product_sku_info?.stock_transfer_lines,
          availableQuantity: Helpers.getValueTotalInObject(stockTransferLine?.available_batches, 'available_qty'),
          stock_transfer_line_id: stockTransferLine?.stock_transfer_line_id,
          sku: stockTransferLine?.tenant_product_id,
          product_sku_info: stockTransferLine?.product_sku_info,
          product_sku_id: stockTransferLine?.product_sku_info?.product_sku_id,
          uom_info: stockTransferLine?.product_sku_info?.uom_info,
          expiry_days: stockTransferLine?.product_sku_info?.expiry_days,
          remarks: stockTransferLine?.remarks,
          product_batches: getBatchesWithReservation(Helpers.batchSelectionMethod(stockTransferLine?.default_outwards_method || Helpers.getBatchMethod(stockTransferLine), stockTransferLine?.available_batches, stockTransferLine?.quantity), convertQtyToInventoryUomQty(stockTransferLine?.quantity, stockTransferLine), { ...stockTransferLine, uom_info: stockTransferLine?.product_sku_info?.uom_info }, true, key, true),
          manufacturingDateFormat: stockTransferLine?.product_sku_info?.manufacturing_date_format,
          expiryDateFormat: stockTransferLine?.product_sku_info?.expiry_date_format,
          inventory_location_id: stockTransferLine?.source_product_batches?.[0]?.destination_inventory_location_id,
          batchConsumptionMethod: stockTransferLine?.default_outwards_method || Helpers.getBatchMethod(stockTransferLine),
          lineCustomFields: CustomFieldHelpers.mergeCustomFields(props.cfV2DocStockTransfer?.data?.document_line_custom_fields, stockTransferLine?.st_line_custom_fields || []),
          product_category_info: stockTransferLine?.product_category_info || stockTransferLine?.product_sku_info?.product_category_info,
        });
      }
      return {
        ...state,
        isUserReady: true,
        stNumber: selectedSt?.stock_transfer_number,
        sourceDepartment: selectedSt?.source_department_info?.department_id,
        destinationDepartment: selectedSt?.destination_department_info?.department_id,
        destinationTenant: selectedSt?.destination_department_info?.tenant_id,
        sourceDepartmentId: selectedSt?.source_tenant_department_id,
        destinationDepartmentId: Number(selectedSt?.destination_tenant_department_id),
        selectedDate: dayjs(selectedSt?.stock_transfer_date),
        remark: selectedSt?.remark || ' ',
        data: stockTransferLines || [],
        stockTransferId: selectedSt?.stock_transfer_id,
        fileList: selectedSt?.attachments,
        selectedSt,
        cfStockTransferDoc: CustomFieldHelpers.mergeCustomFields(props.cfV2DocStockTransfer?.data?.document_custom_fields, oldCustomField),
        cfStockTransferLine: CustomFieldHelpers.mergeCustomFields(props.cfV2DocStockTransfer?.data?.document_line_custom_fields, oldLineCustomField),
        transferTypeValue: selectedSt?.source_department_info?.tenant_id === selectedSt?.destination_department_info?.tenant_id ? 1 : 2,
        toRecipients: selectedSt?.notification_recipients || [],
        checkedRecipients: !!(selectedSt?.notification_recipients?.length > 0),
        terms: selectedSt?.t_and_c || '',
      };
    }
    if (state.transferTypeValue === 1 && props?.user?.tenant_info?.tenant_id && !state?.sourceTenant) {
      return {
        ...state,
        sourceTenant: Number(props?.user?.tenant_info?.tenant_id),
        sourceDepartment: Helpers.getDepartmentId(props?.user),
        sourceDepartmentId: Helpers.getTenantDepartmentId(props?.user),
      };
    }
    if (state.transferTypeValue === 1 && !state.terms && !selectedSt) {
      if (props.docConfigStockTransfer) {
        const termsData = props.docConfigStockTransfer?.data?.find((item) => item?.document_type === 'STOCK_TRANSFER_DOWNLOAD')?.allowed_fields?.find((field) => field?.field_name === 'default_t_and_c')?.field_value;
        return {
          ...state,
          terms: termsData,
        };
      }
    }
    return {
      ...state,
    };
  }

  getBatchesForUpload(productBatches, quantity) {
    const batches = productBatches;
    let remainingQty = quantity;
    for (let j = 0; j < batches?.length; j++) {
      batches[j].consumed_qty = 0;
      batches[j].batch_in_use = true;
    }
    for (let i = 0; i < batches?.length; i++) {
      if (remainingQty > 0 && Number(batches[i].consumed_qty || '0') < batches?.[i]?.available_qty) {
        batches[i].consumed_qty = batches?.[i]?.available_qty > remainingQty ? remainingQty : batches?.[i]?.available_qty;
        remainingQty -= batches[i].consumed_qty;
      } else {
        batches[i].consumed_qty = 0;
        remainingQty -= batches[i].consumed_qty;
      }
    }
    return batches;
  }

  getBatchMethod = (tenantSku) => {
    let batchMethod;

    if (tenantSku?.default_outwards_method) {
      batchMethod = tenantSku?.default_outwards_method;
    } else if (tenantSku?.expiry_days > 0) {
      batchMethod = 'FEFO';
    } else {
      batchMethod = 'FIFO';
    }

    return batchMethod;
  }

  onBulkUpload(updatedData, importedData) {
    const finalData = updatedData?.map((item) => ({
      key: uuidv4(),
      product_sku_info: { product_sku_id: item.product_sku_id, product_sku_name: item?.product_sku_name, internal_sku_code: item?.internal_sku_code },
      tenant_product_id: item?.tenant_product?.[0]?.tenant_product_id,
      tenantProductId: item?.tenant_product?.[0]?.tenant_product_id,
      product_sku_id: item.product_sku_id,
      product_sku_name: item?.product_sku_name,
      quantity: importedData.find((i) => i?.sku_id === item?.internal_sku_code)?.quantity,
      manufacturingDateFormat: item?.manufacturing_date_format,
      expiryDateFormat: item?.expiry_date_format,
      availableQuantity: Helpers.getUploadedBatches(item?.tenant_product, importedData.find((i) => i?.sku_id === item?.internal_sku_code)?.quantity, this.getBatchesForUpload) ? Helpers.getUploadedBatches(item?.tenant_product, importedData.find((i) => i?.sku_id === item?.internal_sku_code)?.quantity, this.getBatchesForUpload).reduce((acc, cur) => acc + cur.available_qty, 0) : 0,
      batchConsumptionMethod: item?.default_outwards_method || this.getBatchMethod(item),
      product_batches: this.getBatchesForUpload(Helpers.batchSelectionMethod(item?.default_outwards_method || this.getBatchMethod(item), item?.tenant_product[0]?.product_batches, importedData.find((i) => i?.sku_id === item?.internal_sku_code)?.quantity), importedData.find((i) => i?.sku_id === item?.internal_sku_code)?.quantity),
      uom_info: item.uom_info,
      expiry_days: item?.expiry_days,
      expiryDate: item?.expiry_days >= 0 ? (toISTDate(dayjs()).add(item?.expiry_days || 0, 'day')).format('YYYY-MM-DD') : INFINITE_EXPIRY_DATE,
    }));
    this.setState({ data: finalData });
  }

  handleCreateSt = (withApproval) => {
    const {
      createSt, updateSt, history, match, callback, selectedST, tenants,
    } = this.props;
    const {
      data, selectedDate, stockTransferId, remark, sourceDepartmentId, destinationDepartmentId, sourceDepartment, destinationDepartment, fileList, transferTypeValue, destinationTenant, cfStockTransferDoc, toRecipients, checkedRecipients, terms, initialStNumber, stNumber, docSeqId,
    } = this.state;
    this.setState({ formSubmitted: true });
    const destinationDptId = tenants?.data?.filter((item) => item?.tenant_id === destinationTenant)?.[0]?.default_store_id;
    const getStBatches = (batches, sourceTenantProductId, destTenantProductId, inventoryLocationId) => {
      const updatedBatches = [];
      for (let i = 0; i < batches?.length; i++) {
        const newBatch = {
          ...batches[i],
          quantity: batches[i]?.consumed_qty,
          is_rejected_batch: !!batches[i]?.is_rejected_batch,
          source_tenant_product_id: sourceTenantProductId,
          destination_tenant_product_id: destTenantProductId,
          source_tenant_department_id: sourceDepartmentId,
          destination_inventory_location_id: inventoryLocationId,
          destination_tenant_department_id: transferTypeValue === 1 ? destinationDepartmentId : destinationDptId,
        };
        delete newBatch.tenant_department_id;
        delete newBatch.tenant_product_id;
        updatedBatches.push(newBatch);
      }
      return updatedBatches;
    };

    if (this.isDataValid() && selectedDate && (checkedRecipients ? toRecipients?.length : true) && sourceDepartment && (transferTypeValue === 1 ? destinationDepartment : destinationTenant) && !cfStockTransferDoc?.filter((customField) => customField.isActive && customField.isRequired && (customField?.fieldType === 'ATTACHMENT' ? !customField?.fieldValue?.length : !customField?.fieldValue))?.length && stNumber) {
      if (match?.params?.stockTransferId) {
        const stockTransferLines = [];
        for (let i = 0; i < data?.length; i++) {
          stockTransferLines.push({
            remarks: data[i]?.remarks,
            stock_transfer_line_id: data[i]?.stock_transfer_line_id,
            tenant_product_id: data[i]?.tenantProductId,
            quantity: data[i]?.quantity,
            secondary_uom_qty: data[i]?.secondaryUomId ? data[i]?.secondary_uom_qty : 0,
            uom_id: data[i]?.uom_info?.uom_id,
            uom_info: data[i]?.uom_info,
            inventory_location_id: data[i]?.inventory_location_id,
            source_product_batches: getStBatches(
              data[i]?.product_batches
                ?.filter((item) => item?.consumed_qty > 0)
                ?.map((batch) => ({
                  ...batch,
                  manufacturing_date: batch?.manufacturing_date ? FormHelpers.dateFormatter(batch?.manufacturing_date, data[i]?.manufacturingDateFormat) : null,
                  expiry_date: batch?.expiry_date ? FormHelpers.dateFormatter(batch?.expiry_date, data[i]?.expiryDateFormat) : null,
                })),
              data[i]?.tenantProductId,
              null,
              data[i]?.inventory_location_id,
            ),
            default_outwards_method: data[i]?.batchConsumptionMethod,
            custom_fields: CustomFieldHelpers.postCfStructure(data[i]?.lineCustomFields),
          });
        }
        const payload = {
          source_tenant_department_id: sourceDepartmentId,
          destination_tenant_department_id: transferTypeValue === 1 ? destinationDepartmentId : destinationDptId,
          stock_transfer_date: selectedDate,
          remark,
          custom_fields: CustomFieldHelpers.postCfStructure(cfStockTransferDoc),
          status: withApproval ? 'ISSUED' : 'DRAFT',
          stock_transfer_lines: stockTransferLines,
          stock_transfer_id: stockTransferId,
          attachments: fileList?.map((attachment) => ({
            url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
          })) || [],
          notification_recipients: toRecipients,
          t_and_c: global.isBlankString(terms) ? '' : terms,
          stock_transfer_number: initialStNumber?.toLowerCase()?.trim() === stNumber?.toLowerCase()?.trim() ? null : stNumber,
        };
        updateSt(payload, () => {
          this.setState({ currentAction: '' });
          if (selectedST) {
            callback();
          } else {
            history.push(`/inventory/stock-transfer/view/${stockTransferId}`);
          }
        }, withApproval);
      } else {
        const stockTransferLines = [];
        for (let i = 0; i < data?.length; i++) {
          stockTransferLines.push({
            remarks: data[i]?.remarks,
            tenant_product_id: data[i]?.tenantProductId,
            quantity: data[i]?.quantity,
            secondary_uom_qty: data[i]?.secondaryUomId ? data[i]?.secondary_uom_qty : 0,
            uom_id: data[i]?.uom_info?.uom_id,
            uom_info: data[i]?.uom_info,
            source_product_batches: getStBatches(
              data[i]?.product_batches
                ?.filter((item) => item?.consumed_qty > 0)
                ?.map((batch) => ({
                  ...batch,
                  manufacturing_date: FormHelpers.dateFormatter(batch?.manufacturing_date, data[i]?.manufacturingDateFormat),
                  expiry_date: FormHelpers.dateFormatter(batch?.expiry_date, data[i]?.expiryDateFormat),
                })),
              data[i]?.tenantProductId,
              null,
              data[i]?.inventory_location_id,
            ),
            default_outwards_method: data[i]?.batchConsumptionMethod,
            custom_fields: CustomFieldHelpers.postCfStructure(data[i]?.lineCustomFields),
          });
        }
        const payload = {
          source_tenant_department_id: sourceDepartmentId,
          destination_tenant_department_id: transferTypeValue === 1 ? destinationDepartmentId : destinationDptId,
          stock_transfer_date: selectedDate,
          custom_fields: CustomFieldHelpers.postCfStructure(cfStockTransferDoc),
          remark,
          status: withApproval ? 'ISSUED' : 'DRAFT',
          stock_transfer_lines: stockTransferLines,
          attachments: fileList?.map((attachment) => ({
            url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
          })) || [],
          notification_recipients: checkedRecipients ? toRecipients : [],
          t_and_c: global.isBlankString(terms) ? '' : terms,
          stock_transfer_number: initialStNumber?.toLowerCase()?.trim() === stNumber?.toLowerCase()?.trim() ? null : stNumber,
          seq_id: docSeqId || null,
        };
        createSt(payload, () => {
          this.setState({ currentAction: '' });
          if (selectedST) {
            callback();
          } else {
            history.push('/inventory/stock-transfer');
          }
        }, withApproval);
      }
    } else {
      this.setState({ currentAction: '' });
      notification.open({
        type: 'error',
        message: 'Please provide all required inputs.',
        duration: 4,
        placement: 'top',
      });
    }
  }

  handleProductChangeValue = (value, key) => {
    const { data } = this.state;
    const copyData = JSON.parse(JSON.stringify(data));
    for (let i = 0; i < copyData.length; i++) {
      if (copyData[i].key === key) {
        const copyDataItem = copyData[i];
        copyDataItem.product_sku_name = value;
        copyData[i] = copyDataItem;
      }
    }
    this.setState({ data: copyData });
  }

  getBatchMethod = (tenantSku) => {
    let batchMethod;

    if (tenantSku?.default_outwards_method) {
      batchMethod = tenantSku?.default_outwards_method;
    } else if (tenantSku?.product_info?.expiry_days > 0) {
      batchMethod = 'FEFO';
    } else {
      batchMethod = 'FIFO';
    }

    return batchMethod;
  }

  createData = ({
    dataItem, tenantSku, productData, autoPrintDescription, cfStockTransferLine,
  }) => {
    const copyDataItem = dataItem;
    const batches = tenantSku?.product_batches;
    if (tenantSku?.barcode_batch_id) {
      for (let j = 0; j < batches?.length; j++) {
        if (tenantSku?.barcode_batch_id === batches[j]?.batch_id) {
          batches[j].consumed_qty = 1;
          batches[j].batch_in_use = true;
        }
      }
    } else {
      for (let j = 0; j < batches?.length; j++) {
        batches[j].consumed_qty = 0;
        batches[j].batch_in_use = true;
      }
    }
    copyDataItem.secondaryUomUqc = tenantSku?.secondary_uom_info?.uqc;
    copyDataItem.secondaryAvailableQty = tenantSku?.secondary_available_qty;
    copyDataItem.secondaryUomId = tenantSku?.secondary_uom_id;
    copyDataItem.secondary_uom_qty = 0;
    copyDataItem.product_sku_name = tenantSku?.product_info?.product_sku_name;
    copyDataItem.otherDepartmentStock = productData?.tenant_products;
    copyDataItem.product_sku_id = tenantSku?.product_info?.product_sku_id;
    copyDataItem.availableQuantity = Number(tenantSku?.available_qty || '0');
    copyDataItem.thresholdQuantity = `${tenantSku?.threshold_qty}`;
    copyDataItem.tenantProductId = `${tenantSku?.tenant_product_id}`;
    copyDataItem.asset = tenantSku?.product_info?.assets ? tenantSku?.product_info?.assets : [];
    copyDataItem.product_batches = Helpers.batchSelectionMethod(this.getBatchMethod(tenantSku), batches);
    copyDataItem.batchConsumptionMethod = this.getBatchMethod(tenantSku);
    copyDataItem.manufacturingDateFormat = tenantSku?.product_info?.manufacturing_date_format;
    copyDataItem.expiryDateFormat = tenantSku?.product_info?.expiry_date_format;
    copyDataItem.expiry_days = tenantSku?.product_info?.expiry_days;
    copyDataItem.unit = tenantSku?.product_info?.unit;
    copyDataItem.expiry_days = tenantSku?.product_info?.expiry_days;
    copyDataItem.uom_info = tenantSku?.uom_info;
    copyDataItem.product_sku_info = tenantSku?.product_info;
    copyDataItem.internal_sku_code = tenantSku?.product_info?.internal_sku_code;
    copyDataItem.ref_product_code = tenantSku?.product_info?.ref_product_code;
    copyDataItem.quantity = tenantSku?.barcode_batch_id ? 1 : 0;
    copyDataItem.remarks = autoPrintDescription ? (productData?.description || '')?.replace(/<[^>]+>/g, '') : '';
    copyDataItem.sku = tenantSku.product_info.product_sku_id ? tenantSku.product_info.product_sku_id : '';
    copyDataItem.lineCustomFields = FormHelpers.lineSystemFieldValue(
      cfStockTransferLine,
      [...(tenantSku?.custom_fields ?? []), ...(productData?.system_custom_fields ?? [])],
    );
    copyDataItem.product_category_info = tenantSku?.product_category_info;
    return copyDataItem;
  }

  handleProductChange = (tenantSku, key, productData, isMultiMode, callback) => {
    const { data, cfStockTransferLine } = this.state;
    const { user } = this.props;
    const autoPrintDescription = user?.tenant_info?.global_config?.settings?.print_description_automatically;

    if (isMultiMode && Array.isArray(tenantSku)) {
      const copyData = [];

      tenantSku.forEach((tenantSkuData) => {
        const productSkuData = productData?.find((item) => item?.product_sku_id === tenantSkuData?.product_sku_id);

        const newData = {
          key: uuidv4(),
          lineCustomFields: cfStockTransferLine,
        };

        const copyDataItem = this.createData({
          dataItem: newData,
          tenantSku: tenantSkuData,
          productData: productSkuData,
          autoPrintDescription,
          cfStockTransferLine,
        });

        copyData.push(copyDataItem);
      });

      if (callback) {
        callback();
      }

      this.setState((prevState) => ({
        data: [...prevState.data, ...copyData],
      }));
    } else {
      const copyData = JSON.parse(JSON.stringify(data));
      for (let i = 0; i < copyData.length; i++) {
        if (copyData[i].key === key) {
          const copyDataItem = this.createData({
            dataItem: copyData[i],
            tenantSku,
            productData,
            autoPrintDescription,
            cfStockTransferLine,
          });
          copyData[i] = copyDataItem;
        }
      }
      this.setState({ data: copyData });
    }
  }

  handleMultiProductChange = (tenantSku, key, productData, callback) => {
    this.handleProductChange(tenantSku, key, productData, true, callback);
  }

  handleDelete(key) {
    const { data, cfStockTransferLine } = this.state;
    if (data.length === 1) {
      this.setState({
        data: [{
          key: uuidv4(), asset: '', product_name: '', availableQuantity: '', thresholdQuantity: '', quantity: null, lineCustomFields: cfStockTransferLine,
        }],
      });
    } else {
      const copyData = data.filter((item) => item.key !== key);
      this.setState({ data: copyData });
    }
  }

  handleChangeTextArea = (html) => {
    this.setState({ terms: html });
  }

  isDataValid = () => {
    const { data, transferTypeValue, allStock } = this.state;
    const { user } = this.props;
    let isDataValid = true;
    const isWarehouseLocationRequired = user?.tenant_info?.inventory_config?.settings?.is_inventory_location_mandatory;

    const staticReservations = JSON.parse(sessionStorage.getItem('staticReservations'));

    for (let i = 0; i < data.length; i++) {
      const customFieldsValid = CustomFieldHelpers.isCfValid(data[i]?.lineCustomFields);

      let availableQty = 0;
      let availableBatchQty = {};

      availableQty = Helpers.getValueTotalInObject(data[i]?.product_batches?.filter((item) => (item?.batch_in_use && (allStock ? !item?.is_rejected_batch : true))), 'available_qty');

      const getEntries = (obj) => (obj ? Object.entries(obj) : []);

      const batchIdsToSum = data[i]?.product_batches?.filter((item) => (item?.batch_in_use && (allStock ? !item?.is_rejected_batch : true)))?.map((item) => item?.batch_id) || [];

      const valuesToSum = batchIdsToSum?.flatMap((batchId) => getEntries(staticReservations[batchId])
        .filter(([lineId, value]) => lineId !== data[i]?.key)
        .map(([lineId, value]) => value));

      const sum = valuesToSum.reduce((acc, value) => acc + value, 0);
      availableBatchQty = availableQty - sum;

      const quantityGreaterThanAvailableQty = (availableBatchQty - data[i].quantity) < 0;

      if (!Number(data[i].quantity) || !data[i].product_sku_name
        || !isEqualWithTolerance(Helpers.getValueTotalInObject(data[i]?.product_batches?.filter((item) => (item?.batch_in_use && (allStock ? !item?.is_rejected_batch : true))), 'consumed_qty'), Number(data[i]?.quantity), data[i]?.uom_info?.precision)
        || quantityGreaterThanAvailableQty
        || (transferTypeValue === 1 ? (isWarehouseLocationRequired && !data[i]?.inventory_location_id) : false)
        || !customFieldsValid
      ) {
        isDataValid = false;
      }
    }
    return isDataValid;
  }

  addNewRow(callback) {
    const { data, cfStockTransferLine } = this.state;
    const copyData = JSON.parse(JSON.stringify(data));
    const key = uuidv4();
    copyData.push({
      key,
      asset: '',
      product_sku_name: '',
      availableQuantity: '',
      thresholdQuantity: '',
      quantity: 0,
      lineCustomFields: cfStockTransferLine,
    });
    this.setState({ data: copyData }, () => {
      if (callback) {
        callback(key);
      }
    });
  }

  getDataSource(data) {
    const copyData = JSON.parse(JSON.stringify(data));
    return [...copyData?.filter((item) => item?.product_sku_id), ...copyData?.filter((item) => !item?.product_sku_id)];
  }

  customInputChange(fieldValue, cfId) {
    const { cfStockTransferDoc } = this.state;
    const newCustomField = cfStockTransferDoc.map((customField) => {
      if (customField?.cfId === cfId) {
        if (customField?.fieldType === 'ATTACHMENT') {
          return {
            ...customField,
            fieldValue: fieldValue?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
            })),

          };
        }
        return {
          ...customField,
          fieldValue,
        };
      }
      return {
        ...customField,
      };
    });
    this.setState({
      cfStockTransferDoc: newCustomField,
    });
  }

  customLineInputChange(fieldValue, key) {
    const { data } = this.state;
    const copyData = JSON.parse(JSON.stringify(data));
    for (let i = 0; i < copyData.length; i++) {
      if (copyData[i]?.key === key) {
        copyData[i].lineCustomFields = fieldValue;
      }
    }

    this.setState({ data: copyData });
  }

  toggleBatch(record, adjustmentRow) {
    const { data } = this.state;
    for (let i = 0; i < data?.length; i++) {
      if (data[i]?.key === adjustmentRow?.key) {
        const rowBatches = data[i]?.product_batches;
        for (let j = 0; j < rowBatches?.length; j++) {
          if (rowBatches[j]?.batch_id === record?.batch_id) {
            rowBatches[j].batch_in_use = !rowBatches[j]?.batch_in_use;
            // data[i].quantity -= rowBatches[j].consumed_qty;
            // rowBatches[j].consumed_qty = 0;
            const rowQty = new Decimal(rowBatches[j]?.consumed_qty || 0);
            const dataQty = new Decimal(data[i]?.quantity || 0);
            data[i].quantity = dataQty.minus(rowQty).toNumber();
            rowBatches[j].consumed_qty = 0;
            clearReservationsByLine(data[i]?.key);
          }
        }
        break;
      }
    }
    this.setState({ data });
  }

  getColumns() {
    const { user, selectedSt } = this.props;
    const {
      cfStockTransferLine, transferTypeValue, visibleColumns, formSubmitted, data,
    } = this.state;
    const enabledWarehouseLocations = user?.tenant_info?.inventory_config?.settings?.enable_location_based_stock;
    const isSecondaryUomEnabled = user?.tenant_info?.global_config?.settings?.enable_secondary_uom;

    const enableInternalRefCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_ref_code;
    const enableInternalSKUCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_sku_code;

    const productListColumns = [
      {
        title: 'PRODUCT',
        width: '260px',
        fixed: 'left',
        render: (text, record) => {
          const {
            formSubmitted, sourceDepartmentId, data,
          } = this.state;
          return (
            <div className="table__product-name">
              {record?.product_sku_id ? (
                <React.Fragment>
                  <div onClick={() => window.open(`/inventory/product/view/${record?.product_sku_info?.product_sku_id}`, '_blank')}>
                    <ProductCodesAndName
                      skuCode={record?.product_sku_info?.internal_sku_code}
                      refCode={record?.ref_product_code}
                      name={record?.product_sku_name}
                      showSku={enableInternalSKUCode}
                      showRefCode={enableInternalRefCode}
                    />
                  </div>
                  {record?.product_category_info
                    && record?.product_category_info?.category_path?.length > 0 && (
                      <ProductCategoryLabel
                        categoryPath={record?.product_category_info?.category_path}
                        categoryName={record?.product_category_info?.category_path?.at(
                          -1,
                        )}
                        containerStyle={{
                          width: 'fit-content',
                        }}
                      />
                    )}
                  <textarea
                    value={record.remarks}
                    onChange={(e) => {
                      const copyData = JSON.parse(JSON.stringify(data));
                      const updatedData = copyData.map((obj) => {
                        if (obj.key === record.key) {
                          return { ...obj, remarks: e.target.value };
                        }
                        return obj;
                      });
                      this.setState({
                        data: updatedData,
                      });
                    }}
                    className="invoice-table__remarks"
                    placeholder="Add description of this item.."
                  />
                </React.Fragment>
              ) : (
                <ProductFilterV2
                  record={record}
                  productTypes={['STORABLE']}
                  handleProductChangeValue={this.handleProductChangeValue}
                  handleProductChange={this.handleProductChange}
                  required
                  showError={formSubmitted && !record.product_sku_name}
                  onClear={(key) => {
                    const copyData = JSON.parse(JSON.stringify(data));
                    const updatedData = copyData.map((item) => {
                      if (item.key === key) {
                        return {
                          ...item,
                          tenant_product_id: null,
                          product_sku_info: null,
                          product_sku_id: null,
                          tenantProductId: null,
                          product_sku_name: '',
                          asset: '',
                          unit: null,
                          quantity: 0,
                          availableQuantity: null,
                          sku: null,
                          product_batches: null,
                          uom_info: null,
                        };
                      }
                      return {
                        ...item,
                      };
                    });
                    this.setState({ data: updatedData });
                  }}
                  disabled={!sourceDepartmentId || record?.product_sku_id}
                  excludedProducts={data.map((item) => (typeof item?.tenantProductId === 'string'
                    ? item?.tenantProductId
                    : JSON.stringify(item?.tenantProductId)))}
                  tenantDepartmentId={sourceDepartmentId}
                  excludeOutOfStock
                  showClear
                  filterReservedQuantity
                  selectedTenant={
                    selectedSt
                      ? selectedSt?.source_department_info?.tenant_id
                      : user?.tenant_info?.tenant_id
                  }
                  enableAdvanceSearch
                  handleMultiProductChange={this.handleMultiProductChange}
                />
              )}
            </div>
          );
        },
        visible: visibleColumns?.PRODUCT?.visible,
      },
      {
        title: 'AVAILABLE QTY',
        width: '150px',
        render: (text, record) => {
          const { allStock, sourceTenant, sourceDepartmentId } = this.state;
          const tenantProduct = record?.otherDepartmentStock?.filter((item) => item?.tenant_id === sourceTenant)?.[0];
          const filteredDepartmentsStock = tenantProduct?.available_stock?.filter((item) => item?.tenant_department_id !== sourceDepartmentId);
          const totalStockInOtherDepartments = filteredDepartmentsStock?.map((dept) => Helpers.getValueTotalInObject(dept?.batches, 'available_qty')).reduce((acc, cur) => acc + cur, 0);
          const hoverContent = (
            <div>
              {filteredDepartmentsStock?.map((dept) => (
                <div key={dept.tenant_department_id} className="other-uom-stock">
                  <H3Text text={dept?.department_name} />
                  <H3Text
                    text={`${QUANTITY(Helpers.getValueTotalInObject(dept?.batches, 'available_qty'))} ${record?.uom_info?.uqc?.toProperCase()}`}
                    className="tenant-inventory-slider__dept-qty"
                  />
                </div>
              ))}
            </div>
          );
          return (
            <React.Fragment>
              {`${QUANTITY(Helpers.getValueTotalInObject(record?.product_batches?.filter((item) => (item?.batch_in_use && (allStock ? !item?.is_rejected_batch : true))), 'available_qty'), record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase() || ''} `}
              {(Helpers.getValueTotalInObject(record?.product_batches?.filter((item) => item?.batch_in_use), 'available_qty') && record?.uom_info) ? (
                <div
                  className="table-subscript"
                >
                  {`In ${record?.product_batches?.filter((item) => item?.batch_in_use)?.length || '0'} Batches`}

                </div>
              ) : ''}
              {totalStockInOtherDepartments > 0 && (
                <Popover
                  placement="bottom"
                  title="Stocks in other locations"
                  content={hoverContent}
                  trigger="hover"
                >

                  <div
                    className="table-subscript"
                    style={{
                      color: 'rgb(109 107 106)',
                    }}
                  >
                    {`+${QUANTITY(totalStockInOtherDepartments, record?.uom_info?.precision)}  ${record?.uom_info?.uqc?.toProperCase() || ''} in other locations`}
                    <InfoCircleOutlined style={{
                      color: 'rgb(45, 124, 246)',
                      margin: '2px',
                    }}
                    />
                  </div>
                </Popover>
              )}
              <div
                style={{
                  marginTop: '8px',
                }}
              >
                {(record?.tenant_product_id || record?.secondaryAvailableQty) && (
                  <Fragment>
                    {`${(record?.secondaryAvailableQty || 'N/A')} ${record?.secondaryUomUqc?.toProperCase() || ''}`}
                    {' '}
                    (Secondary)
                  </Fragment>
                )}
              </div>
            </React.Fragment>
          );
        },
        visible: visibleColumns?.AVAILABLE_QTY?.visible,
      },
      {
        title: 'TRANSFER QTY',
        width: '160px',
        render: (text, record) => {
          const { data, allStock, formSubmitted } = this.state;

          // const copyData = JSON.parse(JSON.stringify(data));

          let availableQty = 0;
          let availableBatchQty = {};

          availableQty = Helpers.getValueTotalInObject(record?.product_batches?.filter((item) => (item?.batch_in_use && (allStock ? !item?.is_rejected_batch : true))), 'available_qty');

          const getEntries = (obj) => (obj ? Object.entries(obj) : []);

          const staticReservations = JSON.parse(sessionStorage.getItem('staticReservations'));

          const batchIdsToSum = record?.product_batches?.filter((item) => (item?.batch_in_use && (allStock ? !item?.is_rejected_batch : true)))?.map((item) => item?.batch_id) || [];

          const valuesToSum = batchIdsToSum?.flatMap((batchId) => getEntries(staticReservations[batchId])
            .filter(([lineId, value]) => lineId !== record?.key)
            .map(([lineId, value]) => value));

          // const sum = valuesToSum.reduce((acc, value) => acc + value, 0);

          // availableBatchQty = availableQty - sum;
          const sum = valuesToSum.reduce((acc, value) => acc.plus(new Decimal(value || 0)), new Decimal(0));
          availableBatchQty = new Decimal(availableQty).minus(sum);

          const handleChange = (event) => {
            const newQuantity = event.target.value;

            if (/^-?\d*\.?\d*$/.test(newQuantity)) {
              const copyData = JSON.parse(JSON.stringify(data));
              const batches = JSON.parse(JSON.stringify(record.product_batches));

              const pBatches = getBatchesWithReservation(batches, newQuantity, record, allStock, record.key);

              const updatedData = copyData?.map((item) => {
                if (item?.key === record.key) {
                  item.quantity = newQuantity;
                  item.product_batches = pBatches;
                }
                return item;
              });
              this.setState({
                data: updatedData,
              });
            }
          };

          return (
            <Fragment>
              <div className={`st-qty-input ${formSubmitted && (!record?.quantity || Number(record?.quantity) <= 0) ? 'form-error__input' : ''}`}>
                <Input
                  value={record.quantity}
                  type="text"
                  disabled={!record?.tenantProductId}
                  onWheel={(event) => event.target.blur()}
                  onChange={handleChange}
                  className="orgFormInput"
                  style={{
                    width: '110px',
                  }}
                />
                <div className="st-qty-input-uom">{record?.uom_info?.uqc?.toProperCase()}</div>
              </div>
              {
                 ((Number(availableBatchQty) < Number(record?.quantity)))
                && (
                  <div className="input-error">
                    *Please enter a valid quantity
                  </div>
                )
              }
              &nbsp;
              {isSecondaryUomEnabled && (
                <div className="st-qty-input">
                  {/* <span className="uom-label">Secondary</span> */}
                  <Input
                    value={record.secondary_uom_qty}
                    type="text"
                    disabled={!record?.tenantProductId || !record?.secondaryUomUqc}
                    onWheel={(event) => event.target.blur()}
                    style={{
                      width: '110px',
                    }}
                    onChange={(event) => {
                      const newQuantity = event.target.value;

                      if (/^-?\d*\.?\d*$/.test(newQuantity)) {
                        const copyData = JSON.parse(JSON.stringify(data));

                        const updatedData = copyData?.map((item) => {
                          if (item?.key === record.key) {
                            item.secondary_uom_qty = newQuantity;
                          }
                          return item;
                        });
                        this.setState({
                          data: updatedData,
                        });
                      }
                    }}
                    className="orgFormInput"
                  />
                  <div className="st-qty-input-uom">{record.secondaryUomUqc ? record.secondaryUomUqc.toProperCase() : ''}</div>
                </div>
              )}
            </Fragment>
          );
        },
        visible: visibleColumns?.TRANSFER_QTY?.visible,
      },
      {
        title: 'NEW ON HAND QTY',
        width: '150px',
        render: (item) => (
          <Fragment>
            {item?.quantity && item?.uom_info?.uqc ? (
              <H3Text text={`${QUANTITY(Number(item?.availableQuantity) - Number(item?.quantity))} ${item?.uom_info?.uqc?.toProperCase()}`} />
            ) : 0}
          </Fragment>
        ),
        visible: visibleColumns?.ON_HAND_QTY?.visible,
      },
      {
        title: 'LOCATION',
        width: '210px',
        render: (item) => {
          const {
            getInventoryLocations, inventoryLocations, getInventoryLocationsLoading, user,
          } = this.props;
          const {
            destinationDepartmentId, data, formSubmitted, transferType,
          } = this.state;
          const isWarehouseLocationRequired = user?.tenant_info?.inventory_config?.settings?.is_inventory_location_mandatory;
          return (
            <Fragment>
              {
                transferType === 'SAME BUSINESS' ? (
                  <Fragment>
                    <PRZSelect
                      value={item.inventory_location_id}
                      loading={getInventoryLocationsLoading}
                      filterOption={false}
                      showSearch
                      disabled={!destinationDepartmentId}
                      onChange={(value) => {
                        const copyData = JSON.parse(JSON.stringify(data));
                        const updatedData = copyData.map((record) => {
                          if (record.key === item.key) {
                            return {
                              ...record,
                              inventory_location_id: value,
                            };
                          }
                          return {
                            ...record,
                          };
                        });
                        this.setState({ data: updatedData });
                      }}
                      onSearch={(keyword) => {
                        getInventoryLocations(destinationDepartmentId, keyword, null, true);
                      }}
                    >
                      {
                        inventoryLocations?.inventory_location?.filter((location) => location?.is_active)?.map((location) => (
                          <Option
                            key={location?.inventory_location_id}
                            value={location?.inventory_location_id}
                          >
                            <H3Text text={location?.inventory_location_path} className="create-new-batch__location-item" />
                          </Option>
                        ))
                      }
                    </PRZSelect>
                    {
                      formSubmitted && isWarehouseLocationRequired && !item?.inventory_location_id
                      && (
                        <div className="input-error">
                          *Please enter location
                        </div>
                      )
                    }
                  </Fragment>
                ) : '-'
              }
            </Fragment>
          );
        },
        visible: visibleColumns?.LOCATION?.visible && transferTypeValue === 1 && enabledWarehouseLocations,
      },
      {
        title: '',
        render: (text, record) => {
          const { data } = this.state;
          return (
            data.length !== record.key && (
              <div className="create-st__delete-line-button" onClick={() => { this.handleDelete(record.key); }}>
                <CloseOutlined />
              </div>
            )
          );
        },
        visible: true,
        fixed: 'right',
        width: '40px',
      },
    ];

    if (cfStockTransferLine?.length) {
      productListColumns?.splice(4, 0, ...CustomFieldHelpers.renderCustomLineColumns(false, cfStockTransferLine, visibleColumns, this.customLineInputChange, formSubmitted));
    }

    return productListColumns.filter((item) => item.visible);
  }

  updateBatchValue(record, key, value, label, batchId) {
    const { data, allStock } = this.state;
    const copyData = JSON.parse(JSON.stringify(data));
    const updatedData = copyData.map((obj) => {
      if (obj.key === key) {
        let productBatches = obj?.product_batches;
        productBatches = productBatches?.map((productBatch) => {
          if (productBatch.batch_id === batchId) {
            // Check if label is 'consumed_qty' and apply logic based on available_qty
            if (label === 'consumed_qty') {
              return {
                ...productBatch,
                [label]: value > productBatch?.available_qty ? productBatch?.available_qty : value,
              };
            }
            // For other labels, simply update with the provided value
            return {
              ...productBatch,
              [label]: value,
            };
          }
          return productBatch;
        });
        const tempQty = productBatches.reduce(
          (sum, batch) => sum.plus(new Decimal(batch.consumed_qty || 0)),
          new Decimal(0)
        );

        // Update productBatches using the decimal-based quantity
        productBatches = getBatchesWithReservation(
          productBatches,
          tempQty.toNumber(),
          obj,
          allStock,
          key,
          false,
          batchId,
          value
        );
        // Calculate quantity after reservation
        const qty = productBatches.reduce(
          (sum, batch) => sum.plus(new Decimal(batch.consumed_qty || 0)),
          new Decimal(0)
        );

        return {
          ...obj,
          product_batches: productBatches,
          quantity: qty,
        };
      }
      return obj;
    });

    this.setState({ data: updatedData });
  }

  render() {
    const {
      createStLoading, updateStLoading, user, getTenantsLoading, tenants, match, getStByIdLoading, users, getUsers,
      getInventoryLocations, getProductsSuccess, getDocConfigStockTransferLoading, getDocConfig,
    } = this.props;
    const {
      productListColumns, data, remark, selectedDate, sourceTenant, destinationTenant, currentAction,
      transferTypes, transferTypeValue, destinationDepartment, formSubmitted, sourceDepartment, fileList, cfStockTransferDoc,
      checkedRecipients, toRecipients, sourceDepartmentId, allStock, destinationDepartmentId, cfStockTransferLine, visibleColumns,
      terms, stNumber, docSeqId, initialStNumber,
    } = this.state;

    const inventoryLocationEnabled = user?.tenant_info?.inventory_config?.settings?.enable_location_based_stock;

    const barCoding = user?.tenant_info?.global_config?.sub_modules?.barcoding?.is_active;
    const userMenuConfig = JSON.parse(localStorage.getItem('user_menu_config'));

    return (
      <div className={match.url.includes('/purchase-request') ? 'create-st__wrapper create-invoice-component' : 'create-st__wrapper create-invoice-page'}>
        <Fragment>
          {getStByIdLoading ? (
            <div className="update-st__loading__wrapper">
              {[1, 2, 3, 4].map((item) => (
                <div key={item} className="update-st__loading__wrapper-row">
                  <div className="update-st__loading__wrapper-row__label loadingBlock" />
                  <div className="update-st__loading__wrapper-row__value loadingBlock" />
                </div>
              ))}
              <div className="update-st__loading__wrapper-table loadingBlock" />
              <div className="update-st__loading__wrapper-details loadingBlock" />
            </div>
          ) : (
            <Fragment>
              <div className="form__wrapper" style={{ padding: '0px 0px 80px 0px' }}>
                <div className="ant-row">
                  <div className="ant-col-md-24">
                    <div className="form__section">
                      <div className="flex-display flex-align-c mg-bottom-5 pd-right-15">
                        <H3Text text="PART A" className="form__section-title" />
                        <div className="form__section-line" />
                      </div>
                      <div className="form__section-input mg-bottom-20">
                        <div className="ant-row">
                          <div className="ant-col-md-24">
                            <div className="form__input-row">
                              <div className="create-st__input-row__label">Transfer Type</div>
                              <Radio.Group
                                disabled={createStLoading || updateStLoading}
                                onChange={(event) => {
                                  this.setState({
                                    transferType: transferTypes[event.target.value],
                                    transferTypeValue: event.target.value,
                                    destinationDepartment: null,
                                    destinationDepartmentId: null,
                                    checkedRecipients: event.target.value === 2,
                                  });
                                }}
                                value={transferTypeValue}
                                style={{
                                  width: 'calc(100% - 180px)',
                                }}
                              >
                                <Radio value={1}>Same Business Unit</Radio>
                                <Radio value={2}>Other Business Unit</Radio>
                              </Radio.Group>
                            </div>
                          </div>
                          <DocumentNumberSeqInput
                            valueFromProps={stNumber}
                            updateCase={match?.params?.stockTransferId}
                            setInitialDocSeqNumber={(value) => this.setState({ initialStNumber: value })}
                            entityName="STOCK_TRANSFER"
                            docSeqId={docSeqId}
                            tenantId={sourceTenant}
                            onChangeFromProps={(event, newValue, seqId) => {
                              this.setState({
                                stNumber: newValue ? (newValue || '') : (event?.target?.value || ''),
                                docSeqId: seqId,
                              })
                            }}
                            docTitle="Stock Transfer#"
                            formSubmitted={formSubmitted}
                          />
                          <div className="ant-col-md-6">
                            <div className="form__input-row">
                              <H3Text text="Source Department" className="form__input-row__label" />
                              <div className="form__input-row__input">
                                <SelectDepartment
                                  hideTitle
                                  selectedDepartment={sourceDepartment}
                                  onChange={(value) => {
                                    getProductsSuccess(null);
                                    this.setState({
                                      sourceDepartment: value?.department_id,
                                      sourceDepartmentId: value?.tenant_department_id,
                                      destinationDepartment: null,
                                      destinationDepartmentId: null,
                                      data: [
                                        {
                                          key: uuidv4(), asset: '', product_sku_name: '', availableQuantity: '', thresholdQuantity: '', quantity: null,
                                        },
                                      ],
                                    });
                                  }}
                                  tenantId={sourceTenant}
                                  loading={getTenantsLoading}
                                  disabled={createStLoading || updateStLoading || user?.tenant_info?.default_store_id !== Helpers.getTenantDepartmentId(user)}
                                  labelClassName="mo-form__input-row__label"
                                  inputClassName="orgFormInput input"
                                  emptyNotAllowed
                                  showError={formSubmitted && !sourceDepartment}
                                />
                              </div>
                            </div>
                          </div>
                          {transferTypeValue !== 1 && (
                            <div className="ant-col-md-6">
                              <div className="form__input-row">
                                <div className="form__input-row__label">
                                  <H3Text text="Destination Business Unit" className="form__input-row__label" required />
                                </div>
                                <div
                                  className={`form__input-row__input ${formSubmitted && !destinationTenant ? 'form__input-row__input-error' : ''}`}
                                >
                                  <PRZSelect
                                    value={destinationTenant}
                                    loading={getTenantsLoading || getDocConfigStockTransferLoading}
                                    showSearch
                                    optionFilterProp="children"
                                    onChange={(value) => {
                                      this.setState({ destinationTenant: value, destinationDepartmentId: tenants?.data?.filter((item) => item?.tenant_id === value)?.[0]?.default_store_id });
                                      getInventoryLocations(tenants?.data?.filter((item) => item?.tenant_id === value)?.[0]?.default_store_id, '', null, true);
                                      getUsers(value, null, `${Helpers.permissionEntities.STOCK_TRANSFER}.${Helpers.permissionTypes.READ},${Helpers.permissionEntities.GOOD_RECEIVING}.${Helpers.permissionTypes.CREATE}`, (usersCallbackData) => {
                                        this.setState({
                                          toRecipients: [...new Map(usersCallbackData?.data?.[0]?.tenant_user?.map((item) => [item.email, item])).values()]?.map((item) => item?.email),
                                        });
                                      });
                                    }}
                                    disabled={createStLoading || updateStLoading}
                                    dropdownAlign={{ offset: [-4, 4] }}
                                    style={{
                                      border: 'none',
                                      borderRadius: '3px',
                                      height: '28px',
                                      backgroundColor: '#efefef',
                                      marginBottom: formSubmitted && !destinationTenant ? '5px' : '10px',
                                    }}
                                    placeholder="Select business unit.."
                                    status={(formSubmitted && !destinationTenant) ? 'error' : ''}
                                  >
                                    {tenants?.data?.filter((item) => item?.tenant_id !== user?.tenant_info?.tenant_id)?.map((tenant) => (
                                      <Option key={tenant?.tenant_id} value={tenant?.tenant_id}>
                                        {tenant?.tenant_name}
                                      </Option>
                                    ))}
                                  </PRZSelect>
                                </div>
                              </div>
                              {formSubmitted && !destinationTenant && <div style={{ marginBottom: '10px', marginTop: '0px' }} className="input-error">*Please select a destination business unit</div>}
                            </div>
                          )}
                          {transferTypeValue !== 2 && (
                            <div className="ant-col-md-6">
                              <div className="form__input-row">
                                <H3Text text="Destination Department" className="form__input-row__label" required />
                                <div className={`form__input-row__input ${formSubmitted && !destinationDepartment ? 'form__input-row__input-error' : ''}`}>
                                  <SelectDepartment
                                    hideTitle
                                    selectedDepartment={destinationDepartment}
                                    onChange={(value) => {
                                      getInventoryLocations(value?.tenant_department_id, '', null, true);
                                      const copyData = JSON.parse(JSON.stringify(data));
                                      for (let i = 0; i < copyData?.length; i++) {
                                        copyData[i].inventory_location_id = null;
                                      }
                                      this.setState({
                                        destinationDepartment: value?.department_id,
                                        destinationDepartmentId: value?.tenant_department_id,
                                        data: copyData,
                                      });
                                    }}
                                    loading={getTenantsLoading}
                                    disabled={createStLoading || updateStLoading}
                                    customStyle={{
                                      border: 'none',
                                      borderRadius: '4px',
                                      height: '28px',
                                      padding: '0px',
                                      background: '#efefef',
                                      marginBottom: (formSubmitted && !destinationDepartment) ? '5px' : '10px',
                                    }}
                                    labelClassName="form__input-row__label"
                                    inputClassName="orgFormInput input"
                                    emptyNotAllowed
                                    excludedDepartments={!inventoryLocationEnabled ? (sourceDepartment ? [sourceDepartment] : []) : []}
                                  />
                                </div>
                              </div>
                              {/* {formSubmitted && !destinationDepartment && <div className="input-error" style={{ marginBottom: '10px', marginTop: '0px' }}>*Please select a destination department</div>} */}
                            </div>
                          )}
                          <div className="ant-col-md-6">
                            <div className="form__input-row">
                              <H3Text text="Stock Transfer Date" className="form__input-row__label" required />
                              <div className={`form__input-row__input ${formSubmitted && !selectedDate ? 'form__input-row__input-error' : ''}`}>
                                <DatePicker
                                  value={selectedDate}
                                  onChange={(value) => {
                                    this.setState({ selectedDate: value });
                                  }}
                                  loading={getTenantsLoading}
                                  disabled={createStLoading || updateStLoading}
                                  format="DD-MMM-YYYY"
                                  style={{
                                    border: '1px solid rgba(68, 130, 218, 0.2)',
                                    borderRadius: '2px',
                                    height: '28px',
                                    padding: '1px 3px',
                                    width: '100%',
                                    background: 'white',
                                    marginBottom:
                                      formSubmitted && !selectedDate ? '0px' : '10px',
                                  }}
                                />
                              </div>
                            </div>
                            {/* {formSubmitted && !selectedDate && <div className="input-error" style={{ marginBottom: formSubmitted && !selectedDate ? '10px' : '0px', marginTop: '0px' }}>*Please select valid stock tranfer date</div>} */}
                          </div>
                          <CustomFieldV3
                            customFields={cfStockTransferDoc || []}
                            formSubmitted={formSubmitted}
                            customInputChange={(value, cfId) => this.customInputChange(value, cfId)}
                            disableCase={createStLoading || updateStLoading}
                            // isHorizontalUi
                            wrapperClassName="ant-col-md-6"
                            containerClassName="form__input-row"
                            labelClassName="form__input-row__label"
                            inputClassName="form__input-row__input"
                            errorClassName="form__input-row__input-error"
                            hideTitle
                          />
                        </div>
                      </div>
                    </div>
                    <div className="form__section">
                      <div className="flex-display flex-align-c mg-bottom-5 pd-right-15">
                        <H3Text text="PART B" className="form__section-title" />
                        <div className="form__section-line" />
                      </div>
                      <div className="form__section-inputs">
                        <div className="ant-row">
                          <div className="form__input-row">
                            <H3Text
                              text="Internal Remarks"
                              className="form__input-row__label"
                            />
                            <div className="form__input-row__input">
                              <textarea
                                rows="3"
                                cols="50"
                                value={remark}
                                onChange={(e) => {
                                  this.setState({ remark: e.target.value });
                                }}
                                disabled={createStLoading || updateStLoading}
                                style={{
                                  resize: 'none',
                                  backgroundColor: 'rgb(239, 239, 239)',
                                  border: 'none',
                                  borderRadius: '4px',
                                  marginRight: '15px',
                                  height: '103px',
                                  padding: '4px 8px',
                                }}
                              />
                            </div>
                          </div>
                          <div className="ant-col-md-12">
                            <div
                              className="form__data-attachment"
                            >
                              <label className="form__input-row__label">Attachment(s)</label>
                              <Upload
                                action={Constants.UPLOAD_FILE}
                                listType="picture-card"
                                fileList={fileList}
                                // disabled={getSOByIdLoading}
                                multiple
                                onChange={(fileListData) => {
                                  this.setState({
                                    fileList: fileListData?.fileList?.map((item) => ({
                                      ...item,
                                      url: item?.response?.response?.location,
                                    })),
                                  });
                                }}
                              >
                                {fileList?.length >= 20 ? null : uploadButton}
                              </Upload>
                            </div>
                          </div>
                          {transferTypeValue !== 1 && (
                            <div className="ant-col-md-24">
                              <div className="form__input-row" style={{ marginTop: '10px' }}>
                                <Checkbox
                                  disabled={createStLoading || updateStLoading}
                                  checked={checkedRecipients}
                                  onChange={(event) => {
                                    this.setState(
                                      {
                                        checkedRecipients: event.target.checked,
                                      },
                                    );
                                  }}
                                />
                                <span style={{ fontWeight: '500', fontSize: '12px', marginLeft: '5px' }}>Send Email Notification to Recipients</span>
                              </div>
                            </div>
                          )}
                          {checkedRecipients && (
                            <div className="ant-col-md-6">
                              <div className="form__input-row__input">
                                <PRZSelect
                                  mode="tags"
                                  value={toRecipients}
                                  filterOption={false}
                                  onChange={(value) => {
                                    const recipients = [];
                                    for (let i = 0; i < value?.length; i++) {
                                      if (Helpers.validateEmail(value[i])) {
                                        recipients.push(value[i]);
                                      }
                                    }
                                    this.setState({ toRecipients: recipients });
                                  }}
                                  disabled={createStLoading || updateStLoading || !destinationTenant}
                                  maxTagCount="responsive"
                                >
                                  {users && users?.data?.[0] && users?.data?.[0]?.tenant_user?.filter((item) => item.is_active)
                                    .map((item) => (
                                      <Option key={item.user_id} value={item.email}>
                                        {item?.email}
                                      </Option>
                                    ))}
                                </PRZSelect>
                              </div>
                              {formSubmitted && checkedRecipients && !toRecipients?.length && <div className="input-error">*Please enter at least one recipient</div>}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* <div>
                      <CustomDocumentInputs
                        customFields={cfStockTransferDoc}
                        updateCustomFields={(cf) => this.setState({ cfStockTransferDoc: cf })}
                      />
                    </div> */}

                  <div className="form__lines-wrapper">
                    <Table
                      title={() => (
                        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <H3Text text={`Stock Transfer Products (${data.filter((item) => item.product_sku_name?.length > 0)?.length})`} className="create-indent__table-label" />
                          <div style={{ display: 'flex' }}>
                            {!match?.params?.stockTransferId && (
                              <BulkUpload
                                disabled={!sourceDepartmentId || createStLoading || updateStLoading}
                                disableBatchCreate
                                userDepartmentId={sourceDepartmentId}
                                onBulkUpload={(updatedData, importedData) => this.onBulkUpload(updatedData, importedData)}
                                adjustmentType="OUTWARD"
                                customClass="custom-doc-columns__wrapper"
                              />
                            )}
                            <div style={{ marginLeft: '5px' }}>
                              <CustomDocumentColumns
                                visibleColumns={visibleColumns}
                                setVisibleColumns={(dt) => this.setState({ visibleColumns: dt })}
                                customColumns={cfStockTransferLine}
                                data={data}
                                updateData={(updatedData) => this.setState({ data: updatedData })}
                              />
                            </div>
                          </div>
                        </div>
                      )}
                      bordered
                      showHeader
                      size="small"
                      loading={createStLoading || updateStLoading}
                      columns={this.getColumns()}
                      dataSource={this.getDataSource(data)}
                      currentPage
                      pagination={false}
                      expandable={{
                        defaultExpandAllRows: true,
                        rowExpandable: (record) => record.product_sku_name && record?.product_sku_info && record?.product_batches?.length,
                        expandedRowRender: (record) => (
                          <BatchSelector
                            allStock={allStock}
                            upDateAllStock={() => {
                              const copyData = JSON.parse(JSON.stringify(data));
                              const filteredData = copyData?.map((item) => {
                                if (record.key === item.key) {
                                  clearReservationsByLine(item.key);
                                  for (let i = 0; i < item.product_batches?.length; i++) {
                                    item.product_batches[i].consumed_qty = 0;
                                  }
                                  item.quantity = 0;
                                }
                                return {
                                  ...item,
                                };
                              });
                              this.setState({ allStock: !allStock, data: filteredData });
                            }}
                            batchMethod={record?.batchConsumptionMethod}
                            updateBatchConsumptionMethod={(value) => {
                              const copyData = JSON.parse(JSON.stringify(data));
                              const sortedData = copyData?.map((item) => {
                                if (record.key === item.key) {
                                  item.batchConsumptionMethod = value;
                                  item.product_batches = Helpers.batchSelectionMethod(value, item.product_batches, item.quantity, allStock);
                                }
                                return {
                                  ...item,
                                };
                              });
                              this.setState({ data: sortedData });
                            }}
                            batches={record?.product_batches}
                            uomInfo={record?.uom_info}
                            productUomInfo={record?.uom_info}
                            productInfo={record}
                            expiryDays={record?.expiry_days}
                            itemsRow={record}
                            onToggleBatch={(batch, adjustmentRow) => this.toggleBatch(batch, adjustmentRow)}
                            updateBatchValue={(value, label, batchId) => this.updateBatchValue(record, record?.key, value, label, batchId)}
                          />
                        ),
                      }}
                      scroll={{ x: 'max-content' }}
                    />
                  </div>
                  <div className="create-st__summary-wrapper">
                    <div>
                      <div
                        className={`new-row-button ${data?.find((item) => !item?.product_sku_id) ? 'new-row-button__disabled' : ''}`}
                        onClick={() => {
                          if (!data?.find((item) => !item?.product_sku_id)) this.addNewRow();
                        }}
                      >
                        <span className="new-row-button__icon"><PlusCircleFilled /></span>
                        <div>New Item</div>
                      </div>
                      <div className="ant-row" style={{ marginTop: '15px' }}>
                        <div className="ant-col-md-24">
                          <div className="ant-col-md-12">
                            <div className="form__data-tc">
                              <label className="orgFormLabel">
                                Terms and Conditions
                              </label>

                              <RichTextEditor
                                onChange={(value) => this.handleChangeTextArea(value)}
                                disabled={createStLoading || updateStLoading || getDocConfigStockTransferLoading}
                                value={terms}
                              />

                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="create-st__summary-wrapper" />
                  </div>

                </div>
                <div className={`create-st__footer form__footer ${userMenuConfig === 'FIXED' ? 'form__footer-fixed' : ''}`}>
                  {Helpers.getPermission(Helpers.permissionEntities.STOCK_TRANSFER,
                    Helpers.permissionTypes.CREATE, user) && (
                      <H3Button
                        id="save-as-draft"
                        text="Save as Draft"
                        buttonType={defaultButtonTypes.BLUE_ROUNDED}
                        customClass="create-st__footer-draft"
                        onClick={() => {
                          this.setState({ currentAction: 'SAVE_AS_DRAFT' });
                          if (!createStLoading || updateStLoading) {
                            this.handleCreateSt(false);
                          }
                        }}
                        isLoading={currentAction === 'SAVE_AS_DRAFT' && (createStLoading || updateStLoading)}
                        disabled={createStLoading || updateStLoading}
                      />
                    )}
                  {Helpers.getPermission(Helpers.permissionEntities.STOCK_TRANSFER,
                    Helpers.permissionTypes.CREATE, user) && (
                      <H3Button
                        id="save-and-issue"
                        text="Save And Issue"
                        buttonType={defaultButtonTypes.BLUE_ROUNDED}
                        customClass="create-st__footer-submit"
                        onClick={() => {
                          this.setState({ currentAction: 'SAVE_AND_ISSUED' });
                          if (!createStLoading || !updateStLoading) {
                            this.handleCreateSt(true);
                          }
                        }}
                        isLoading={currentAction === 'SAVE_AND_ISSUED' && (createStLoading || updateStLoading)}
                        disabled={createStLoading || updateStLoading}
                      />
                    )}
                  <div className="form-barcode__wrapper" style={{ display: 'flex', justifyContent: 'end' }}>
                    {!barCoding && (
                      <Popconfirm
                        placement="topRight"
                        title="This feature is not accessible within your current plan to use this feature contact us."
                        onConfirm={() => window.Intercom('showNewMessage')}
                        okText="Contact Us"
                        cancelText="Cancel"
                      >
                        <img className="barcode-restrict" src={Crown} alt="premium" />
                      </Popconfirm>
                    )}
                    <BarcodeReader
                      productTypes={['STORABLE']}
                      excludedProducts={data.map((item) => JSON.stringify(item?.product_sku_id)) || []}
                      tenantDepartmentId={sourceDepartmentId}
                      excludeOutOfStock
                      filterReservedQuantity
                      onSearch={(tenantSku) => {
                        if (data[data?.length - 1]?.product_sku_id) {
                          this.addNewRow((key) => this.handleProductChange(tenantSku, key));
                        } else {
                          this.handleProductChange(tenantSku, data[data?.length - 1]?.key);
                        }
                      }}
                      disabled={!barCoding}
                    />
                  </div>

                </div>
              </div>

            </Fragment>
          )}
        </Fragment>
      </div>
    );
  }
}

const mapStateToProps = ({
  UserReducers, STReducers, TenantReducers, CFV2Reducers, InventoryLocationReducers, ProductReducers, DocConfigReducers,
}) => ({
  user: UserReducers.user,
  users: TenantReducers.users,
  createStLoading: STReducers.createStLoading,
  updateStLoading: STReducers.updateStLoading,
  tenants: TenantReducers.tenants,
  getTenantsLoading: TenantReducers.getTenantsLoading,
  selectedSt: STReducers.selectedSt,
  getStByIdLoading: STReducers.getStByIdLoading,
  getDocCFV2Loading: CFV2Reducers.getDocCFV2Loading,
  cfV2DocStockTransfer: CFV2Reducers.cfV2DocStockTransfer,
  getInventoryLocationsLoading: InventoryLocationReducers.getInventoryLocationsLoading,
  inventoryLocations: InventoryLocationReducers.inventoryLocations,
  selectedOrgProduct: ProductReducers.selectedOrgProduct,
  docConfigStockTransfer: DocConfigReducers.docConfigStockTransfer,
  getDocConfigStockTransferLoading: DocConfigReducers.getDocConfigStockTransferLoading,
});

const mapDispatchToProps = (dispatch) => ({
  createSt: (payload, callback, withApproval) => dispatch(STActions.createSt(payload, callback, withApproval)),
  updateSt: (payload, callback, withApproval) => dispatch(STActions.updateSt(payload, callback, withApproval)),
  getTenants: (page, keyword, isVerified, orgId, limit, callback) => dispatch(TenantActions.getTenants(page, keyword, isVerified, orgId, limit, callback)),
  getStByIdSuccess: (selectedSt) => dispatch(STActions.getStByIdSuccess(selectedSt)),
  getDepartments: (keyword, tenantId, page, limit, departmentId, callback) => dispatch(DepartmentActions.getDepartments(keyword, tenantId, page, limit, departmentId, callback)),
  getStById: (tenantId, stId) => dispatch(STActions.getStById(tenantId, stId)),
  getDocCFV2: (payload, callback) => dispatch(CFV2Actions.getDocCFV2(payload, callback)),
  getDocCFV2Success: (customFields) => dispatch(CFV2Actions.getDocCFV2Success(customFields)),
  getUsers: (tenantId, roleId, permissionType, callback) => dispatch(TenantActions.getUsers(tenantId, roleId, permissionType, callback)),
  getInventoryLocations: (tenantDepartmentId, inventoryLocationPath, parentLocationId, isStorable) => dispatch(InventoryLocationActions.getInventoryLocations(tenantDepartmentId, inventoryLocationPath, parentLocationId, isStorable)),
  getProductsSuccess: (products, forOffers) => dispatch(ProductActions.getProductsSuccess(products, forOffers)),
  getDocConfig: (tenantId, entityName, callback) => dispatch(DocConfigActions.getDocConfig(tenantId, entityName, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(StockTransferForm));
