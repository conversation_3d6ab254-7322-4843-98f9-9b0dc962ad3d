import React, { Component, Fragment } from 'react';
import { <PERSON>, withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { Select, Table, Popover } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faInfoCircle, faTag, faClose } from '@fortawesome/free-solid-svg-icons';
import { v4 as uuidv4 } from 'uuid';

import H3Text from '@Uilib/h3Text';
import H3FormInput from '@Uilib/h3FormInput';
import Helpers from '@Apis/helpers';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import { QUANTITY, DEFAULT_CUR_ROUND_OFF, } from '@Apis/constants';

// components
import BatchSelector from '@Components/Inventory/Indent/CreateIndent/BatchSelector';
import { handleQuantityChange } from '@Components/Sales/Invoice/InvoiceForm/helpers';
import QuickAddButton from '@Components/Common/UILib/QuickAddButton';
import ProductDescriptions from '@Components/Common/ProductDescriptions';
import ProductCategoryLabel from '@Components/Common/ProductCategoryLabel';
import ProductFilterV2 from '@Components/Common/ProductFilterV2';
import SelectTax from '../../../../Admin/Common/SelectTax';
import RecentTransactions from '../../../../Inventory/ManageProducts/ViewProduct/RecentTransactions';
import { handleQuantityForFgRm, handleFgQty, handleQuantityForJw, handleJwQty } from '../helpers';
import PRZSelect from '../../../../Common/UI/PRZSelect';
import ProductCodesAndName from '@Components/Common/ProductCodesAndName';
import './style.scss';


const { Option } = Select;

class InvoiceLines extends Component {

  constructor(props) {
    super(props);
    this.state = {
      bundleProductListColumns: [
        {
          title: 'Product',
          render: (text, record) => (
            <div className="invoice-table__product-name">
              <Link to={`/inventory/product/view/${record?.product_sku_info?.product_sku_id}`} target="_blank">
                {record?.product_sku_name}
              </Link>
            </div>
          ),
        },
        {
          title: 'Availability',
          width: '80px',
          render: (text, record) => {
            const availableQty = Helpers.getValueTotalInObject(record?.product_batches?.filter((item) => item?.batch_in_use), 'available_qty');
            return (
              <Fragment>
                {['SERVICE', 'NON_STORABLE'].includes(record?.product_sku_info?.product_type) && '-'}
                {!['SERVICE', 'NON_STORABLE'].includes(record?.product_sku_info?.product_type) && record?.product_sku_name
                  && (
                    <Fragment>
                      {availableQty ? `${QUANTITY((availableQty / record?.uom_info?.ratio) * record?.product_sku_info?.uom_info?.ratio, record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase()}` : <span style={{ color: 'red', fontSize: '11px', whiteSpace: 'nowrap' }}>OUT OF STOCK</span>}
                    </Fragment>
                  )}
                {!['SERVICE', 'NON_STORABLE'].includes(record?.product_sku_info?.product_type) && Helpers.getValueTotalInObject(record?.product_batches?.filter((item) => item?.batch_in_use), 'available_qty') ? (
                  <H3Text
                    text={`In ${record?.product_batches?.filter((item) => item?.batch_in_use)?.length || '0'} Batches`}
                    className="table-subscript"
                  />
                ) : ''}
              </Fragment>
            );
          },
        },
        {
          title: 'Quantity in Bundle',
          hidden: !this.props?.selectedOrderForInvoice && !this.props.moRmLines,
          render: (text, record) => `${QUANTITY(record?.so_quantity || '0', record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase() || ''}`,
        },
        {
          title: 'Invoice Qty',
          dataIndex: 'quantity',
          render: (text, record) => (
            <div>{`${Helpers.getValueTotalInObject(record?.product_batches, 'consumed_qty')} ${record?.uom_info?.uqc?.toProperCase()}`}</div>
          ),
        },
      ],
    };
  }

  fgQuantitySplit = (record, fgLines) => {
    const { user } = this.props;
    const enableInternalSKUCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_sku_code;
    const enableInternalRefCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_ref_code;
    return (
      <Fragment>
        {fgLines?.map((fgLine) => (
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '7px' }} key={fgLine?.fg_info?.product_sku_id}>
            <div style={{ fontSize: '12px', width: '60%' }}>
              <div onClick={() => window.open(`/inventory/product/view/${fgLine?.fg_info?.product_sku_id}`, '_blank')}>
                <ProductCodesAndName
                  skuCode={fgLine?.fg_info?.internal_sku_code}
                  refCode={fgLine?.fg_info?.ref_product_code}
                  name={fgLine?.fg_info?.product_sku_name}
                  showSku={enableInternalSKUCode}
                  showRefCode={enableInternalRefCode}
                />
              </div>
              <div className='truncate' style={{ width: '80px' }}>
                {fgLine?.fg_info?.product_sku_name}
              </div>
            </div>
            <div className="inv-qty-input">
              <H3FormInput
                value={fgLine.remaining_quantity || ''}
                type="text"
                containerClassName={`${(Number(fgLine.remaining_quantity) <= 0) ? 'form-error__input' : ''}`}
                labelClassName="orgFormLabel"
                inputClassName="orgFormInput"
                onChange={(event) => {
                  const { data, customLineInputChange } = this.props;
                  let qtyData = handleFgQty(event.target.value, data, record, fgLine);
                  let qty = qtyData?.totalRmQty;
                  let fgRmQtyMap = qtyData?.fgRmQtyMap;
                  CustomFieldHelpers.updateCustomColumnValue(Number(qty || 0), record.lineCustomFields?.find((i) => i?.fieldName === 'Quantity'), record.lineCustomFields, (value) => customLineInputChange(value, record?.key, null, { ...this.props, fgRmQtyMap }));
                }}
              />
              <div className="inv-qty-input-uom">{fgLine?.uom_info?.uqc?.toProperCase()}</div>
            </div>
          </div>))}
      </Fragment>
    );
  };

  jwQuantitySplit(record, jwLines) {
    return (
      <Fragment>
        {jwLines?.map((jwLine) => (
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '7px' }}>
            <div style={{ fontSize: '12px', width: '60%' }}>
              <div className="flex-display flex-align-c">
                <Link to={`/inventory/product/view/${jwLine?.fg_info?.product_sku_id}`} target="_blank">
                  {`#${jwLine?.fg_info?.internal_sku_code}`}
                </Link>
              </div>
              <div className='truncate' style={{ width: '80px' }}>
                {jwLine?.fg_info?.product_sku_name}
              </div>
            </div>
            <div className="inv-qty-input">
              <H3FormInput
                value={jwLine.remaining_quantity || ''}
                type="text"
                containerClassName={`${(Number(jwLine.remaining_quantity) <= 0) ? 'form-error__input' : ''}`}
                labelClassName="orgFormLabel"
                inputClassName="orgFormInput"
                onChange={(event) => {
                  const { data, customLineInputChange } = this.props;
                  let qtyData = handleJwQty(event.target.value, data, record, jwLine);
                  let qty = qtyData?.totalJwQty;
                  let jwQtyMap = qtyData?.jwQtyMap;
                  CustomFieldHelpers.updateCustomColumnValue(Number(qty || 0), record.lineCustomFields?.find((i) => i?.fieldName === 'Quantity'), record.lineCustomFields, (value) => customLineInputChange(value, record?.key, null, { ...this.props, jwQtyMap }));
                }}
              />
              <div className="inv-qty-input-uom">{jwLine?.uom_info?.uqc?.toProperCase()}</div>
            </div>
          </div>))}
      </Fragment>
    )
  }

  getColumns = () => {
    const {
      cfInvoiceLine, customLineInputChange, visibleColumns, user, formSubmitted, moRmLines, jwRmLines, priceMasking, isCarryForward,
    } = this.props;

    const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;

    const enableInternalRefCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_ref_code;
    const enableInternalSKUCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_sku_code;

    const productListColumns = [
      {
        title: 'Product',
        render: (text, record) => {
          const {
            handleProductChangeValue, handleProductChange, formSubmitted, addNewRow, data, tenantDepartmentId, selectedOrderForInvoice, user, tenantId, selectedCustomer, dontDeductStock, updateData, handleMultiProductChange, loading,
          } = this.props;
          return (
            <div className="invoice-table__product-name">
              {record?.product_sku_info ? (
                <React.Fragment>
                  <div className="flex-display flex-align-c" style={{ gap: '10px', justifyContent: 'space-between' }}>
                    <div onClick={() => window.open(`/inventory/product/view/${record?.product_sku_info?.product_sku_id}`, '_blank')}>
                      <ProductCodesAndName
                        skuCode={record?.product_sku_info?.internal_sku_code}
                        refCode={record?.product_sku_info?.ref_product_code}
                        name={record?.product_sku_name}
                        showSku={enableInternalSKUCode}
                        showRefCode={enableInternalRefCode}
                      />
                    </div>
                    <div>
                      {Helpers.getPermission(Helpers.permissionEntities.INVOICE, Helpers.permissionTypes.CREATE, user) && (
                        <RecentTransactions
                          tenantId={tenantId}
                          productSkuId={record?.product_sku_info?.product_sku_id}
                          internalSkuCode={record?.product_sku_info?.internal_sku_code}
                          refProductCode={record?.product_sku_info?.ref_product_code}
                          productSkuName={record?.product_sku_name}
                          costumerID={selectedCustomer}
                          TransactionType="SALES_ORDER"
                        />
                      )}
                    </div>
                  </div>
                  {record?.product_category_info?.category_path?.length > 0 && (
                    <ProductCategoryLabel
                      categoryPath={record?.product_category_info?.category_path}
                      categoryName={record?.product_category_info?.category_path?.at(-1)}
                      containerStyle={{
                        width: 'fit-content',
                      }}
                    />
                  )}
                  {
                    !record?.remarkRequired
                    && (
                      <QuickAddButton
                        label="Add Description"
                        onClick={() => {
                          const copyData = JSON.parse(JSON.stringify(data));
                          const newData = copyData.map((item) => {
                            if (item.key === record.key) {
                              return { ...item, remarkRequired: !record?.remarkRequired };
                            }
                            return item;
                          });
                          if (JSON.stringify(data) !== JSON.stringify(newData)) {
                            updateData(newData);
                          }
                        }}
                      />
                    )
                  }
                  {record?.remarkRequired ? (
                    <ProductDescriptions
                      descriptions={record?.remarks}
                      modules={{
                        toolbar: false,
                      }}
                      onChange={(e) => {
                        const copyData = JSON.parse(JSON.stringify(data));
                        copyData.map((item) => {
                          if (item.key === record.key) {
                            item.remarks = e;
                            item.remarkRequired = !!e?.replace(/<[^>]+>/g, '');
                          }
                          return data;
                        });
                        updateData(copyData);
                      }}
                      className="invoice-table__remarks"
                      placeholder="Add description of this item.."
                      key={record.key}
                    />
                  ) : ''}

                  <div style={{ display: 'flex' }}>
                    <div style={{ marginTop: '2px' }}>
                      {(record?.product_sku_info?.product_type === 'BUNDLE') && <H3Text text="Bundle" className="table-subscript table-subscript-tag" />}
                      {(record?.product_sku_info?.product_type === 'SERVICE') && <H3Text text="Service" className="table-subscript table-subscript-tag" />}
                      {(!['SERVICE', 'BUNDLE'].includes(record?.product_sku_info?.product_type)) && <H3Text text="Goods" className="table-subscript table-subscript-tag" />}
                    </div>
                    <div className="invoice-table__hsn">
                      <H3Text text={record?.product_sku_info?.product_type === 'SERVICE' ? 'SAC Code' : 'HSN Code'} className="invoice-table__hsn-label" />
                      <div className="invoice-table__hsn-value">
                        <input
                          value={record.hsn_code}
                          onChange={(e) => {
                            const copyData = JSON.parse(JSON.stringify(data));
                            copyData.map((item) => {
                              if (item.key === record.key) {
                                item.hsn_code = e.target.value;
                              }
                              return data;
                            });
                            updateData(copyData);
                          }}
                          className="invoice-table__remarks"
                          maxLength="10"
                        />
                      </div>
                    </div>
                  </div>
                </React.Fragment>
              ) : (
                <React.Fragment>
                  <ProductFilterV2
                    record={record}
                    productTypes={['STORABLE', 'SERVICE', 'BUNDLE', 'NON_STORABLE']}
                    handleProductChangeValue={handleProductChangeValue}
                    handleProductChange={handleProductChange}
                    required
                    showError={formSubmitted && !record.product_sku_name}
                    isSalesProduct
                    onClear={(key) => {
                      const copyData = JSON.parse(JSON.stringify(data));
                      const newData = copyData.map((item) => {
                        if (item.key === key) {
                          return {
                            key: uuidv4(),
                            asset1: '',
                            product_sku_name: '',
                            product_sku_id: null,
                            product_sku_info: null,
                            quantity: '',
                            unitPrice: '',
                            taxId: '',
                            lot: '',
                          };
                        }
                        return {
                          ...item,
                        };
                      });
                      addNewRow();
                      updateData(newData);
                    }}
                    disabled={!tenantDepartmentId}
                    tenantDepartmentId={tenantDepartmentId}
                    excludeOutOfStock={!dontDeductStock}
                    showClear
                    filterReservedQuantity
                    selectedTenant={tenantId}
                    enableAdvanceSearch
                    handleMultiProductChange={handleMultiProductChange}
                    loading={loading}
                  />
                </React.Fragment>
              )}
            </div>
          );
        },
        fixed: 'left',
        visible: visibleColumns?.PRODUCT?.visible,
      },
      {
        title: 'Availability',
        width: '96px',
        render: (text, record) => {
          let availableQty = 0;
          const { allStock } = this.props;
          if (record?.bundle_products?.length) {
            const arr = [];
            for (let i = 0; i < record?.bundle_products?.length; i++) {
              const qty = Helpers.getValueTotalInObject(record?.bundle_products?.[i]?.product_batches?.filter((item) => (item?.batch_in_use && (allStock ? !item?.is_rejected_batch : true))), 'available_qty');
              arr.push(qty / (record?.bundle_products[i]?.so_quantity / Number(record?.so_quantity || 1)));
            }
            availableQty = Math.min(...arr);
          } else {
            availableQty = Helpers.getValueTotalInObject(record?.product_batches?.filter((item) => (item?.batch_in_use && (allStock ? !item?.is_rejected_batch : true))), 'available_qty');
          }
          return (
            <div style={{ width: '80px' }}>
              {['SERVICE', 'NON_STORABLE'].includes(record?.product_sku_info?.product_type) && '-'}
              {!['SERVICE', 'NON_STORABLE'].includes(record?.product_sku_info?.product_type) && record?.product_sku_name
                && (
                  <Fragment>
                    {availableQty ? `${QUANTITY((availableQty / record?.uom_info?.ratio) * record?.product_sku_info?.uom_info?.ratio, (record?.uom_info?.precision || 0))} ${record?.uom_info?.uqc?.toProperCase() || ''}` : <span style={{ color: 'red', fontSize: '11px', whiteSpace: 'nowrap' }}>OUT OF STOCK</span>}
                  </Fragment>
                )}
              {!['SERVICE', 'BUNDLE'].includes(record?.product_sku_info?.product_type?.toUpperCase()) && availableQty ? (
                <H3Text
                  text={`In ${record?.product_batches?.filter((item) => item?.batch_in_use)?.length || '0'} Batches`}
                  className="table-subscript"
                />
              ) : ''}
            </div>
          );
        },
        visible: visibleColumns?.AVAILABLE_STOCK?.visible,
      },
      {
        title: (this.props.moRmLines) ? 'Pending Qty' : 'Ordered Qty',
        render: (text, record) => (
          <div style={{ width: '100px' }}>
            {`${QUANTITY(record?.so_quantity || '0', record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase() || ''}`}
            {Number(record?.so_free_quantity) > 0 ? (
              <H3Text
                text={`+ ${QUANTITY(record?.so_free_quantity || '0', record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase() || ''} Free`}
                className="table-subscript"
              />
            ) : ''}
          </div>
        ),
        hidden: (!this.props?.selectedOrderForInvoice && !this.props.isInvoiceFromSo) && !this.props.moRmLines,
        visible: true,
      },
      {
        title: 'Pending Qty',
        render: (text, record) => {
          return (
            <div style={{ width: '100px' }}>
              {`${QUANTITY(record?.pending_quantity_so || '0', record?.uom_info?.precision)} ${record?.uom_info?.uqc?.toProperCase() || ''}`}
            </div>
          );
        },
        hidden: !this.props?.selectedOrderForInvoice && !this.props.isInvoiceFromSo,
        visible: true,
      },
      {
        title: 'Quantity',
        render: (text, record) => {
          const {
            formSubmitted, updateData, data, allStock, pricesToApply, dontDeductStock, jwRmLines, user, selectedOrderForInvoice, isInvoiceFromSo
          } = this.props;
          let availableQty = 0;
          let availableBatchQty = {};
          const allowExcessQty = user?.tenant_info?.sales_config?.sub_modules?.invoice?.settings?.flexible_qty_from_so;
          if (record?.bundle_products?.length) {
            const arr = [];
            for (let i = 0; i < record?.bundle_products?.length; i++) {
              const qty = Helpers.getValueTotalInObject(record?.bundle_products?.[i]?.product_batches?.filter((item) => item?.batch_in_use), 'available_qty');
              arr.push(qty / (record?.bundle_products[i]?.so_quantity / record?.so_quantity));
            }
            availableQty = Math.min(...arr);
          } else {
            availableQty = Helpers.getValueTotalInObject(record?.product_batches?.filter((item) => (item?.batch_in_use && (allStock ? !item?.is_rejected_batch : true))), 'available_qty');
            availableQty = QUANTITY((availableQty / record?.uom_info?.ratio) * record?.product_sku_info?.uom_info?.ratio, (record?.uom_info?.precision || 0));
            const getEntries = (obj) => (obj ? Object.entries(obj) : []);
            const staticReservations = JSON.parse(sessionStorage.getItem('staticReservations'));
            const batchIdsToSum = record?.product_batches?.filter((item) => (item?.batch_in_use && (allStock ? !item?.is_rejected_batch : true)))?.map((item) => item?.batch_id) || [];
            const valuesToSum = batchIdsToSum?.flatMap((batchId) => getEntries(staticReservations[batchId])
              .filter(([lineId, value]) => lineId !== record?.key)
              .map(([lineId, value]) => value));
            const sum = valuesToSum.reduce((acc, value) => acc + value, 0);
            availableBatchQty = availableQty - sum;
          }
          const conditionForErrorSoToInv = (selectedOrderForInvoice || isInvoiceFromSo) && record?.quantity > record?.pending_quantity_so && !allowExcessQty;
          return (
            <div style={{ width: '120px' }}>
              <div className="inv-qty-input">
                {!record?.parent_line_id && (
                  <H3FormInput
                    value={record.quantity || ''}
                    type="number"
                    containerClassName={`orgInputContainer ${(formSubmitted && (Number(record.quantity) <= 0 || conditionForErrorSoToInv)) ? 'form-error__input' : ''}`}
                    labelClassName="orgFormLabel"
                    inputClassName="orgFormInput"
                    onWheel={(event) => event.target.blur()}
                    onChange={(event) => {
                      if (moRmLines) {
                        let qtyData = handleQuantityForFgRm(event.target.value, data, record);
                        let qty = qtyData?.newRmQty;
                        let fgRmQtyMap = qtyData?.fgRmQtyMap;

                        CustomFieldHelpers.updateCustomColumnValue(
                          qty,
                          record.lineCustomFields?.find((i) => i?.fieldName === 'Quantity'),
                          record.lineCustomFields,
                          (value) => customLineInputChange(value, record?.key, null, { ...this.props, fgRmQtyMap })
                        );
                      } else if (jwRmLines) {
                        let qtyData = handleQuantityForJw(event.target.value, data, record);
                        let qty = qtyData?.newJwQty;
                        let jwQtyMap = qtyData?.jwQtyMap;

                        CustomFieldHelpers.updateCustomColumnValue(
                          qty,
                          record.lineCustomFields?.find((i) => i?.fieldName === 'Quantity'),
                          record.lineCustomFields,
                          (value) => customLineInputChange(value, record?.key, null, { ...this.props, jwQtyMap })
                        );
                      } else {
                        let qty = event.target.value;

                        CustomFieldHelpers.updateCustomColumnValue(
                          qty,
                          record.lineCustomFields?.find((i) => i?.fieldName === 'Quantity'),
                          record.lineCustomFields,
                          (value) => customLineInputChange(value, record?.key, null, this.props),
                        );
                      }
                    }}
                    disabled={(['SERVICE', 'NON_STORABLE'].includes(record?.product_sku_info?.product_type?.toUpperCase()) || dontDeductStock) ? false : !availableQty}
                  />
                )}
                <div className="inv-qty-input-uom">{record?.uom_info?.uqc?.toProperCase()}</div>
                {(moRmLines || jwRmLines) && (
                  <div style={{ marginLeft: '5px', marginTop: '10px', cursor: 'pointer' }}>
                    <Popover
                      content={jwRmLines ? this.fgQuantitySplit(record, record?.jw_wise_breakup || []) : this.fgQuantitySplit(record, record?.fg_wise_breakup || [])}
                      placement="top"
                      getPopupContainer={(triggerNode) => triggerNode.parentNode}
                    >
                      <FontAwesomeIcon icon={faInfoCircle} className="info-icon" color="#008FFB" />
                    </Popover>
                  </div>)}
              </div>
              {record?.product_sku_info && !user?.tenant_info?.inventory_config?.settings?.enable_batch_consumed_qty_editable && (
                <div className="freebie-container">
                  <div
                    className="freebie-cta"
                    onClick={(e) => {
                      const copyData = JSON.parse(JSON.stringify(data));
                      copyData.map((item) => {
                        if (item.key === record.key) {
                          item.showFreebie = true;
                        }
                        return data;
                      });
                      updateData(copyData);
                    }}
                  >
                    + Add Free Qty
                  </div>
                  {record?.showFreebie && (
                    <div className="freebie-input">
                      <H3FormInput
                        value={record.free_quantity}
                        type="number"
                        containerClassName={`orgInputContainer ${(formSubmitted && Number(record.free_quantity) < 0) ? 'form-error__input' : ''}`}
                        labelClassName="orgFormLabel"
                        inputClassName="orgFormInput"
                        onChange={(e) => {
                          handleQuantityChange(record, record.quantity, e.target.value, record?.showFreebie, this.props, this.state);
                        }}
                      />
                      <CloseOutlined onClick={() => {
                        handleQuantityChange(record, record.quantity || 0, 0, false, this.props, this.state);
                      }}
                      />
                    </div>
                  )}
                </div>
              )}
              {record?.product_sku_info && !dontDeductStock && (
                <Fragment>
                  {!['SERVICE', 'NON_STORABLE'].includes(record?.product_sku_info?.product_type)
                    && (Number(availableBatchQty) - (Number(record?.quantity) + Number(record?.free_quantity || 0)) < 0 || availableQty === 0)
                    ? (
                      <div className="input-error">
                        *Entered quantity is more than available quantity
                      </div>
                    ) : (
                      <Fragment>
                        {['SERVICE', 'NON_STORABLE'].includes(record?.product_sku_info?.product_type) && formSubmitted
                          && Number(availableQty + record?.quantity < 0)
                          ? (
                            <div className="input-error">
                              Sufficient inventory is not available.
                            </div>
                          ) : ''}
                      </Fragment>
                    )}
                </Fragment>
              )}
            </div>
          );
        },
        visible: visibleColumns?.QUANTITY?.visible,
      },
      {
        title: 'UOM',
        render: (text, record) => {
          const {
            formSubmitted, updateData, data,
          } = this.props;
          return (
            <div style={{ width: '120px' }}>
              <div className="orgInputContainer">
                <PRZSelect
                  filterOption={false}
                  className=""
                  value={record?.uomId}
                  onChange={(value) => {
                    const copyData = JSON.parse(JSON.stringify(data));
                    copyData.map((item) => {
                      if (item.product_sku_id === record.product_sku_id) {
                        item.uomId = value;
                        item.uomInfo = item?.uom_list?.find(
                          (rec) => rec?.uom_id === value,
                        );
                        item.uom_info = item?.uom_list?.find(
                          (rec) => rec?.uom_id === value,
                        );
                      }
                      return item;
                    });
                    updateData(copyData);
                  }}
                  disabled={!record?.uom_list || record?.product_type === 'BUNDLE'}
                >
                  {record?.uom_list?.map((uom) => (
                    <Option
                      key={uom?.uom_id}
                      value={uom?.uom_id}
                    >
                      {`${uom?.uqc?.toProperCase()} (${uom?.uom_name?.toProperCase()})`}

                    </Option>
                  ))}
                </PRZSelect>
              </div>
            </div>
          );
        },
        visible: !this.props?.selectedOrderForInvoice && !this.props?.selectedWorkOrder && !this.props?.jwRmLines && !this.props?.moRmLines && !this.props?.selectedInvoice?.mo_id && !this.props?.selectedInvoice?.order_id,
      },
      {
        title: 'Rate',
        render: (text, record) => {
          const {
            formSubmitted, billFromState, billToState, dontDeductStock, disablePriceChange,
          } = this.props;
          let availableQty = 0;
          if (record?.bundle_products?.length) {
            const arr = [];
            for (let i = 0; i < record?.bundle_products?.length; i++) {
              const qty = Helpers.getValueTotalInObject(record?.bundle_products?.[i]?.product_batches?.filter((item) => item?.batch_in_use), 'available_qty');
              arr.push(qty / (record?.bundle_products[i]?.so_quantity / record?.so_quantity));
            }
            availableQty = Math.min(...arr);
          } else {
            availableQty = Helpers.getValueTotalInObject(record?.product_batches?.filter((item) => item?.batch_in_use), 'available_qty');
          }
          const disableUnitPrice = disablePriceChange || (dontDeductStock ? false : !availableQty);
          const val = Helpers.trimDecimalsAsNumber(record?.unitPrice, DEFAULT_CUR_ROUND_OFF)
          return (
            <div style={{ width: '120px' }}>
              <H3FormInput
                value={val}
                type="number"
                containerClassName={(formSubmitted && Number.parseFloat(record?.unitPrice) < 0) ? 'form-error__input' : ''}
                labelClassName="orgFormLabel"
                inputClassName="orgFormInput"
                onWheel={(event) => event.target.blur()}
                onChange={(e) => {
                  CustomFieldHelpers.updateCustomColumnValue(Number.parseFloat(e.target.value || 0), record.lineCustomFields?.find((i) => i?.fieldName === 'Rate'), record.lineCustomFields, (value) => {
                    customLineInputChange(value, record?.key, null, this.props, null, null, Number.parseFloat(e.target.value || 0))
                  });
                }}
                disabled={['SERVICE', 'NON_STORABLE'].includes(record?.product_sku_info?.product_type) ? false : disableUnitPrice}
                hideInput={isDataMaskingPolicyEnable && isHideSellingPrice}
                popOverMessage={"You don't have access to view rate"}
              />
            </div>
          );
        },
        visible: visibleColumns?.UNIT_PRICE?.visible,
      },
      {
        title: 'Tax',
        render: (text, record) => {
          const { formSubmitted, billFromState, billToState } = this.props;
          return (
            <div style={{ width: '120px' }}>
              <div>
                <SelectTax
                  onChange={(value) => {
                    const { data, updateData } = this.props;
                    const copyData = JSON.parse(JSON.stringify(data));
                    const discountValue = Number(record.discount) || 0;
                    const taxableValue = record?.lineDiscountType === 'Percent' ? (record.quantity * record.unitPrice) * (discountValue ? Number(100 - discountValue) / 100 : 1) : Math.max(record.quantity * record.unitPrice - discountValue, 0);
                    copyData.map((item) => {
                      if (item.key === record.key) {
                        item.taxId = value?.tax_id;
                        item.taxInfo = value;
                        item.child_taxes = Helpers.computeTaxation(taxableValue, value, billFromState, billToState)?.tax_info?.child_taxes;
                      }
                      return item;
                    });
                    updateData(copyData);
                  }}
                  errorOutline={formSubmitted && !record.taxId}
                  selectedGST={record.taxId}
                />
              </div>
            </div>
          );
        },
        visible: visibleColumns?.TAX?.visible,
      },
      {
        title: 'Discount',
        width: '150px',
        render: (text, record) => {
          const {
            data, updateData, isLineWiseDiscount, billFromState, billToState, selectedCurrencyName, MONEY,
          } = this.props;
          const lineDiscountAmount = record?.lineDiscountType === 'Percent' ? (((record.quantity * record.unitPrice) / 100) * record.discount) : record?.discount;
          const currencySymbol = selectedCurrencyName?.currency_symbol || '';

          const amountTypeOptions = [
            {
              label: `${selectedCurrencyName?.currency_symbol} `,
              value: 'Amount',
            },
            {
              label: '%',
              value: 'Percent',
            },
          ];
          return (

            <Fragment>
              <Fragment>
                {isLineWiseDiscount ? (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <H3FormInput
                      value={record.discount}
                      type="number"
                      labelClassName="orgFormLabel"
                      inputClassName="orgFormInput"
                      onChange={(e) => {
                        const copyData = JSON.parse(JSON.stringify(data));
                        const discountValue = Number(e.target.value) || 0;
                        const taxableValue = record?.lineDiscountType === 'Percent' ? (record.quantity * record.unitPrice) * (1 - discountValue / 100) : Math.max(record.quantity * record.unitPrice - discountValue, 0);
                        copyData.map((item) => {
                          if (item.key === record.key) {
                            item.discount = e.target.value || '';
                            item.unitDiscount = Number.parseFloat(Number(Number(e.target.value) / Number(record.quantity)).toFixed(DEFAULT_CUR_ROUND_OFF));
                            item.child_taxes = Helpers.computeTaxation(taxableValue, record.taxInfo, billFromState, billToState)?.tax_info?.child_taxes;
                          }
                          return data;
                        });
                        updateData(copyData);
                      }}
                      disabled={!isLineWiseDiscount}
                    />
                    <div className="orgInputContainer discount-type">
                      <PRZSelect
                        value={record?.lineDiscountType}
                        onChange={(value) => {
                          const copyData = JSON.parse(JSON.stringify(data));
                          const discountValue = Number(record.discount) || 0;
                          const taxableValue = value === 'Percent'
                            ? (record.quantity * record.unitPrice) * (1 - discountValue / 100)
                            : Math.max(record.quantity * record.unitPrice - discountValue, 0);

                          copyData.map((item) => {
                            if (item.key === record.key) {
                              item.lineDiscountType = value;
                              item.child_taxes = Helpers.computeTaxation(taxableValue, record.taxInfo, billFromState, billToState)?.tax_info?.child_taxes;
                            }
                            return item;
                          });
                          updateData(copyData);
                        }}
                        style={{
                          width: '50px',
                        }}
                        disabled={!isLineWiseDiscount}
                        options={amountTypeOptions}
                        placeholder=""
                      />
                    </div>
                  </div>
                ) : (
                  <div>
                    <H3Text
                      text={
                        record?.discount ? record?.lineDiscountType === 'Percent' ? `${(record?.discount)?.toFixed(DEFAULT_CUR_ROUND_OFF)}%` :
                          MONEY((record?.discount)?.toFixed(DEFAULT_CUR_ROUND_OFF), selectedCurrencyName?.currency_code)
                          : record?.lineDiscountType === 'Percent' ? `0%` : MONEY(0, selectedCurrencyName?.currency_code)
                      }
                    />
                  </div>
                )}
                {(record.quantity > 0 && record.unitPrice > 0 && record.discount > 0) && (
                  <H3Text
                    text={(
                      <div>
                        {record?.lineDiscountType === 'Percent' ? (
                          (isDataMaskingPolicyEnable && isHideCostPrice) ? (
                            <HideValue showPopOver popOverMessage="You don't have access to view discount/unit" />
                          ) : (
                            `${currencySymbol}${(lineDiscountAmount / record?.quantity).toFixed(2)}/unit`
                          )
                        ) : (
                          `${((record?.discount / (record.quantity * record.unitPrice)) * 100).toFixed(2)}% | ${currencySymbol}${(lineDiscountAmount / record?.quantity).toFixed(2)}/unit`
                        )}
                      </div>

                    )}
                    className="table-subscript table-subscript__discount"
                  />
                )}
              </Fragment>
              {/* {record?.lineDiscountType === 'Amount' && !record?.showUnitDiscount && (
                <QuickAddButton
                  label="Add Unit Discount"
                  onClick={() => {
                    const copyData = JSON.parse(JSON.stringify(data));
                    const newData = copyData.map((item) => {
                      if (item.key === record.key) {
                        return { ...item, showUnitDiscount: !record?.showUnitDiscount };
                      }
                      return item;
                    });
                    if (JSON.stringify(data) !== JSON.stringify(newData)) {
                      updateData(newData);
                    }
                  }}
                />
              )} */}
              {record?.lineDiscountType !== 'Percent' && record?.showUnitDiscount && (
                <div className="small-add-button__blue small-add-button__blue-wrapper">
                  <FontAwesomeIcon icon={faTag} />
                  &nbsp;
                  <div className="doc-line__unit-discount">
                    <H3FormInput
                      value={record.unitDiscount}
                      type="number"
                      containerClassName="orgInputContainer"
                      labelClassName="orgFormLabel"
                      inputClassName="orgFormInput"
                      onChange={(e) => {
                        const copyData = JSON.parse(JSON.stringify(data));
                        const unitDiscountValue = Number.parseFloat(e.target.value) || 0;
                        const discountValue = Number.parseFloat(Number(Number(unitDiscountValue) * Number(record?.quantity)).toFixed(DEFAULT_CUR_ROUND_OFF));
                        const taxableValue = record?.lineDiscountType === 'Percent'
                          ? (record.quantity * record.unitPrice) * (1 - discountValue / 100)
                          : Math.max(record.quantity * record.unitPrice - discountValue, 0);

                        copyData.map((item) => {
                          if (item.key === record.key) {
                            item.discount = discountValue;
                            item.unitDiscount = unitDiscountValue || '';
                            item.child_taxes = Helpers.computeTaxation(taxableValue, record.taxInfo, billFromState, billToState)?.tax_info?.child_taxes;
                          }
                          return data;
                        });
                        updateData(copyData);
                      }}
                      disabled={!isLineWiseDiscount}
                    />
                    <H3Text text="/unit" className="doc-line__unit-discount-label" />
                    <FontAwesomeIcon
                      icon={faClose}
                      className="doc-line__unit-discount-close"
                      onClick={() => {
                        const copyData = JSON.parse(JSON.stringify(data));
                        copyData.map((item) => {
                          if (item.key === record.key) {
                            item.showUnitDiscount = !record?.showUnitDiscount;
                          }
                          return data;
                        });
                        updateData(copyData);
                      }}
                    />
                  </div>
                </div>
              )}
            </Fragment>
          );
        },
        visible: visibleColumns?.DISCOUNT?.visible,
      },
      {
        title: 'Sales Account',
        hidden: !(user?.tenant_info?.tally_configuration?.sales_account_selection === 'LINE_LEVEL'),
        render: (item) => {
          const { tallyConnections, formSubmitted, user } = this.props;
          return (
            <Fragment>
              <PRZSelect
                value={item.sales_account_name}
                onChange={(value) => {
                  this.updateTableValue(item.key, value, 'sales_account_name');
                }}
                style={{
                  width: '150px',
                }}
                showError={formSubmitted && !item.sales_account_name}
                errorName="sales account"
                errorClassName="input-error"
                placeholder=""
              >
                {user?.tenant_info?.tally_configuration?.sales_account_list?.map((item) => <Option key={item.sales_account_name} value={item.sales_account_name}>{item.sales_account_name}</Option>)}
              </PRZSelect>
            </Fragment>
          );
        },
        visible: visibleColumns?.SALES_ACCOUNT?.visible,
      },
      {
        title: 'Total',
        width: '100px',
        fixed: 'right',
        render: (text, record) => {
          const {
            selectedCurrencyName, MONEY, billFromState, billToState,
          } = this.props;

          const discountValue = Number(record.discount) || 0;
          const taxableValue = record?.lineDiscountType === 'Percent' ? (record.quantity * record.unitPrice) * (1 - discountValue / 100) : Math.max(record.quantity * record.unitPrice - discountValue, 0);

          return (
            <div style={{ width: '100px' }}>
              <H3Text
                text={record.taxInfo
                  ? MONEY(Helpers.computeTaxation(taxableValue, record.taxInfo, billFromState, billToState)?.taxed_value, selectedCurrencyName?.currency_code) : 0}
                hideText={isDataMaskingPolicyEnable && isHideSellingPrice}
                popOverMessage={"You don't have access to view total amount"}
              />
            </div>
          );
        },
        visible: visibleColumns?.LINE_TOTAL?.visible,
      },
      {
        title: '',
        render: (text, record) => {
          const { handleDelete, data } = this.props;
          return (
            <Fragment>
              {data.length !== record.key && (
                <div className="form__delete-line-button" onClick={() => handleDelete(record.key, record)}>
                  <CloseOutlined />
                </div>
              )}
            </Fragment>
          );
        },
        width: '40px',
        fixed: 'right',
        visible: true,
      },
    ].filter((item) => !item.hidden);

    if (cfInvoiceLine?.length) {
      productListColumns.splice((!this.props?.selectedOrderForInvoice && !this.props.moRmLines && !this.props.isInvoiceFromSo) ? 4 : 5, 0, ...CustomFieldHelpers.renderCustomLineColumns(false, cfInvoiceLine, visibleColumns, customLineInputChange, formSubmitted, null, null, null, null, null, this.props, '', isCarryForward));
    }

    return productListColumns.filter((item) => item?.visible);
  }

  getBundleLineKeys(rows) {
    const keys = [];
    for (let i = 0; i < rows?.length; i++) {
      if (rows[i]?.bundle_products?.length) {
        keys.push(rows[i]?.key);
      }
    }
    return keys;
  }

  updateTableValue(key, value, label) {
    const { updateData, data } = this.props;
    const copyData = JSON.parse(JSON.stringify(data));
    const newData = copyData.map((obj) => {
      if (obj.key === key) {
        return {
          ...obj,
          [`${label}`]: value,
        };
      }
      return obj;
    });
    updateData(newData);
  }

  updateBatchValue(record, key, value, label, batchId) {
    const { data, customLineInputChange } = this.props;
    const copyData = JSON.parse(JSON.stringify(data));
    const newData = copyData.map((obj) => {
      if (obj.key === key) {
        let productBatches = obj?.product_batches;
        productBatches = productBatches?.map((productBatch) => {
          if (productBatch.batch_id === batchId) {
            // Check if label is 'consumed_qty' and apply logic based on available_qty
            if (label === 'consumed_qty') {
              const avlQty = QUANTITY((Number(productBatch?.available_qty) / obj?.uom_info?.ratio) * obj?.product_sku_info?.uom_info?.ratio, obj?.uom_info?.precision);
              return {
                ...productBatch,
                [label]: value > avlQty ? avlQty : value,
              };
            }
            // For other labels, simply update with the provided value
            return {
              ...productBatch,
              [label]: value,
            };
          }
          return productBatch;
        });

        return {
          ...obj,
          product_batches: productBatches,
        };
      }
      return obj;
    });

    const qty = newData?.find((item) => item?.key === key)?.product_batches.reduce((sum, batch) => sum + (Number(batch.consumed_qty) || 0), 0);
    CustomFieldHelpers.updateCustomColumnValue(
      qty,
      record.lineCustomFields?.find((i) => i?.fieldName === 'Quantity'),
      record.lineCustomFields,
      (_value) => customLineInputChange(_value, record?.key, null, this.props, batchId, value),
    );
  }

  render() {
    const {
      loading, data, toggleBatch, toggleBatchInner, allStock, toggleAllStock, updateData, title, moRmLines, selectedOrderForInvoice, disabled, dontDeductStock,
    } = this.props;
    const {
      productListColumns, bundleProductListColumns,
    } = this.state;

    return (
      <div className="invoice-lines__wrapper">
        <Table
          title={title}
          bordered
          loading={loading}
          showHeader
          size="small"
          scroll={{ x: 'max-content' }}
          columns={this.getColumns()}
          dataSource={data}
          pagination={false}
          expandable={{
            rowExpandable: (record) => !dontDeductStock && (record?.bundle_products || Helpers.getValueTotalInObject(record?.product_batches, 'available_qty')) && !['SERVICE', 'NON_STORABLE'].includes(record?.product_sku_info?.product_type),
            defaultExpandedRowKeys: this.getBundleLineKeys(data),
            expandedRowRender: (record) => (
              <Fragment>
                {record.bundle_products?.length ? (
                  <div className="inv__bundle-products__wrapper">
                    <Table
                      bordered
                      loading={loading}
                      showHeader
                      size="small"
                      columns={bundleProductListColumns}
                      dataSource={record?.bundle_products}
                      pagination={false}
                      expandable={{
                        defaultExpandAllRows: false,
                        rowExpandable: (innerRecord) => innerRecord?.product_sku_name && Helpers.getValueTotalInObject(innerRecord?.product_batches, 'available_qty'),
                        expandedRowRender: (innerRecord) => (
                          <BatchSelector
                            innerBatchSelector
                            batchMethod={innerRecord?.batchConsumptionMethod}
                            updateBatchConsumptionMethod={(value) => {
                              const copyData = JSON.parse(JSON.stringify(data));
                              const sortedData = copyData?.map((item) => {
                                if (innerRecord.key === item.key) {
                                  item.batchConsumptionMethod = value;
                                  item.product_batches = Helpers.batchSelectionMethod(value, item.product_batches, item.quantity, allStock);
                                }
                                return {
                                  ...item,
                                };
                              });
                              updateData(sortedData);
                            }}
                            batches={innerRecord?.product_batches}
                            uomInfo={innerRecord?.uom_info}
                            productUomInfo={innerRecord?.product_sku_info?.uom_info}
                            expiryDays={innerRecord?.product_sku_info?.expiry_days}
                            itemsRow={innerRecord}
                            onToggleBatch={(batch, adjustmentRow) => toggleBatchInner(batch, adjustmentRow)}
                            productInfo={record}
                          />
                        ),
                      }}
                      scroll={{ x: 'max-content' }}
                    />
                  </div>
                ) : (
                  <BatchSelector
                    allStock={allStock}
                    upDateAllStock={() => {
                      const copyData = JSON.parse(JSON.stringify(data));
                      const filteredData = copyData?.map((item) => {
                        if (record?.key === item?.key) {
                          for (let i = 0; i < item?.product_batches?.length; i++) {
                            item.product_batches[i].consumed_qty = 0;
                          }
                          item.quantity = 0;
                        }
                        return {
                          ...item,
                        };
                      });
                      updateData(filteredData);
                      toggleAllStock();
                    }}
                    batchMethod={record?.batchConsumptionMethod}
                    updateBatchConsumptionMethod={(value) => {
                      const copyData = JSON.parse(JSON.stringify(data));
                      const sortedData = copyData?.map((item) => {
                        if (record.key === item.key) {
                          item.batchConsumptionMethod = value;
                          item.product_batches = Helpers.batchSelectionMethod(value, item.product_batches, item.quantity, allStock, null, item?.uom_info);
                          item.free_quantity = 0;
                        }
                        return {
                          ...item,
                        };
                      });
                      updateData(sortedData);
                    }}
                    batches={record?.product_batches}
                    uomInfo={record?.uom_info}
                    productUomInfo={record?.product_sku_info?.uom_info}
                    expiryDays={record?.product_sku_info?.expiry_days}
                    itemsRow={record}
                    onToggleBatch={(batch, adjustmentRow) => toggleBatch(batch, adjustmentRow)}
                    productInfo={record}
                    updateBatchValue={(value, label, batchId) => this.updateBatchValue(record, record?.key, value, label, batchId)}
                    noBlockCalculation
                  />
                )}
              </Fragment>
            ),
          }}
        />
      </div>
    );
  }
}

const mapStateToProps = ({
  UserReducers, PurchaseOrderReducers, OfferReducers,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  updateCustomPurchaseOrderLoading: PurchaseOrderReducers.updateCustomPurchaseOrderLoading,
  selectedPurchaseOrder: PurchaseOrderReducers.selectedPurchaseOrder,
  getOfferByTenantSkuLoading: OfferReducers.getOfferByTenantSkuLoading,
  priceMasking: UserReducers.priceMasking,
});

const mapDispatchToProps = () => ({

});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(InvoiceLines));