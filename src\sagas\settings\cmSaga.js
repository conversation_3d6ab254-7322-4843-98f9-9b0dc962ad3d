import { put, takeLatest, all } from 'redux-saga/effects';
import { notification } from 'antd';
import TrackerActions from '@Actions/trackerActions';
import CMActions, { cmActionTypes } from '../../actions/settings/cmActions';
import CMServices from '../../services/settings/cmServices';

/**
 * @param action
 */
function* _createCM(action) {
  try {
    const { payload } = action;
    yield put(CMActions.createCMLoading(true));
    const cm = yield CMServices.createCM(payload);
    if (cm.success) {
      yield put(TrackerActions.trackAction(cmActionTypes.CREATE_CM.SUCCESS, cm));
      notification.open({
        type: 'success',
        message: 'successfully created',
        duration: 4,
        placement: 'top',
      });
      action.callback(cm);
    } else {
      notification.open({
        type: 'error',
        message: cm?.message,
        duration: 4,
        placement: 'top',
      });
    }
    yield put(CMActions.createCMLoading(false));
  } catch (error) {
    notification.open({
      type: 'error',
      message: error?.message,
      duration: 4,
      placement: 'top',
    });
    yield put(CMActions.createCMLoading(false));
    yield put(TrackerActions.trackAction(cmActionTypes.CREATE_CM.ERROR, error));
  }
}

/**
 *
 */
function* createCM() {
  yield takeLatest(cmActionTypes.CREATE_CM.REQUEST, _createCM);
}

/**
 * @param action
 */
function* _updateCM(action) {
  try {
    yield put(CMActions.updateCMLoading(true));
    const cm = yield CMServices.updateCM(action.payload);
    if (cm.success) {
      yield put(TrackerActions.trackAction(cmActionTypes.UPDATE_CM.SUCCESS, {
        request: action?.payload,
        response: cm,
      }));
      notification.open({
        type: 'success',
        message: cm?.message || 'Custom Message successfully updated',
        duration: 4,
        placement: 'top',
      });
      action.callback(cm);
      yield put(CMActions.updateCMLoading(false));
    } else {
      notification.open({
        type: 'error',
        message: cm?.message,
        duration: 4,
        placement: 'top',
      });
      yield put(CMActions.updateCMLoading(false));
    }
  } catch (error) {
    notification.open({
      type: 'error',
      message: error?.message,
      duration: 4,
      placement: 'top',
    });
    yield put(CMActions.updateCMLoading(false));
    yield put(TrackerActions.trackAction(cmActionTypes.UPDATE_CM.ERROR, {
      request: action,
      error,
    }));
  }
}

/**
 *
 */
function* updateCM() {
  yield takeLatest(cmActionTypes.UPDATE_CM.REQUEST, _updateCM);
}

/**
 * @param action
 */
function* _getCM(action) {
  const {
    orgId, entityId, entityType, page, limit, tenantId, actionType, isActive, isArchived, callback
  } = action;
  try {
    CMActions.getCMSuccess(null);
    yield put(CMActions.getCMLoading(true));
    const customMessages = yield CMServices.getCM(orgId, entityId, entityType, page, limit, tenantId, actionType, isActive, isArchived);
    yield put(CMActions.getCMSuccess(customMessages, entityType));
    if (callback) callback(customMessages?.data);
    yield put(CMActions.getCMLoading(false, entityType));
  } catch (error) {
    yield put(CMActions.getCMLoading(false, entityType));
    notification.open({
      type: 'error',
      message: error.message,
      duration: 4,
      placement: 'top',
    });
    yield put(TrackerActions.trackAction(cmActionTypes.GET_CM.ERROR, error));
  }
}

/**
 *
 */
function* getCM() {
  yield takeLatest(cmActionTypes.GET_CM.REQUEST, _getCM);
}

/**
 * @param action
 */
function* _deleteCM(action) {
  try {
    yield put(CMActions.deleteCMLoading(true));
    const {
      payload, callback,
    } = action;
    const deletedGrn = yield CMServices.deleteCM(payload);

    if (deletedGrn.success) {
      notification.open({
        type: 'success',
        message: 'Custom Message successfully deleted',
        duration: 4,
        placement: 'top',
      });
      callback();
    } else {
      notification.open({
        type: 'error',
        message: deletedGrn?.message,
        duration: 4,
        placement: 'top',
      });
    }
    yield put(CMActions.deleteCMLoading(false));
  } catch (error) {
    yield put(CMActions.deleteCMLoading(false));
    notification.open({
      type: 'error',
      message: error.message,
      duration: 4,
      placement: 'top',
    });
    yield put(TrackerActions.trackAction(cmActionTypes.DELETE_CM.ERROR, {
      request: action,
      error,
    }));
  }
}

/**
 *
 */
function* deleteCM() {
  yield takeLatest(cmActionTypes.DELETE_CM.REQUEST, _deleteCM);
}

/**
 *
 */
export default function* cmSaga() {
  yield all([
    createCM(), getCM(), updateCM(), deleteCM(),
  ]);
}
