import React, { Component, Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { Tabs } from 'antd';
import PropTypes from 'prop-types';
import Helpers from '@Apis/helpers';
import BusinessProfile from './BusinessProfile';
import UserProfile from './UserProfile';
import WhatsappConfiguration from './WhatsappConfiguration';
import NotificationCentre from './NotificationCentre';
import useCheckPortrait from '../../../hooks/useCheckPortrait';
import './style.scss';
import AllEntityListV2 from './AllEntityConfig/AllEntityListV2';

const { TabPane } = Tabs;

/**
 * @param status
 */

function withCustomHook(WrappedComponent) {
  return function Wrapper(props) {
    const useCheckPortraitCustomHook = useCheckPortrait();
    return <WrappedComponent {...props} customHooks={{ useCheckPortraitCustomHook }} />;
  };
}

class MyProfile extends Component {
  /**
   *
   * @param {*} props
   * @param {*} state
   * @return
   */
  static getDerivedStateFromProps(props, state) {
    const { location } = props;
    const key = new URLSearchParams(location.search).get('tab');
    return {
      ...state,
      currentTab: key || '/company-profile',
    };
  }

  /**
   *
   * @param props
   */
  constructor(props) {
    super(props);
    this.state = {
      currentTab: '/company-profile',
    };
  }

  /**
   *
   * @return {JSX.Element}
   */
  render() {
    const { currentTab } = this.state;
    const { history, user, customHooks } = this.props;

    const {
      isMobile, isPortrait, isMobileAndPortrait, isMobileOrPortrait,
    } = customHooks.useCheckPortraitCustomHook;

    const tabsUpdatePermission = Helpers.getPermission(Helpers.permissionEntities.ACCESS_CONTROL_SETUP, Helpers.permissionTypes.UPDATE, user);
    const tabsReadPermission = Helpers.getPermission(Helpers.permissionEntities.ACCESS_CONTROL_SETUP, Helpers.permissionTypes.READ, user);
    const hideBorderBottom = !tabsUpdatePermission ? 'hide-my-profile-border-bottom' : '';
    return (
      <Fragment>
        <div className={`my-profile__wrapper doc-list__tabs-wrapper ${hideBorderBottom}`}>
          <Tabs
            activeKey={currentTab}
            onChange={(key) => {
              this.setState({ currentTab: key });
              history.push(`?tab=${key}`);
            }}
            mode="horizontal"
          >
            {tabsUpdatePermission && <TabPane tab="Company Profile" key="/company-profile" />}
            {tabsUpdatePermission && <TabPane tab="Configuration" key="/configuration" />}
            {tabsReadPermission && <TabPane tab="My Profile" key="/my-profile" />}
            {user?.tenant_info?.integration_config?.sub_modules?.whatsapp?.is_active && user?.tenant_info?.integration_tenant_whatsapp_id && tabsUpdatePermission && <TabPane tab="Whatsapp Configuration" key="/whatsapp-configuration" />}
            {<TabPane tab="Notification Centre" key="/notification-centre" />}
          </Tabs>
          <div className="doc-list__wrapper doc-list__tab-content">
            {currentTab === '/company-profile' && <BusinessProfile />}
            {currentTab === '/configuration' && <AllEntityListV2 />}
            {currentTab === '/my-profile'
              && (
                <UserProfile
                  editPermission={tabsUpdatePermission}
                />
              )}
            {currentTab === '/whatsapp-configuration' && <WhatsappConfiguration />}
            {/* {currentTab === '/document-configuration' && <DocumentConfiguration tab={currentTab} />} */}
            {currentTab === '/notification-centre' && <NotificationCentre tab={currentTab} />}
          </div>
        </div>
      </Fragment>
    );
  }
}

const mapStateToProps = ({
  UserReducers,
}) => ({
  user: UserReducers.user,
});

const mapDispatchToProps = () => ({
});

MyProfile.propTypes = {
  location: PropTypes.any,
  user: PropTypes.any,
  history: PropTypes.func,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(withCustomHook(MyProfile)));
