import React, { Component, Fragment } from 'react';
import { <PERSON>, withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { Table, Popconfirm } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';
import H3FormInput from '@Uilib/h3FormInput';
import BomSelector from '@Components/Common/Selector/BomSelector';
import H3Text from '@Uilib/h3Text';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import './style.scss';
import ProductFilterV2 from '@Components/Common/ProductFilterV2';
import ProductCategoryLabel from '../../../../../Common/ProductCategoryLabel';
import HideValue from '../../../../../Common/RestrictedAccess/HideValue';
import PRZText from '@Components/Common/UI/PRZText';

class FinishedGood extends Component {
  constructor(props) {
    super(props);

    this.state = {
      includeInActiveBOM: false,
    };
  }

  getColumns = () => {
    const {
      visibleColumns, cfFGLine, customLineInputChange, formSubmitted, user, priceMasking,
    } = this.props;

    const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;

    const productListColumns = [
      {
        title: 'Product',
        width: '300px',
        fixed: 'left',
        render: (text, record) => {
          const {
            handleProductChange, tenantDepartmentId, excludedProducts, selectedTenant, handleChangeRemarks, handleAdhocProductChange, handleAdhocProductChangeValue, data, handleMultiProductChange, loading,
          } = this.props;
          const enableInternalRefCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_ref_code;
          const enableInternalSKUCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_sku_code;

          return (
            <div className="mo-fg-table__product-name">
              {
                (record?.bom_id) ? (
                  <Fragment>
                    {enableInternalSKUCode && (<Link to={`/inventory/product/view/${record?.tenant_product_info?.product_sku_id}`} target="_blank">
                      {`#${record?.tenant_product_info?.internal_sku_code}`}
                    </Link>)}
                    {record?.bom_info?.product_info?.product_category_info
                      && record?.bom_info?.product_info?.product_category_info.category_path?.length > 0 && (
                      <ProductCategoryLabel
                        categoryPath={record?.bom_info?.product_info?.product_category_info.category_path}
                        categoryName={record?.bom_info?.product_info?.product_category_info.category_path?.at(
                          -1,
                        )}
                        containerStyle={{
                          width: 'fit-content',
                        }}
                      />
                    )}
                    {enableInternalRefCode && record?.tenant_product_info?.ref_product_code && (
                      <PRZText text={record?.tenant_product_info?.ref_product_code} />
                    )}
                    <PRZText text={`${record?.tenant_product_info?.product_sku_name || record?.tenant_product_info?.alias_name}`?.trim()} />
                    {(record?.bom_info?.product_info?.description?.replace(/<[^>]+>/g, '') || record?.tenant_product_info?.description) && (
                      <textarea
                        style={{
                          width: '286px',
                        }}
                        value={record?.bom_info?.product_info?.description?.replace(/<[^>]+>/g, '') || record?.tenant_product_info?.description?.replace(/<[^>]+>/g, '')}
                        onChange={(e) => {
                          handleChangeRemarks(record?.tenant_product_info?.tenant_product_id, (e.target.value), record?.key);
                        }}
                        className="mo-table__remarks"
                        placeholder="add description here.."
                        disabled
                      />
                    )}
                  </Fragment>
                ) : (
                  <div>
                    {record?.isAdhocLine ? (
                      <ProductFilterV2
                        record={record}
                        productTypes={['STORABLE', 'NON_STORABLE']}
                        handleProductChangeValue={handleAdhocProductChangeValue}
                        handleProductChange={handleAdhocProductChange}
                        required
                        excludedProducts={excludedProducts || []}
                        disabled={!tenantDepartmentId}
                        tenantDepartmentId={tenantDepartmentId}
                        showClear
                        filterReservedQuantity
                        selectedTenant={selectedTenant}
                        enableAdvanceSearch
                        handleMultiProductChange={handleMultiProductChange}
                        loading={loading}
                      />
                    ) : (
                      <BomSelector
                        hideTitle
                        emptyNotAllowed
                        tenantDepartmentId={tenantDepartmentId}
                        selectedBom={record?.bom_id}
                        placeholder="Select Bill of Material.."
                        onChange={(value) => handleProductChange(record?.key, value)}
                        disabled={(!tenantDepartmentId) || record?.bom_id}
                        labelClassName="mo-form__input-row__label"
                        inputClassName="input"
                        excludedProducts={excludedProducts || []}
                        includeInActiveBOM
                        tenantId={selectedTenant}
                      />
                    )}
                  </div>
                )
              }
            </div>
          );
        },
        visible: visibleColumns?.PRODUCT?.visible,
      },
      {
        title: 'Ready To Produce',
        width: '150px',
        render: (text, record) => {
          const { getReadyToProduce } = this.props;
          return (record?.bom_id && Number(getReadyToProduce(record?.tenant_product_info?.tenant_product_id)) > 0) ? `${getReadyToProduce(record?.tenant_product_info?.tenant_product_id)} ${record?.tenant_product_info?.uom_info?.uqc?.toProperCase() || record?.bom_info?.product_info?.uom_info?.uqc?.toProperCase() || ''}` : `0 ${record?.tenant_product_info?.uom_info?.uqc?.toProperCase() || record?.bom_info?.product_info?.uom_info?.uqc?.toProperCase() || ''}`;
        },
        visible: visibleColumns?.READY_TO_PRODUCE?.visible,
      },
      {
        title: 'Required Quantity',
        width: '180px',
        render: (text, record) => {
          const {
            createMOLoading, updateMOLoading, formSubmitted, handleQuantityChange,
          } = this.props;
          return (
            <Fragment>
              {record?.bom_id
                ? (
                  <div className="mo-fg-table__quantity">
                    <H3FormInput
                      name="a valid quantity"
                      type="number"
                      containerClassName={`orgInputContainer mo-form__input-row__input ${formSubmitted && !Number(record?.quantity) ? 'form-error__input' : ''}`}
                      labelClassName="orgFormLabel"
                      inputClassName="orgFormInput input"
                      placeholder=""
                      onChange={(e) => {
                        CustomFieldHelpers.updateCustomColumnValue(Number(e.target.value || 0), record.lineCustomFields?.find((i) => i?.fieldName === 'Quantity'), record.lineCustomFields, (value) => customLineInputChange(value, record?.key, record?.fg_tenant_product_id));
                      }}
                      value={record?.quantity}
                      showError={formSubmitted && ((!Number(record?.quantity)))}
                      disabled={createMOLoading || updateMOLoading}
                      loading={createMOLoading || updateMOLoading}
                    />
                    <H3Text
                      text={record?.tenant_product_info?.uom_info?.uqc?.toProperCase()}
                      className="mo-fg-table__quantity-unit"
                    />
                  </div>
                ) : '-'}
            </Fragment>
          );
        },
        visible: visibleColumns?.REQUIRED_QUANTITY?.visible,
      },
      {
        title: 'RM Cost Per Unit',
        width: '150px',
        render: (text, record) => (record?.bom_id ? ((isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver popOverMessage="You don't have access to view rm cost per unit" /> : `${this.props.MONEY(this.props.getFgCosting(record?.tenant_product_info?.tenant_product_id, Number(record?.quantity))?.rmCostPerUnit)}`) : '-'),
        visible: visibleColumns?.RM_COST?.visible,
      },
      {
        title: 'Charges Per Unit',
        width: '150px',
        render: (text, record) => (record?.bom_id ? ((isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver popOverMessage="You don't have access to view total unit cost" /> : `${this.props.MONEY(this.props.getFgCosting(record?.tenant_product_info?.tenant_product_id, Number(record?.quantity))?.chargePerUnit)}`) : '-'),
        visible: visibleColumns?.CHARGES?.visible,
      },
      {
        title: 'Total Unit Cost',
        width: '150px',
        render: (text, record) => (record?.bom_id ? ((isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver popOverMessage="You don't have access to view total unit cost" /> : `${this.props.MONEY(this.props.getFgCosting(record?.tenant_product_info?.tenant_product_id, Number(record?.quantity))?.totalCostPerUnit)}`) : '-'),
        visible: visibleColumns?.TOTAL_UNIT_COST?.visible,
      },
      {
        title: 'Total Cost',
        width: '150px',
        fixed: 'right',
        render: (text, record) => (record?.bom_id ? (isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver popOverMessage="You don't have access to view total cost" /> : `${this.props.MONEY(this.props.getFgCosting(record?.tenant_product_info?.tenant_product_id, Number(record?.quantity))?.totalCostPerUnit * record?.quantity)}` : '-'),
        visible: visibleColumns?.TOTAL_COST?.visible,
      },
      {
        title: ' ',
        width: '40px',
        fixed: 'right',
        render: (text, record) => {
          const { handleDelete } = this.props;
          return (
            <Popconfirm
              title={<div style={{ width: '250px' }}>Raw materials, by products and charges associated with this FG will also be removed. Are you sure?</div>}
              onConfirm={() => handleDelete(record.key)}
              onCancel={() => { }}
              okText="Yes"
              cancelText="No"
              placement="topLeft"
              className="mp-fg__delete-line-button-confirm"
            >
              <div className="mp-fg__delete-line-button">
                <CloseOutlined />
              </div>
            </Popconfirm>
          );
        },
        visible: visibleColumns?.TOTAL_COST?.visible,
      },
    ];

    if (cfFGLine?.length) {
      productListColumns.splice(5, 0, ...CustomFieldHelpers.renderCustomLineColumns(false, cfFGLine, visibleColumns, customLineInputChange, formSubmitted));
    }

    return productListColumns?.filter((item) => item?.visible);
  }

  getDataSource(data) {
    const newData = JSON.parse(JSON.stringify(data));
    return newData;
  }

  render() {
    const {
      data, loading, isFlexible,
    } = this.props;
    return (
      <Fragment>
        <div className="mo-fg-table-wrapper hide__in-mobile">
          <Table
            bordered
            loading={loading}
            showHeader
            size="small"
            scroll={{ x: 'max-content' }}
            columns={isFlexible ? this.getColumns() : this.getColumns()?.filter((item) => item.title)}
            dataSource={this.getDataSource(data)}
            pagination={false}
          />
        </div>

      </Fragment>
    );
  }
}

/**
 *
 * @param UserReducers
 * @param PurchaseOrderReducers
 * @return {{updateCustomPurchaseOrderLoading: *, selectedPurchaseOrder, user}}
 */
const mapStateToProps = ({
  UserReducers, PurchaseOrderReducers, OfferReducers,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  updateCustomPurchaseOrderLoading: PurchaseOrderReducers.updateCustomPurchaseOrderLoading,
  selectedPurchaseOrder: PurchaseOrderReducers.selectedPurchaseOrder,
  getOfferByTenantSkuLoading: OfferReducers.getOfferByTenantSkuLoading,
  priceMasking: UserReducers.priceMasking,
});

/**
 *
 * @return {{}}
 */
const mapDispatchToProps = () => ({

});

FinishedGood.propTypes = {
  loading: PropTypes.bool,
  handleProductChange: PropTypes.func,
  handleDelete: PropTypes.func,
  updateData: PropTypes.func,
  data: PropTypes.any,
  formSubmitted: PropTypes.bool,
  addNewRow: PropTypes.func,
  moQuantity: PropTypes.any,
  isFlexible: PropTypes.bool,
  handleQuantityChange: PropTypes.func,
  createMOLoading: PropTypes.bool,
  updateMOLoading: PropTypes.bool,
  getFgCosting: PropTypes.func,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(FinishedGood));
