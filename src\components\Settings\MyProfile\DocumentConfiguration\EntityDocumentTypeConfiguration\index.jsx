import React, { Fragment, useState, useEffect } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import {
  Button, Col, Image, Modal, Row,
  Tag,
  notification,
} from 'antd';
import { EyeOutlined, SaveOutlined } from '@ant-design/icons';
import { faMaximize } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import DocConfigActions from '@Actions/docConfigActions';
import ProcDocConfigInput from '../../../../../uilib/procDocConfigInput';

import './style.scss';

function EntityDocumentTypeConfiguration({
  user, entityName, docType, allowedFields, updateDocConfig, callback, loading,
}) {
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [data, setData] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState('Template1');
  const [hoveredTemplate, setHoveredTemplate] = useState(null);
  const [signature, setSignature] = useState(null);
  const [signatureTakenBy, setSignatureTakenBy] = useState(null);
  const [letterHead, setLetterHead] = useState(null);
  const [signatoryName, setSignatoryName] = useState('Full Page');
  const [label, setLabel] = useState(null);

  const handlePreview = (src) => {
    window.open(src, '_blank');
  };

  function sortData(oldData) {
    // Sort the oldData array based on field_type
    oldData.sort((a, b) => {
      if (a.field_type === 'template') return -1;
      if (b.field_type === 'template') return 1;
      if (a.field_type === 'rich_text') return 1;
      if (b.field_type === 'rich_text') return -1;
      if (a.field_type === 'email') return -1;
      if (b.field_type === 'email') return 1;
      if (a.field_type === 'placement_of_t_and_c') return 1;
      if (b.field_type === 'placement_of_t_and_c') return -1;
      if (a.field_type === 'text_box') return 1;
      if (b.field_type === 'text_box') return -1;
      if (a.field_type === 'dropdown') return 1;
      if (b.field_type === 'dropdown') return -1;
      if (a.field_type === 'taken_by_name') return 1;
      if (b.field_type === 'taken_by_name') return -1;
      if (a.field_type === 'taken_by_sign_base_64') return 1;
      if (b.field_type === 'taken_by_sign_base_64') return -1;
      if (a.field_type === 'print_taken_by_signature_input') return 1;
      if (b.field_type === 'print_taken_by_signature_input') return -1;
      if (a.field_type === 'signature_name') return 1;
      if (b.field_type === 'signature_name') return -1;
      if (a.field_type === 'image') return 1;
      if (b.field_type === 'image') return -1;
      if (a.field_type === 'print_signature_input') return 1;
      if (b.field_type === 'print_signature_input') return -1;
      if (a.field_type === 'checkbox') return 1;
      if (b.field_type === 'checkbox') return -1;
      if (a.field_type === 'number') return 1;
      if (b.field_type === 'number') return -1;
      if (a.field_type === 'print_labels') return 1;
      if (b.field_type === 'print_labels') return -1;
      if (a.field_type === 'footer_padding') return 1;
      if (b.field_type === 'footer_padding') return -1;
      if (a.field_type === 'letter_head_padding') return 1;
      if (b.field_type === 'letter_head_padding') return -1;
      if (a.field_type === 'print_letter_head') return 1;
      if (b.field_type === 'print_letter_head') return -1;
      return 0;
    });

    return oldData;
  }

  useEffect(() => {
    setData(sortData(allowedFields) || []);
    const initialTemplate = allowedFields?.find((field) => field.field_name === 'document_template');
    setSelectedTemplate(initialTemplate.field_value || 'Template1');
  }, []);

  const handleOnChangeValue = (fieldName, value) => {
    const copyData = JSON.parse(JSON.stringify(data));

    for (let i = 0; i < copyData?.length; i++) {
      if (copyData?.[i]?.field_name === fieldName) {
        if (copyData?.[i]?.field_name === 'doc_sign_base_64' || copyData?.[i]?.field_name === 'taken_by_sign_base_64') {
          copyData[i].field_value = value?.length ? value[0].response?.response?.location : '';
        } else if (copyData?.[i]?.field_name === 'email_cc_address') {
          copyData[i].field_value = value.join(',');
        } else {
          copyData[i].field_value = value;
        }
        break;
      }
    }

    setData(copyData);
  };

  const isDataValid = () => {
    let isValid = true;
    for (let i = 0; i < data?.length; i++) {
      if (data[i]?.required && !data[i]?.field_value) {
        isValid = false;
      }
    }
    return isValid;
  };

  const updateDocumentConfig = () => {
    setFormSubmitted(true);

    if (isDataValid()) {
      const payloadKey = data?.map((item) => item?.field_name);

      const payload = {
        tenant_id: user?.tenant_info?.tenant_id,
        entity_name: entityName,
        document_type: docType,
      };

      payloadKey.forEach((key) => {
        const dataItem = data.find((item) => item.field_name === key);
        if (dataItem) {
          payload[key] = dataItem.field_value;
        }
      });
      if (payload?.footer_padding + payload?.letter_head_padding > 270) {
        notification.error({
          message: 'Footer Padding and Letter Head Padding should not exceed 270 (px)',
          placement: 'top',
          duration: 4,
        });
      } else {
        updateDocConfig(payload, () => {
          callback();
        });
      }
    }
  };

  useEffect(() => {
    if (data) {
      const signatureField = data?.find((item) => item?.field_name === 'print_signature_input');
      const signatureTakenByField = data?.find((item) => item?.field_name === 'print_taken_by_signature_input');
      const letterHeadField = data?.find((item) => item?.field_name === 'print_letter_head');
      const labelField = data?.find((item) => item?.field_name === 'print_labels');
      setLetterHead(letterHeadField?.field_value);
      setSignature(signatureField?.field_value);
      setLabel(labelField?.field_value);
      setSignatureTakenBy(signatureTakenByField?.field_value);
    }
  }, [data]);

  return (
    <div>
      {data?.map((allowedField) => (
        allowedField?.field_type === 'template' && (
          <div className="template-selection" key={allowedField.field_name}>
            <div className="orgFormLabel">
              {allowedField?.label}
            </div>
            {allowedField?.dropdown_options?.map((template) => (
              <div
                key={template.key}
                style={{
                  border: selectedTemplate === template.key ? '2px solid rgb(26, 192, 93)' : '2px solid rgb(230, 244, 255)',
                  borderRadius: '5px',
                  padding: '5px',
                  marginBottom: '10px',
                  cursor: 'pointer',
                  position: 'relative',
                  marginTop: '10px',
                  width: '25%',
                  display: 'inline-block',
                  marginRight: '10px',
                }}
                onMouseEnter={() => setHoveredTemplate(template.key)}
                onMouseLeave={() => setHoveredTemplate(null)}
                onClick={() => {
                  setSelectedTemplate(template.key);
                  handleOnChangeValue('document_template', template.key);
                }}
              >
                <div className="orgFormLabel">{template.label}</div>
                <div style={{ width: '100%', height: '100%', position: 'relative' }}>
                  <Image
                    src={template.value}
                    alt="Template Image"
                    preview={false}
                    className={selectedTemplate === template.key ? 'selected' : ''}
                    selected={selectedTemplate === template.key}
                  />
                  {hoveredTemplate === template.key && (
                    <div
                      style={{
                        position: 'absolute',
                        top: '50%',
                        left: '50%',
                        transform: 'translate(-50%, -50%)',
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handlePreview(template.url);
                      }}
                    >
                      <FontAwesomeIcon icon={faMaximize} color="#2D7DF7" />
                    </div>
                  )}
                  {selectedTemplate === template.key && (
                    <Tag className="tag" bordered={false}>Selected</Tag>
                  )}
                </div>
              </div>
            ))}
          </div>
        )
      ))}
      {data?.length ? (
        <div className="doc-type-config__wrapper">

          <Fragment>
            {selectedTemplate && (
              <div className="ant-row"
                style={{
                  marginBottom: '20px',
                }}
              >
                {data
                  ?.filter((allowedField) => allowedField.field_type !== 'template')
                  .map((allowedField) => (
                    <ProcDocConfigInput
                      key={allowedField?.label}
                      required={allowedField?.required}
                      label={allowedField?.label}
                      labelClassName="orgFormLabel"
                      containerClassName="orgInputContainer ant-col-md-24"
                      inputClassName="orgFormInput"
                      fieldType={allowedField?.field_type}
                      placeholder=""
                      value={allowedField?.field_value}
                      onChange={(value) => handleOnChangeValue(allowedField?.field_name, value)}
                      disabled={loading}
                      dropDownOptions={allowedField?.dropdown_options}
                      showOnly={selectedTemplate}
                      showSignatureUpload={signature}
                      showLetterHead={letterHead}
                      setSignatoryName={setSignatoryName}
                      signatoryName={signatoryName}
                      showLabelInput={label}
                      showSignatureTakenByUpload={signatureTakenBy}
                      disableTnc={entityName === 'PURCHASE_ORDER' || entityName === 'INVOICE' || entityName === 'SALES_ORDER'}
                    />
                  ))}
              </div>
            )}
            <div className="save_doc-config_changes">
              <Button
                loading={loading}
                style={{ marginTop: '10px' }}
                type="primary"
                onClick={() => updateDocumentConfig()}
                customClass="create-invoice__footer-submit"
              >
                <SaveOutlined />
                &nbsp;Save
              </Button>
            </div>
          </Fragment>
        </div>
      ) : (
        <div />
        // <div className="doc-type-no-config__wrapper">
        //   <div className="doc-type-no-config">
        //     <img
        //       alt="No Document Configuration icon"
        //       src={NoDoc}
        //       height={100}
        //       width={100}
        //     />
        //     <div className="doc-type-no-config__text">
        //       {`No Document Configuration Available for ${entityName?.split('_').join(' ').toProperCase()}
        //     `}
        //     </div>
        //   </div>
        // </div>
      )}
    </div>
  );
}

const mapStateToProps = ({ UserReducers }) => ({
  user: UserReducers.user,
});

const mapDispatchToProps = (dispatch) => ({
  updateDocConfig: (payload, callback) => dispatch(DocConfigActions.updateDocConfig(payload, callback)),
});

EntityDocumentTypeConfiguration.propTypes = {
  user: PropTypes.any,
  allowedFields: PropTypes.any,
  entityName: PropTypes.any,
  docType: PropTypes.any,
  updateDocConfig: PropTypes.func,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(EntityDocumentTypeConfiguration));
