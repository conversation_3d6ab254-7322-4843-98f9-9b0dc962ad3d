/* eslint-disable no-param-reassign */
import React, { Component, Fragment } from 'react';
import { Link, withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { Table, Popconfirm } from 'antd';
import {
  CaretRightFilled,
  CloseOutlined,
} from '@ant-design/icons';
import PropTypes from 'prop-types';
import { v4 as uuidv4 } from 'uuid';
import { QUANTITY } from '@Apis/constants';
import H3FormInput from '@Uilib/h3FormInput';
import H3Text from '@Uilib/h3Text';
import ProductFilterV2 from '@Components/Common/ProductFilterV2';
import ProductCategoryLabel from '@Components/Common/ProductCategoryLabel';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCirclePlus } from '@fortawesome/free-solid-svg-icons';
import PRZButton from '../../../../../Common/UI/PRZButton';
import ProductCodesAndName from '@Components/Common/ProductCodesAndName';
import './style.scss';

/**
 *
 */
class ByProduct extends Component {
  /**
   *
   * @param props
   */
  constructor(props) {
    super(props);
    this.state = {
      productListColumns: [
        {
          title: 'Product',
          width: '350px',
          render: (text, record) => {
            const {
              handleProductChangeValue, handleProductChange, selectedTenant, formSubmitted, excludedProducts, data, tenantDepartmentId, handleMultiProductChange, loading, enableAdvanceSearch, getExcludedProduct, user,
            } = this.props;
            const enableInternalSKUCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_sku_code;
            const enableInternalRefCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_ref_code;
            return (
              <div className="mo-bp-table__product-name">
                {
                  record?.bomByProductId || record?.by_product_id ? (
                    <Fragment>
                      <div onClick={() => window.open(`/inventory/product/view/${record?.tenant_product_info?.product_sku_id}`, '_blank')}>
                        <ProductCodesAndName
                          skuCode={record?.tenant_product_info?.internal_sku_code}
                          refCode={record?.tenant_product_info?.ref_product_code}
                          name={record?.productSkuName}
                          showSku={enableInternalSKUCode}
                          showRefCode={enableInternalRefCode}
                        />
                      </div>
                    </Fragment>
                  ) : (
                    <Fragment>
                      <ProductFilterV2
                        record={record}
                        productTypes={['STORABLE', 'SERVICE']}
                        handleProductChangeValue={handleProductChangeValue}
                        handleProductChange={(tenantSku, key, productData) => handleProductChange(tenantSku, key, productData, record)}
                        required
                        showError={formSubmitted && !record.productSkuName}
                        onClear={(key) => {
                          const { updateData } = this.props;
                          const copyData = JSON.parse(JSON.stringify(data));
                          const newData = copyData.map((item) => {
                            if (item.key === key) {
                              return {
                                key: uuidv4(),
                                asset1: '',
                                productSkuName: '',
                                quantity: '',
                                unitPrice: '',
                                taxId: '',
                                lot: '',
                              };
                            }
                            return {
                              ...item,
                            };
                          });
                          updateData(newData);
                        }}
                        excludedProducts={getExcludedProduct(record?.fg_tenant_product_id)}
                        tenantDepartmentId={tenantDepartmentId}
                        selectedTenant={selectedTenant}
                        enableAdvanceSearch={enableAdvanceSearch}
                        handleMultiProductChange={(tenantSku, key, productData, callback) => handleMultiProductChange(tenantSku, key, productData, callback, record)}
                        loading={loading}
                      />
                    </Fragment>
                  )
                }
                {record?.product_category_info?.category_path?.length > 0 && (
                  <ProductCategoryLabel
                    categoryPath={record?.product_category_info?.category_path}
                    categoryName={record?.product_category_info?.category_path?.at(-1)}
                    containerStyle={{
                      width: 'fit-content',
                    }}
                  />
                )}
              </div>
            );
          },
        },
        {
          title: 'Estimated Production',
          width: '180px',
          render: (text, record) => {
            const {
              createMOLoading, updateMOLoading, formSubmitted, handleQuantityChange, multipleFG,
            } = this.props;
            return (
              <div className="mo-fg-table__quantity">
                <H3FormInput
                  name="a valid quantity"
                  type="number"
                  containerClassName={formSubmitted && (!Number(record?.estimatedUsage) || Number(record?.estimatedUsage) <= 0) ? 'form-error__input' : 'orgInputContainer mo-form__input-row__input'}
                  labelClassName="orgFormLabel"
                  inputClassName="orgFormInput input"
                  placeholder=""
                  onChange={(e) => {
                    handleQuantityChange(record?.fg_tenant_product_id, e.target.value, record?.key);
                  }}
                  value={record?.estimatedUsage}
                  disabled={createMOLoading || updateMOLoading}
                  loading={createMOLoading || updateMOLoading}
                />
                <H3Text text={record?.tenant_product_info?.uom_info?.uqc?.toProperCase()} className="mo-fg-table__quantity-unit" />
              </div>
            );
          },
        },
        {
          title: ' ',
          width: '40px',
          render: (text, record) => {
            const { handleDelete } = this.props;
            return (
              <Fragment>
                <Popconfirm
                  title="Confirm remove By Products?"
                  onConfirm={() => handleDelete(record?.product_sku_id, record.key)}
                  onCancel={() => { }}
                  okText="Yes"
                  cancelText="No"
                >
                  <div className="create-mo-bp__delete-line-button">
                    <CloseOutlined />
                  </div>
                </Popconfirm>
              </Fragment>
            );
          },
        },
      ],
    };
  }

  /**
   *
   */
  componentDidMount() {
  }

  /**
   *
   * @param {*} data
   * @return
   */
  getDataSource(data) {
    if (data) {
      return JSON.parse(JSON.stringify(data));
    }
    return [];
  }

  /**
   *
   * @return {JSX.Element}
   */
  render() {
    const {
      data, loading, isFlexible, finishedGoods, addNewRowByProduct, bomByProductsArray,
    } = this.props;
    const {
      productListColumns,
    } = this.state;

    const disableAddNewByProductButton = !!bomByProductsArray?.find((item) => !(item?.product_sku_id || item?.tenant_product_info?.tenant_product_id));

    return (
      <Fragment>
        {finishedGoods?.map((fg) => (!!fg?.tenant_product_info?.tenant_product_id
          && (
            <div className="mo-bp__wrapper" key={fg.key || uuidv4()}>
              <div className="table__fg">
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <span style={{
                    display: 'inline-block', fontSize: '13px', fontWeight: 500, marginLeft: '10px',
                  }}
                  >
                    {fg?.tenant_product_info?.alias_name}
                    {' '}
                    <CaretRightFilled />
                  </span>
                  <PRZButton
                    className={disableAddNewByProductButton ? 'disable-add-by-product-button' : ''}
                    buttonStyle={{ borderRadius: '4px', width: 'fit-content' }}
                    type="ghost"
                    size="small"
                    onClick={() => {
                      addNewRowByProduct({ parentData: fg });
                    }}
                    disabled={disableAddNewByProductButton}
                  >
                    <FontAwesomeIcon icon={faCirclePlus} color="#74C0FC" />
                    &nbsp;
                    <span style={{ fontSize: '11px' }}>Add By Product</span>
                  </PRZButton>
                </div>
              </div>
              {data[fg?.tenant_product_info?.tenant_product_id]?.length > 0 && (
                <div className="bp-table-wrapper">
                  <Table
                    bordered
                    loading={loading}
                    showHeader
                    size="small"
                    columns={isFlexible ? productListColumns : productListColumns?.filter((item) => item.title)}
                    dataSource={data[fg?.tenant_product_info?.tenant_product_id]}
                    pagination={false}
                  />
                </div>
              )}
            </div>
          )
        ))}

      </Fragment>
    );
  }
}

/**
 *
 * @param UserReducers
 * @param PurchaseOrderReducers
 * @return {{updateCustomPurchaseOrderLoading: *, selectedPurchaseOrder, user}}
 */
const mapStateToProps = ({
  UserReducers, PurchaseOrderReducers, OfferReducers,
}) => ({
  user: UserReducers.user,
  updateCustomPurchaseOrderLoading: PurchaseOrderReducers.updateCustomPurchaseOrderLoading,
  selectedPurchaseOrder: PurchaseOrderReducers.selectedPurchaseOrder,
  getOfferByTenantSkuLoading: OfferReducers.getOfferByTenantSkuLoading,
});

/**
 *
 * @return {{}}
 */
const mapDispatchToProps = () => ({

});

ByProduct.propTypes = {
  loading: PropTypes.bool,
  handleProductChangeValue: PropTypes.func,
  handleProductChange: PropTypes.func,
  handleDelete: PropTypes.func,
  updateData: PropTypes.func,
  data: PropTypes.any,
  formSubmitted: PropTypes.bool,
  addNewRow: PropTypes.func,
  isFlexible: PropTypes.bool,
  createMOLoading: PropTypes.bool,
  updateMOLoading: PropTypes.bool,
  multipleFG: PropTypes.bool,
  handleQuantityChange: PropTypes.func,
};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(ByProduct));
