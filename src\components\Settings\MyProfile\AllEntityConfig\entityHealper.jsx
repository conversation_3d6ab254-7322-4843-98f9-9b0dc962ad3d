import React from 'react';
import DocumentSequenceV2 from '../DocumentSequenceV2';
import EntityTab from '../DocumentConfiguration/EntityTab';
import TnCConfig from './TnCConfig';

// Flattened configuration array with flags directly
const documentConfigs = [
  { label: 'Stock Adjustment', key: 'INVENTORY_ADJUSTMENT', secondaryEntityName: 'INVENTORY_ADJUSTMENT', documentSequenceEnable: true, entityTabEnable: false },
  { label: 'Stock Transfer', key: 'STOCK_TRANSFER', secondaryEntityName: 'STOCK_TRANSFER', documentSequenceEnable: true, entityTabEnable: true },
  { label: 'Delivery Challan', key: 'DELIVERY_CHALLAN', secondaryEntityName: 'DELIVERY_CHALLAN', documentSequenceEnable: true, entityTabEnable: true },
  { label: 'Gate Document', key: 'GATE_DOCUMENT', secondaryEntityName: 'GATE_DOCUMENT', documentSequenceEnable: true, entityTabEnable: false },
  { label: 'Material Request', key: 'MATERIAL_REQUEST', secondaryEntityName: 'PURCHASE_REQUEST', documentSequenceEnable: true, entityTabEnable: true },
  { label: 'Purchase Indent', key: 'PURCHASE_INDENT', secondaryEntityName: 'PURCHASE_INDENT', documentSequenceEnable: true, entityTabEnable: true },
  { label: 'Request For Quotation', key: 'REQUEST_FOR_QUOTATION', secondaryEntityName: 'REQUEST_FOR_QUOTATION', documentSequenceEnable: true, entityTabEnable: false },
  { label: 'Purchase Order', key: 'PURCHASE_ORDER', secondaryEntityName: 'PURCHASE_ORDER', documentSequenceEnable: true, entityTabEnable: true, termsAndConditionsEnable: true },
  { label: 'Goods Receiving Note', key: 'GOOD_RECEIVING_NOTE', secondaryEntityName: 'GOOD_RECEIVING_NOTES', documentSequenceEnable: true, entityTabEnable: true },
  { label: 'Account Payable Invoice', key: 'ACCOUNT_PAYABLE_INVOICE', secondaryEntityName: 'ACCOUNT_PAYABLE_INVOICE', documentSequenceEnable: true, entityTabEnable: true },
  { label: 'Debit Note', key: 'DEBIT_NOTE', secondaryEntityName: 'DEBIT_NOTE', documentSequenceEnable: true, entityTabEnable: true },
  { label: 'Expense', key: 'EXPENSE', secondaryEntityName: 'EXPENSE', documentSequenceEnable: true, entityTabEnable: false },
  { label: 'Bill of Material', key: 'BILL_OF_MATERIAL', secondaryEntityName: 'BILL_OF_MATERIAL', documentSequenceEnable: false, entityTabEnable: true },
  { label: 'Manufacturing Order', key: 'MANUFACTURING_ORDER', secondaryEntityName: 'MANUFACTURING_ORDER', documentSequenceEnable: true, entityTabEnable: true },
  { label: 'Manual Production Entry', key: 'MANUAL_PRODUCTION_ENTRY', secondaryEntityName: 'MANUAL_PRODUCTION_ENTRY', documentSequenceEnable: true, entityTabEnable: true },
  { label: 'Job Work', key: 'JOB_WORK', secondaryEntityName: 'JOB_WORK', documentSequenceEnable: true, entityTabEnable: true },
  { label: 'MRP', key: 'MATERIAL_REQUIREMENT_PLANNING', secondaryEntityName: 'MATERIAL_REQUIREMENT_PLANNING', documentSequenceEnable: true, entityTabEnable: false },
  { label: 'Quality Checks', key: 'QUALITY_CHECKS', secondaryEntityName: 'QUALITY_CHECKS', documentSequenceEnable: false, entityTabEnable: true },
  { label: 'Quality Rules', key: 'QUALITY_CONTROL_RULE', secondaryEntityName: 'QUALITY_CONTROL_RULE', documentSequenceEnable: true, entityTabEnable: false },
  { label: 'Sales Estimate', key: 'SALES_ESTIMATE', secondaryEntityName: 'SALES_ESTIMATE', documentSequenceEnable: true, entityTabEnable: true },
  { label: 'Sales Order', key: 'SALES_ORDER', secondaryEntityName: 'SALES_ORDER', documentSequenceEnable: true, entityTabEnable: true, termsAndConditionsEnable: true },
  { label: 'Packing Slip', key: 'PACKING_SLIP', secondaryEntityName: 'PACKING_SLIP', documentSequenceEnable: true, entityTabEnable: true },
  { label: 'Invoice', key: 'INVOICE', secondaryEntityName: 'INVOICE', documentSequenceEnable: true, entityTabEnable: true, termsAndConditionsEnable: true },
  { label: 'Annual Maintenance Contract', key: 'ANNUAL_MAINTENANCE_CONTRACT', secondaryEntityName: 'ANNUAL_MAINTENANCE_CONTRACT', documentSequenceEnable: true, entityTabEnable: true },
  { label: 'Consumption Order', key: 'CONSUMPTION_ORDER', secondaryEntityName: 'CONSUMPTION_ORDER', documentSequenceEnable: true, entityTabEnable: true },
  { label: 'Credit Note', key: 'CREDIT_NOTE', secondaryEntityName: 'CREDIT_NOTE', documentSequenceEnable: true, entityTabEnable: true },
  { label: 'Return Slip', key: 'RETURN_SLIP', secondaryEntityName: 'RETURN_SLIP', documentSequenceEnable: true, entityTabEnable: true },
];

// Get menu items with UI name injection
export const getDocumentMenuItems = (documentUINames) => {
  return documentConfigs.map((item) => {
    if (item.key === 'SALES_ESTIMATE') {
      return { ...item, label: documentUINames.estimateUIName };
    }
    if (item.key === 'SALES_ORDER') {
      return { ...item, label: documentUINames.salesOrderUIName };
    }
    return item;
  });
};

// Generate tab items
export const getTabItems = (currentDocTab, items) => {
  const currentItem = items?.find((item) => item.key === currentDocTab);
  if (!currentItem) return [];

  const tabItems = [];

  if (currentItem.documentSequenceEnable) {
    tabItems.push({
      tabLabel: 'Document Sequence',
      key: 'sequence',
      component: (
        <div className="document-config-menu-items">
          <DocumentSequenceV2
            key={currentItem.key}
            label={currentItem.label}
            entityName={currentItem.key}
          />
        </div>
      ),
    });
  }

  if (currentItem.entityTabEnable) {
    tabItems.push({
      tabLabel: 'Document Configuration',
      key: 'config',
      component: (
        <div className="document-config-menu-items">
          <EntityTab
            entityName={currentItem.secondaryEntityName}
            key={currentItem.key}
          />
        </div>
      ),
    });
  }

  if (currentItem.termsAndConditionsEnable) {
    tabItems.push({
      tabLabel: 'Terms and Conditions',
      key: 'tnc',
      component: (
        <div className="document-config-menu-items">
          <TnCConfig
            key={currentItem.key}
            label={currentItem.label}
            entityName={currentItem.key}
          />
        </div>
      ),
    });
  }

  return tabItems;
};