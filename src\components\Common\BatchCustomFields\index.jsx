import React, { Fragment, useEffect, useState } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import H3CustomInput from '@Uilib/h3CustomInput';
import './style.scss';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faChevronDown, faChevronUp } from '@fortawesome/free-solid-svg-icons';

const BatchCustomFields = ({
  customFields, formSubmitted, customInputChange, disableCase, match, isCarryForward, isUpdate, wrapperClassName, isForGRNLine
}) => {

  const [showAll, setShowAll] = useState(false);
  const [renderedCustomFields, setRenderedCustomFields] = useState();

  const isDocumentInUpdateState = match.url.includes('/update') || (isUpdate === true);
  const isDocumentIsCarryForwarding = isCarryForward;
  // Helper function to determine if the input should be disabled
  const shouldDisableField = (customField) => (
    disableCase
    || (isDocumentInUpdateState && customField.isEditable === false)
    || (isDocumentIsCarryForwarding && customField.isCarryForward === false)
  );

  useEffect(() => {

    if (customFields) {

      const mandatoryCustomFields = customFields?.filter(item => item?.isRequired);
      const nonMandatoryCustomFields = customFields?.filter(item => !item?.isRequired);

      if (showAll) {
        setRenderedCustomFields([...mandatoryCustomFields, ...nonMandatoryCustomFields]);
      } else {
        setRenderedCustomFields([...mandatoryCustomFields, ...nonMandatoryCustomFields]?.slice(0,3));
      }
    }
  }, [showAll, customFields]);

  return (
    <div
      style={{
        width: '270px',
      }}
      className="create-batch__cf-wrapper"
    >
      {renderedCustomFields?.filter((item) => (item.visible && item.isActive && item?.fieldType?.toUpperCase() !== 'ATTACHMENT' && item?.fieldType?.toUpperCase() !== 'CHECKBOX' && item?.fieldType?.toUpperCase() !== 'TEXTAREA'))?.map((customField, index) => (
        <React.Fragment key={customField?.cfId}>
          <div key={customField?.cfId} className={wrapperClassName}>
            <H3CustomInput
              containerClassName="create-batch-cf__input-wrapper"
              customClassName="pr_input_custom_field"
              labelClassName="create-batch-cf__input-label"
              inputClassName={`orgFormInput  custom-field-input ${(formSubmitted && customField?.isRequired && (Number(customField?.fieldValue) <= 0 || customField?.fieldValue === '')) ? 'orgFormInputError' : ''}`}
              inputErrorClassName="cf__input-error"
              type={customField?.fieldType?.toUpperCase()}
              required={customField?.isRequired}
              label={customField?.fieldName}
              options={customField?.defaultData?.length ? customField?.defaultData : []}
              placeholder={customField?.placeholder}
              onChange={(value) => customInputChange(value, customField?.cfId)}
              value={customField?.fieldValue}
              // showError={formSubmitted && customField?.isRequired && !customField?.fieldValue}
              disabled={shouldDisableField(customField)}
              showErrorCheckBox={(formSubmitted && customField?.isRequired && (Number(customField?.fieldValue) <= 0 || customField?.fieldValue === ''))}
            />
          </div>
        </React.Fragment>
      ))}

      {renderedCustomFields?.filter((item) => (item.visible && item.isActive && item?.fieldType?.toUpperCase() === 'ATTACHMENT' || item?.fieldType?.toUpperCase() === 'CHECKBOX' || item?.fieldType?.toUpperCase() === 'TEXTAREA'))?.map((customField, index) => (
        <React.Fragment key={customField?.cfId}>
          <div key={customField?.cfId} className={wrapperClassName}>
            <H3CustomInput
              containerClassName="create-batch-cf__input-wrapper"
              customClassName="pr_input_custom_field"
              labelClassName="create-batch-cf__input-label"
              inputClassName={`orgFormInput  custom-field-input ${(formSubmitted && customField?.isRequired && (Number(customField?.fieldValue) <= 0 || customField?.fieldValue === '')) ? 'orgFormInputError' : ''}`}
              inputErrorClassName="cf__input-error"
              type={customField?.fieldType?.toUpperCase()}
              required={customField?.isRequired}
              label={customField?.fieldName}
              options={customField?.defaultData?.length ? customField?.defaultData : []}
              placeholder={customField?.placeholder}
              onChange={(value) => customInputChange(value, customField?.cfId)}
              value={customField?.fieldValue}
              // showError={formSubmitted && customField?.isRequired && !customField?.fieldValue}
              disabled={shouldDisableField(customField)}
              showErrorCheckBox={(formSubmitted && customField?.isRequired && (Number(customField?.fieldValue) <= 0 || customField?.fieldValue === ''))}
              islineFields
              isForGRNLine={isForGRNLine}
            />
          </div>
        </React.Fragment>
      ))}

      {customFields?.filter(item => item?.visible && item?.isActive)?.length > 3 && (
        <div onClick={() => setShowAll(!showAll)} style={{ cursor: 'pointer', }}>
          {showAll ? (
            <div>
              show less <FontAwesomeIcon icon={faChevronUp} />
            </div>
          ) : (
            <div>
              show more <FontAwesomeIcon icon={faChevronDown} />
            </div>
          )}
        </div>
      )}

    </div>
  );
};
const mapStateToProps = () => ({

});

const mapDispatchToProps = (dispatch) => ({
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(BatchCustomFields));
