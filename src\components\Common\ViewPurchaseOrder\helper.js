/* eslint-disable unicorn/no-array-for-each */
// Core Libraries
import React, { Fragment } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Link } from 'react-router-dom/cjs/react-router-dom.min';

// Ant Design
import { <PERSON>u, Tooltip } from 'antd';
import SubMenu from 'antd/es/menu/SubMenu';

// UI Components
import H3Text from '../../../uilib/h3Text';
import H3Progress from '@Components/Common/H3Progress';
import RecentTransactions from '@Components/Inventory/ManageProducts/ViewProduct/RecentTransactions';
import HideValue from '../RestrictedAccess/HideValue';
import ProductCategoryLabel from '../ProductCategoryLabel';

// Helpers / Utilities
import Helpers from '@Apis/helpers';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';

// Constants
import Constants, {
  toISTDate,
  DEFAULT_CUR_ROUND_OFF,
  QUANTITY,
  entityNameEnum,
} from '@Apis/constants';
import ProductCodesAndName from '../ProductCodesAndName';

export function isDataValid(poExpiryDate) {
  if (!poExpiryDate) return false;
  return isDataValid;
}

export function updatePOExpiryDate({
  updatePurchaseOrderExpiryDate,
  getPurchaseOrderById,
  user,
  poExpiryDate,
  dispatch,
}) {
  dispatch({ type: 'SET_FORM_SUBMITTED', payload: true });
  if (isDataValid(poExpiryDate)) {
    const payload = {
      po_id: window.location.search?.split('id=')[1],
      po_expiry_date: toISTDate(poExpiryDate),
    };
    updatePurchaseOrderExpiryDate(payload, () => {
      dispatch({ type: 'TOGGLE_DATE_MODAL', payload: false });
      getPurchaseOrderById(
        window.location.search?.split('id=')[1],
        Helpers.getTenantEntityPermission(
          user?.user_tenants,
          Helpers.permissionEntities.PURCHASE_ORDER,
          Helpers.permissionTypes.READ
        ).join(',')
      );
    });
  }
}

export function handleBlanketPoFields({ value, selectedPurchaseOrder }) {
  const newData = {
    ...value,
    UNIT_PRICE: {
      label: 'Unit Price',
      visible: !selectedPurchaseOrder?.purchase_order_without_costing,
      disabled: true,
    },
    TAX: {
      label: 'TAX',
      visible: !selectedPurchaseOrder?.purchase_order_without_costing,
      disabled: true,
    },
    DISCOUNT: {
      label: 'Discount',
      visible: !selectedPurchaseOrder?.purchase_order_without_costing,
      disabled: true,
    },
    LINE_TOTAL: {
      label: 'Line Total',
      visible: !selectedPurchaseOrder?.purchase_order_without_costing,
      disabled: true,
    },
  };
  return newData;
}

export function renderFreight({ position, selectedPurchaseOrder, MONEY }) {
  if (selectedPurchaseOrder?.charge_1_value > 0) {
    if (position === 'top' && selectedPurchaseOrder?.freight_tax_id) {
      return (
        <div className="view-document__totals-field">
          <H3Text
            text={
              <Fragment>
                {selectedPurchaseOrder?.charge_1_name}
                &nbsp;
                <span className="table-subscript">{`tax@${selectedPurchaseOrder?.freight_tax_info?.tax_value}%`}</span>
              </Fragment>
            }
            className="view-document__totals-field-name"
          />
          <H3Text
            text={MONEY(
              selectedPurchaseOrder?.charge_1_value || 0,
              selectedPurchaseOrder?.org_currency_info?.currency_code
            )}
            className="view-document__totals-field-value"
          />
        </div>
      );
    }
    if (position === 'bottom' && !selectedPurchaseOrder?.freight_tax_id) {
      return (
        <div className="view-document__totals-field">
          <H3Text
            text={selectedPurchaseOrder?.charge_1_name}
            className="view-document__totals-field-name"
          />
          <H3Text
            text={MONEY(
              selectedPurchaseOrder?.charge_1_value || 0,
              selectedPurchaseOrder?.org_currency_info?.currency_code
            )}
            className="view-document__totals-field-value"
          />
        </div>
      );
    }
  }
  return '';
}

export function getRatingColor(rating) {
  if (rating >= 3) {
    return 'green'; // 3, 4, and 5 are green
  }
  if (rating >= 2) {
    return 'yellow'; // 2 and 3 are yellow
  }
  return 'red'; // 0 and 1 are red
}

export const HideAdhocApprovals = (selectedPurchaseOrder) => {
  if (selectedPurchaseOrder?.workflow_steps) {
    const steps = Object.values(selectedPurchaseOrder?.workflow_steps)?.filter(
      (step) => step !== null
    );
    const pendingSteps = steps?.filter((step) => step?.status === 'PENDING');
    return steps.length < 10 && pendingSteps.length === steps.length;
  }
  return false;
};

export function MenuItems({ selectedPurchaseOrder, downloadDocument }) {
  const menu = (
    <Menu>
      {selectedPurchaseOrder?.org_currency_info?.org_currency_id && (
        <SubMenu title={`Purchase Order (${selectedPurchaseOrder?.org_currency_info?.currency_code})`}>
          <Menu.Item
            key="purchase-order-pdf"
            onClick={() =>
              downloadDocument({
                url: `${Constants.PURCHASE_ORDERS}/download?po_id=${selectedPurchaseOrder?.po_id}&tenant_id=${selectedPurchaseOrder?.tenant_id}&download_document_in_base_currency=false`,
                document_type: 'PURCHASE_ORDER',
                document_number: selectedPurchaseOrder?.po_number,
                key: uuidv4(),
              })
            }
          >
            Download PDF
          </Menu.Item>
          <Menu.Item
            key="purchase-order-excel"
            onClick={() =>
              downloadDocument({
                url: `${Constants.PURCHASE_ORDERS}/download/excel?po_id=${selectedPurchaseOrder?.po_id}&tenant_id=${selectedPurchaseOrder?.tenant_id}&download_document_in_base_currency=false`,
                document_type: 'PURCHASE_ORDER',
                document_number: selectedPurchaseOrder?.po_number,
                key: uuidv4(),
              })
            }
          >
            Download Excel
          </Menu.Item>
        </SubMenu>
      )}
      {selectedPurchaseOrder?.base_currency_info?.org_currency_id &&
        selectedPurchaseOrder?.org_currency_info?.org_currency_id !==
        selectedPurchaseOrder?.base_currency_info?.org_currency_id && (
        <SubMenu title={`Purchase Order (${selectedPurchaseOrder?.base_currency_info?.currency_code})`}>
          <Menu.Item
            key="purchase-order-base-pdf"
            onClick={() =>
              downloadDocument({
                url: `${Constants.PURCHASE_ORDERS}/download?po_id=${selectedPurchaseOrder?.po_id}&tenant_id=${selectedPurchaseOrder?.tenant_id}&download_document_in_base_currency=true`,
                document_type: 'PURCHASE_ORDER',
                document_number: selectedPurchaseOrder?.po_number,
                key: uuidv4(),
              })
            }
          >
              Download PDF
          </Menu.Item>
          <Menu.Item
            key="purchase-order-base-excel"
            onClick={() =>
              downloadDocument({
                url: `${Constants.PURCHASE_ORDERS}/download/excel?po_id=${selectedPurchaseOrder?.po_id}&tenant_id=${selectedPurchaseOrder?.tenant_id}&download_document_in_base_currency=true`,
                document_type: 'PURCHASE_ORDER',
                document_number: selectedPurchaseOrder?.po_number,
                key: uuidv4(),
              })
            }
          >
              Download Excel
          </Menu.Item>
        </SubMenu>
      )}
    </Menu>
  );
  return menu;
}

export function splitChargesData(charge) {
  const chargeWithTaxName =
    charge?.filter((line) => line?.tax_info?.tax_id) || [];
  const chargeWithoutTaxName =
    charge?.filter((line) => !line?.tax_info?.tax_id) || [];
  return { chargeWithTaxName, chargeWithoutTaxName };
}

export function renderCharges({
  charge,
  isVendorOverseas,
  selectedPurchaseOrder,
  MONEY,
}) {
  return (
    !!charge?.length &&
    charge?.map((otherCharge, i) => (
      <div key={i} className="view-document__totals-field">
        <H3Text
          text={
            <Fragment>
              {otherCharge?.charge_name?.toProperCase()}&nbsp;
              {otherCharge?.tax_info?.tax_value && !isVendorOverseas ? (
                <span className="table-subscript">{`tax@${otherCharge.tax_info.tax_value}%`}</span>
              ) : null}
            </Fragment>
          }
          className="view-document__totals-field-name"
        />
        {otherCharge?.charge_amount < 0 ? (
          <H3Text
            text={`(-) ${MONEY(Math.abs(otherCharge?.charge_amount))}`}
            className="view-document__totals-field-value danger-text"
          />
        ) : (
          <H3Text
            text={MONEY(
              otherCharge?.charge_amount,
              selectedPurchaseOrder?.org_currency_info?.currency_code
            )}
            className="view-document__totals-field-value"
          />
        )}
      </div>
    ))
  );
}

export function handleGRNModalViewOpen({ grn, isOpen, dispatch }) {
  dispatch({
    type: 'SET_GRN_DATA',
    payload: { selectedGRN: grn, openGRNModalView: isOpen },
  });
}

export function handleViewPayoutDrawer({ payment, isOpen, dispatch }) {
  dispatch({
    type: 'SET_PAYMENT_DATA',
    payload: { selectedPayment: payment, showViewPayout: isOpen },
  });
}

export function getLinkedDocumentInfo({
  selectedPurchaseOrder,
  grnData,
  user,
  isQuickView,
  openGRNModalView,
  isDataMaskingPolicyEnable,
  isHideCostPrice,
  showViewPayout,
  dispatch,
}) {
  let linkedDocumentsInfo = [];
  if (grnData?.grn?.length > 0) {
    let documentDataInfo = [];
    grnData?.grn?.forEach((item) => {
      documentDataInfo.push({
        documentId: item?.grn_id,
        documentNumber: item?.grn_number,
        documentStatus: item?.status,
        documentTotal: item?.grn_total,
        documentDueDate: item?.grn_due_date,
        documentRatingInfo: {
          rating: item?.rating_info?.rating.toFixed(1),
          ratingClassName: `po-grn-rating-container-update ${getRatingColor(
            item?.rating_info?.rating
          )}`,
          showRating:
            user?.tenant_info?.seller_rating_config?.is_active &&
            item?.rating_info?.rating > 0,
        },
      });
    });

    const grnDataInfo = {
      entityName: entityNameEnum.GOOD_RECEIVING_NOTE,
      entityVisible:
        Helpers.getPermission(
          Helpers.permissionEntities.GOOD_RECEIVING,
          Helpers.permissionTypes.READ,
          user
        ) &&
        selectedPurchaseOrder?.status === 'ISSUED' &&
        grnData?.grn?.length > 0,
      title: 'GOODS RECIEVED',
      entityNumberPrefix: 'GRN',
      documentData: documentDataInfo,
      isDrawer: !isQuickView,
      isRedirect: isQuickView,
      redirectPath: '/purchase/goods-receiving/view/',
      drawerState: openGRNModalView,
      drawerCallback: (grn, isOpen) =>
        handleGRNModalViewOpen({ grn, isOpen, dispatch }),
      hidePrice: isDataMaskingPolicyEnable && isHideCostPrice,
      popOverMessage: 'You don\'t have access to view amount',
      showEntityTotal: true,
    };
    linkedDocumentsInfo.push(grnDataInfo);
  }
  if (selectedPurchaseOrder?.sales_orders?.length > 0) {
    let documentDataInfo = [];
    selectedPurchaseOrder?.sales_orders?.forEach((item) => {
      documentDataInfo.push({
        documentId: item?.order_id,
        documentNumber: item?.sales_order_number,
        documentStatus: item?.status,
        documentTotal: item?.order_grand_total,
        documentDueDate: item?.order_due_date,
      });
    });

    const soDataInfo = {
      entityName: entityNameEnum.SALES_ORDER,
      title: 'SALES ORDER',
      entityNumberPrefix: 'SO',
      documentData: documentDataInfo,
      isRedirect: true,
      redirectPath: '/sales/sales-order/view/',
      hidePrice: isDataMaskingPolicyEnable && isHideCostPrice,
      popOverMessage: 'You don\'t have access to view amount',
      entityVisible:
        Helpers.getPermission(
          Helpers.permissionEntities.SALES_ORDER,
          Helpers.permissionTypes.READ,
          user
        ) &&
        selectedPurchaseOrder?.status === 'ISSUED' &&
        selectedPurchaseOrder?.sales_orders?.length > 0,
      showEntityTotal: true,
    };
    linkedDocumentsInfo.push(soDataInfo);
  }
  if (selectedPurchaseOrder?.reference_rfq?.length > 0) {
    let documentDataInfo = [];
    selectedPurchaseOrder?.reference_rfq?.forEach((item) => {
      documentDataInfo.push({
        documentId: item?.rfq_id,
        documentNumber: item?.rfq_number,
        documentStatus: item?.status,
        documentTotal: item?.rfq_total,
      });
    });

    const rfqDataInfo = {
      entityName: entityNameEnum.REQUEST_FOR_QUOTATION,
      title: 'REQUEST FOR QUOTE',
      entityNumberPrefix: 'RFQ',
      documentData: documentDataInfo,
      isRedirect: true,
      redirectPath: '/purchase/rfq/view/',
      hidePrice: isDataMaskingPolicyEnable && isHideCostPrice,
      popOverMessage: 'You don\'t have access to view amount',
      entityVisible:
        Helpers.getPermission(
          Helpers.permissionEntities.REQUEST_FOR_QUOTATION,
          Helpers.permissionTypes.READ,
          user
        ) && selectedPurchaseOrder?.reference_rfq?.length > 0,
      showEntityTotal: false,
    };
    linkedDocumentsInfo.push(rfqDataInfo);
  }
  if (selectedPurchaseOrder?.pr_details?.length > 0) {
    let documentDataInfo = [];
    selectedPurchaseOrder?.pr_details?.forEach((item) => {
      documentDataInfo.push({
        documentId: item?.pr_id,
        documentNumber: item?.pr_number,
        documentStatus: 'ISSUED',
      });
    });

    const prDataInfo = {
      entityName: entityNameEnum.PURCHASE_REQUEST,
      title: 'MATERIAL REQUEST',
      entityNumberPrefix: 'MR',
      documentData: documentDataInfo,
      isRedirect: true,
      redirectPath: '/purchase/purchase-request/view/',
      hidePrice: isDataMaskingPolicyEnable && isHideCostPrice,
      popOverMessage: 'You don\'t have access to view amount',
      entityVisible:
        Helpers.getPermission(
          Helpers.permissionEntities.PURCHASE_REQUEST,
          Helpers.permissionTypes.READ,
          user
        ) && selectedPurchaseOrder?.pr_details?.length > 0,
      showEntityTotal: false,
    };
    linkedDocumentsInfo.push(prDataInfo);
  }

  if (selectedPurchaseOrder?.pi_details?.length > 0) {
    let documentDataInfo = [];
    selectedPurchaseOrder?.pi_details?.forEach((item) => {
      documentDataInfo.push({
        documentId: item?.pi_id,
        documentNumber: item?.pi_number,
        documentStatus: item?.status,
      });
    });

    const piDataInfo = {
      entityName: entityNameEnum.PURCHASE_INDENT,
      title: 'PURCHASE INDENT',
      entityNumberPrefix: 'PI',
      documentData: documentDataInfo,
      isRedirect: true,
      redirectPath: '/purchase/purchase-indent/view/',
      entityVisible:
        Helpers.getPermission(
          Helpers.permissionEntities.PURCHASE_INDENT,
          Helpers.permissionTypes.READ,
          user
        ) && selectedPurchaseOrder?.pi_details?.length > 0,
      showEntityTotal: false,
    };
    linkedDocumentsInfo.push(piDataInfo);
  }

  if (selectedPurchaseOrder?.advance_payments?.length > 0) {
    let documentDataInfo = [];
    selectedPurchaseOrder?.advance_payments?.forEach((item) => {
      documentDataInfo.push({
        ...item,
        documentId: item?.payment_id,
        documentNumber: item?.payment_id,
        documentStatus: item?.approval_status,
        documentTotal: item?.amount,
      });
    });

    const apDataInfo = {
      entityName: entityNameEnum.VENDOR_PAYOUT,
      title: 'ADVANCE PAYMENTS',
      entityNumberPrefix: 'PAYMENT',
      documentData: documentDataInfo,
      isDrawer: true,
      drawerState: showViewPayout,
      drawerCallback: (payment, isOpen) =>
        handleViewPayoutDrawer({ payment, isOpen, dispatch }),
      hidePrice: isDataMaskingPolicyEnable && isHideCostPrice,
      popOverMessage: 'You don\'t have access to view amount',
      entityVisible: !(isDataMaskingPolicyEnable && isHideCostPrice) && selectedPurchaseOrder?.advance_payments?.length > 0,
      showEntityTotal: true,
    };
    linkedDocumentsInfo.push(apDataInfo);
  }

  return linkedDocumentsInfo;
}

export function handleFileChange({
  fileListData,
  selectedPurchaseOrder,
  updateAttachment,
  getAttachmentById,
  dispatch,
}) {
  const fileList = fileListData?.fileList?.map((item) => ({
    ...item,
    url: item?.response?.response?.location || item?.url,
  }));
  const attachments = fileList?.map((attachment) => ({
    url: attachment?.response?.response?.location || attachment?.url,
    type: attachment.type,
    name: attachment.name,
    uid: attachment.uid,
  }));

  if (fileListData?.file.status === 'done') {
    const payload = {
      entity_id: selectedPurchaseOrder?.po_id,
      entity_name: 'purchase_order',
      attachments,
    };
    updateAttachment(payload, () => {
      getAttachmentById(selectedPurchaseOrder?.po_id, 'purchase_order', () => {
        dispatch({ type: 'SET_IS_GET_ATTACHMENT', payload: false });
      });
    });
  }
  if (fileListData?.file.status === 'removed') {
    const payload = {
      entity_id: selectedPurchaseOrder?.po_id,
      entity_name: 'purchase_order',
      attachments,
    };
    updateAttachment(payload, () => {
      getAttachmentById(selectedPurchaseOrder?.po_id, 'purchase_order', () => {
        dispatch({ type: 'SET_IS_GET_ATTACHMENT', payload: false });
      });
    });
  }

  dispatch({
    type: 'SET_ATTACHMENT_LIST',
    payload: attachments?.length > 0 ? attachments : [],
  });
}

export function getColumns({
  priceMasking,
  user,
  sellerInfo,
  visibleColumns,
  MONEY,
  selectedPurchaseOrder,
  poLines,
  cfPurchaseOrdersLine,
  setShowPRZModal,
  setSelectedDocumentId
}) {
  const { isDataMaskingPolicyEnable, isHideCostPrice } =
    priceMasking;

  const isVendorOverseas =
    sellerInfo?.seller_type === 'OVERSEAS' &&
    user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings
      ?.hide_tax_for_overseas;

  const enableInternalRefCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_ref_code;
  const enableInternalSKUCode = user?.tenant_info?.inventory_config?.settings?.enable_internal_sku_code;

  const columns = [
    {
      title: '',
      responsive: ['xs'],
      render: (record) => (
        <Link
          to={`/inventory/product/view/${record?.product_sku_info?.product_sku_id}`}
          target="_blank"
        >
          <div className="mobile-list__item">
            <Link
              to={`/inventory/product/view/${record?.product_sku_info?.product_sku_id}`}
              target="_blank"
            >
              {enableInternalSKUCode && (<H3Text
                text={`SKU: ${record?.product_sku_info?.internal_sku_code}`}
                className="mobile-list__item-number"
              />)}
            </Link>
            <H3Text
              text={`PRODUCT: ${record.product_sku_info?.product_sku_name === 'custom'
                ? ''
                : ` ${record?.product_sku_info?.product_sku_name}`
                }`}
              className="mobile-list__item-info"
            />
            <H3Text
              text={`QTY: ${QUANTITY(
                record?.quantity,
                record?.uom_info?.[0]?.precision
              )} ${record?.uom_info?.[0]?.uqc?.toProperCase()}`}
              className="mobile-list__item-info"
            />
            <H3Text
              text={`UNIT PRICE: ${MONEY(record.offer_price)}`}
              className="mobile-list__item-info"
            />
            {!isVendorOverseas && (
              <H3Text
                text={`TAX: ${record.tax_value}%`}
                className="mobile-list__item-info"
              />
            )}
            <H3Text
              text={`TOTAL: ${MONEY(record.offer_price * record.quantity)}`}
              className="mobile-list__item-info"
            />
            <H3Text
              text={`TOTAL (WITH TAX): ${MONEY(record.total_price || '0')}`}
              className="mobile-list__item-info"
            />
          </div>
        </Link>
      ),
      visible: visibleColumns?.PRODUCT?.visible,
    },
    {
      title: 'Product',
      responsive: ['sm', 'md', 'lg', 'xxl'],
      fixed: 'left',
      render: (record) => {
        return (
          <div>
            <div onClick={() => {
              setShowPRZModal(true);
              setSelectedDocumentId(record?.product_sku_info?.product_sku_id);
            }}>
              <ProductCodesAndName
                skuCode={record?.product_sku_info?.internal_sku_code}
                refCode={record?.product_sku_info?.ref_product_code}
                name={record?.product_sku_info?.product_sku_name}
                showRefCode={enableInternalRefCode}
                showSku={enableInternalSKUCode}
              />
            </div>
            <div
              className="flex-display"
              style={{ display: 'flex', flexDirection: 'column' }}
            >
              <div>
                <div
                  dangerouslySetInnerHTML={{ __html: record?.remark }}
                  className="table-subscript"
                  style={{
                    width: '200xp',
                    whiteSpace: 'pre-wrap',
                  }}
                />
              </div>
              <div>
                {record?.pi_number ? (
                  <div>
                    <Link to={`/purchase/purchase-indent/view/${record?.pi_id}`} target="_blank">
                      {record?.pi_number}
                    </Link>
                  </div>
                ) : ''}
              </div>

              <div
                className="flex-align-center-justify-between"
                style={{
                  gap: '10px',
                }}
              >
                {record?.product_sku_info?.product_category_info?.category_path
                  ?.length > 0 && (
                  <ProductCategoryLabel
                    categoryPath={
                      record?.product_sku_info?.product_category_info
                        ?.category_path
                    }
                    categoryName={record?.product_sku_info?.product_category_info?.category_path?.at(
                      -1
                    )}
                    containerStyle={{
                      width: 'fit-content',
                    }}
                  />
                )}
                {Helpers.getPermission(
                  Helpers.permissionEntities.PURCHASE_ORDER,
                  Helpers.permissionTypes.CREATE,
                  user
                ) && (
                  <RecentTransactions
                    sellerId={selectedPurchaseOrder?.seller_info?.seller_id}
                    tenantId={selectedPurchaseOrder?.tenant_id}
                    productSkuId={record?.product_sku_info?.product_sku_id}
                    internalSkuCode={
                      record?.product_sku_info?.internal_sku_code
                    }
                    refProductCode={record?.product_sku_info?.ref_product_code}
                    productSkuName={record?.product_sku_name}
                    TransactionType="PURCHASE_ORDER"
                  />
                )}
              </div>
            </div>
          </div>
        );
      },
      width: '250px',
      visible: visibleColumns?.PRODUCT?.visible,
    },
    {
      title: 'Ordered Qty',
      responsive: ['sm', 'md', 'lg', 'xxl'],
      render: (item) => (
        <Fragment>
          {`${QUANTITY(
            item?.quantity + item?.short_close_quantity,
            item?.uom_info?.[0]?.precision
          )} ${item?.uom_info?.[0]?.uqc?.toProperCase()}`}
          <H3Progress
            percent={
              (item?.total_received_qty /
                (item?.quantity + item?.short_close_quantity)) *
              100
            }
            barWidth="80px"
          />
          <H3Text
            text={`${item?.total_received_qty} of ${item?.quantity + item?.short_close_quantity
              } received`}
            className="table-subscript-grey"
          />
          {item?.secondary_uom_qty > 0 &&
            user?.tenant_info?.global_config?.settings
              ?.enable_secondary_uom && (
            <div>
              {`${QUANTITY(
                item?.secondary_uom_qty,
                item?.product_sku_info?.secondary_uom_info?.precision || 0
              )} ${item?.product_sku_info?.secondary_uom_info?.uqc?.toProperCase() ||
                  'N/A'
                  } (Secondary)`}
            </div>
          )}
        </Fragment>
      ),
      visible: visibleColumns?.ORDERED_QTY?.visible,
    },
    {
      title: 'Pending',
      responsive: ['sm', 'md', 'lg', 'xxl'],
      render: (item) => {
        const pendingQty = item?.pending_quantity || 0;
        return (
          <div
            style={
              selectedPurchaseOrder?.closing_status === 'CLOSED'
                ? { textDecoration: 'line-through' }
                : {}
            }
          >
            {`${QUANTITY(
              pendingQty + item?.short_close_quantity,
              item?.uom_info?.[0]?.precision
            )} ${item?.uom_info?.[0]?.uqc?.toProperCase()}`}
          </div>
        );
      },
      visible: visibleColumns?.PENDING?.visible,
    },
    {
      title: 'Short Closed',
      responsive: ['sm', 'md', 'lg', 'xxl'],
      render: (item) =>
        `${QUANTITY(
          item?.short_close_quantity,
          item?.uom_info?.[0]?.precision
        )} ${item?.uom_info?.[0]?.uqc?.toProperCase()}`,
      className:
        selectedPurchaseOrder?.closing_status === 'CLOSED'
          ? ''
          : 'display-none',
      visible: true,
    },
    {
      title: 'Rate',
      responsive: ['sm', 'md', 'lg', 'xxl'],
      render: (record) =>
        isDataMaskingPolicyEnable && isHideCostPrice ? (
          <HideValue
            showPopOver
            popOverMessage={'You don\'t have access to view rate'}
          />
        ) : (
          <Fragment>
            {MONEY(
              record.offer_price,
              selectedPurchaseOrder?.org_currency_info?.currency_code
            )}
            {!isVendorOverseas && (
              <H3Text
                text={`+ tax@${record.tax_value}%`}
                className="danger-subtext"
              />
            )}
          </Fragment>
        ),
      visible: visibleColumns?.UNIT_PRICE?.visible,
    },
    {
      title: 'Discount',
      responsive: ['sm', 'md', 'lg', 'xxl'],
      render: (record) => (
        <Fragment>
          {Number(record?.line_discount_amount) > 0 ? (
            <Fragment>
              {record?.is_discount_in_percent
                ? `${Number(record?.line_discount_percentage).toPrecision(
                  DEFAULT_CUR_ROUND_OFF
                ) || '0'
                }%`
                : MONEY(
                  record?.line_discount_amount,
                  selectedPurchaseOrder?.org_currency_info?.currency_code
                )}
            </Fragment>
          ) : (
            '-'
          )}
        </Fragment>
      ),
      className: selectedPurchaseOrder?.is_line_wise_discount
        ? ''
        : 'display-none',
      visible: visibleColumns?.DISCOUNT?.visible,
    },
    {
      title: "Delivery Date",
      responsive: ["sm", "md", "lg", "xxl"],
      render: (record) => (
        <Fragment>
          {record?.delivery_date
            ? toISTDate(record?.delivery_date).format("DD/MM/YYYY")
            : "-"}
        </Fragment>
      ),
      visible: visibleColumns?.DELIVERY_DATE?.visible,
    },
    {
      title: "Total",
      responsive: ["sm", "md", "lg", "xxl"],
      render: (record) =>
        isDataMaskingPolicyEnable && isHideCostPrice ? (
          <HideValue
            showPopOver
            popOverMessage={'You don\'t have access to view total'}
          />
        ) : (
          MONEY(
            record.total_price || '0',
            selectedPurchaseOrder?.org_currency_info?.currency_code
          )
        ),
      visible: visibleColumns?.LINE_TOTAL?.visible,
    },
  ];

  const hasSplitStatus = poLines?.some(
    (item) => item?.po_line_status?.length > 0
  );

  if (hasSplitStatus) {
    columns.splice(4, 0, {
      title: 'Split Status',
      responsive: ['sm', 'md', 'lg', 'xxl'],
      render: (item) => {
        const statuses = item?.po_line_status || [];
        const uom = item?.uom_info?.[0]?.uqc?.toProperCase?.() || '';
        return (
          <div className="split-status-container">
            {statuses?.map((status, index) => {
              const statusName = status?.status_name?.toProperCase?.() || '';
              return (
                <div key={index} className="split-status-row">
                  <Tooltip title={statusName}>
                    <span className="split-status-name">{statusName}</span>
                  </Tooltip>
                  <span className="split-status-qty">
                    {status?.quantity} {uom}
                  </span>
                </div>
              );
            })}
          </div>
        );
      },
      visible: true,
    });
  }

  if (cfPurchaseOrdersLine?.length) {
    columns.splice(
      5,
      0,
      ...CustomFieldHelpers.renderCustomLineColumns(
        false,
        cfPurchaseOrdersLine,
        visibleColumns,
        null
      )
    );
  }
  return columns?.filter((item) => item.visible);
}
